var e=(e,a,i)=>new Promise((r,s)=>{var o=e=>{try{l(i.next(e))}catch(a){s(a)}},n=e=>{try{l(i.throw(e))}catch(a){s(a)}},l=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,n);l((i=i.apply(e,a)).next())});import{j as a,s as i}from"./auth-Cw5QfmsP.js";import{r,L as s}from"./react-vendor-C9XH6RF0.js";import{B as o,C as n,a as l,b as m}from"./admin-COBm5LhQ.js";import{Q as d}from"./ui-vendor-COFtXQcG.js";const t=()=>{const[t,u]=r.useState([]),[c,p]=r.useState(!0),[v,b]=r.useState("all");r.useEffect(()=>{e(null,null,function*(){try{p(!0);const{data:e,error:a}=yield i.from("informes").select("\n            id,\n            titulo,\n            tipo_informe,\n            estado,\n            fecha_generacion,\n            generado_por,\n            observaciones,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero,\n              email\n            ),\n            resultados:resultado_id (\n              id,\n              aptitudes:aptitud_id (\n                codigo,\n                nombre\n              )\n            )\n          ").order("fecha_generacion",{ascending:!1});if(a)return void d.error("Error al cargar los informes guardados");u(e||[])}catch(e){d.error("Error al cargar los informes guardados")}finally{p(!1)}})},[]);const N=e=>"evaluacion_completa"===e?"fas fa-file-medical":"fas fa-file-alt",f=t.filter(e=>"all"===v||("individual"===v?"evaluacion_individual"===e.tipo_informe:"complete"!==v||"evaluacion_completa"===e.tipo_informe));return a.jsxDEV("div",{className:"container mx-auto py-6",children:[a.jsxDEV("div",{className:"flex justify-between items-center mb-6",children:[a.jsxDEV("div",{children:[a.jsxDEV("h1",{className:"text-2xl font-bold text-blue-800",children:[a.jsxDEV("i",{className:"fas fa-archive mr-3 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:115,columnNumber:13},void 0),"Informes Guardados - Administración"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:114,columnNumber:11},void 0),a.jsxDEV("p",{className:"text-gray-600 mt-1",children:[f.length," informe",1!==f.length?"s":""," guardado",1!==f.length?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:118,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:113,columnNumber:9},void 0),a.jsxDEV("div",{className:"flex space-x-2",children:[a.jsxDEV(o,{onClick:()=>b("all"),variant:"all"===v?"primary":"outline",size:"sm",children:"Todos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:125,columnNumber:11},void 0),a.jsxDEV(o,{onClick:()=>b("individual"),variant:"individual"===v?"primary":"outline",size:"sm",children:"Individuales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:132,columnNumber:11},void 0),a.jsxDEV(o,{onClick:()=>b("complete"),variant:"complete"===v?"primary":"outline",size:"sm",children:"Completos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:139,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:124,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:112,columnNumber:7},void 0),c?a.jsxDEV("div",{className:"py-16 text-center",children:[a.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:151,columnNumber:11},void 0),a.jsxDEV("p",{className:"text-gray-500",children:"Cargando informes guardados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:152,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:150,columnNumber:9},void 0):a.jsxDEV(a.Fragment,{children:0===f.length?a.jsxDEV(n,{children:a.jsxDEV(l,{children:a.jsxDEV("div",{className:"py-8 text-center",children:[a.jsxDEV("i",{className:"fas fa-folder-open text-4xl text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:160,columnNumber:19},void 0),a.jsxDEV("p",{className:"text-gray-500",children:"No hay informes guardados disponibles."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:161,columnNumber:19},void 0),a.jsxDEV("p",{className:"text-sm text-gray-400 mt-2",children:"Los informes aparecerán aquí una vez que se generen y guarden."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:162,columnNumber:19},void 0),a.jsxDEV(o,{as:s,to:"/admin/reports",className:"mt-4",children:[a.jsxDEV("i",{className:"fas fa-plus mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:166,columnNumber:21},void 0),"Ver Resultados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:165,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:159,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:158,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:157,columnNumber:13},void 0):a.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:f.map(r=>{var c,p,v,b,f,g;const x="evaluacion_completa"===r.tipo_informe?"blue":"green";var h;return a.jsxDEV(n,{className:`overflow-hidden shadow-lg border border-${x}-200 hover:shadow-xl transition-shadow duration-300`,children:[a.jsxDEV(m,{className:`bg-gradient-to-r from-${x}-500 to-${x}-600 border-b border-${x}-300`,children:a.jsxDEV("div",{className:"flex items-center justify-between",children:[a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3 border-2 border-white border-opacity-30",children:a.jsxDEV("i",{className:`${N(r.tipo_informe)} text-white text-lg`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:182,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:181,columnNumber:27},void 0),a.jsxDEV("div",{children:[a.jsxDEV("h3",{className:"text-white font-semibold text-sm",children:"evaluacion_completa"===r.tipo_informe?"Informe Completo":"Informe Individual"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:185,columnNumber:29},void 0),a.jsxDEV("p",{className:"text-white text-opacity-80 text-xs",children:new Date(r.fecha_generacion).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:188,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:184,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:180,columnNumber:25},void 0),a.jsxDEV("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${h=r.estado,{generado:"bg-blue-100 text-blue-800",revisado:"bg-yellow-100 text-yellow-800",finalizado:"bg-green-100 text-green-800"}[h]||"bg-gray-100 text-gray-800"}`,children:r.estado},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:193,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:179,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:178,columnNumber:21},void 0),a.jsxDEV(l,{className:"p-4",children:a.jsxDEV("div",{className:"space-y-3",children:[a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"w-10 h-10 rounded-full flex items-center justify-center text-white text-sm mr-3 "+("masculino"===(null==(c=r.pacientes)?void 0:c.genero)?"bg-blue-500":"bg-pink-500"),children:a.jsxDEV("i",{className:"fas "+("masculino"===(null==(p=r.pacientes)?void 0:p.genero)?"fa-mars":"fa-venus")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:206,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:203,columnNumber:27},void 0),a.jsxDEV("div",{children:[a.jsxDEV("p",{className:"font-semibold text-gray-900 text-sm",children:[null==(v=r.pacientes)?void 0:v.nombre," ",null==(b=r.pacientes)?void 0:b.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:209,columnNumber:29},void 0),(null==(f=r.pacientes)?void 0:f.documento)&&a.jsxDEV("p",{className:"text-gray-500 text-xs",children:["Doc: ",r.pacientes.documento]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:213,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:208,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:202,columnNumber:25},void 0),"evaluacion_individual"===r.tipo_informe&&(null==(g=r.resultados)?void 0:g.aptitudes)&&a.jsxDEV("div",{className:"bg-gray-50 p-2 rounded-lg",children:[a.jsxDEV("p",{className:"text-xs text-gray-500",children:"Test Evaluado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:223,columnNumber:29},void 0),a.jsxDEV("p",{className:"text-sm font-medium text-gray-700",children:[r.resultados.aptitudes.codigo," - ",r.resultados.aptitudes.nombre]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:224,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:222,columnNumber:27},void 0),a.jsxDEV("div",{children:[a.jsxDEV("p",{className:"text-xs text-gray-500",children:"Título:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:232,columnNumber:27},void 0),a.jsxDEV("p",{className:"text-sm text-gray-700 line-clamp-2",children:r.titulo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:233,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:231,columnNumber:25},void 0),r.observaciones&&a.jsxDEV("div",{children:[a.jsxDEV("p",{className:"text-xs text-gray-500",children:"Observaciones:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:241,columnNumber:29},void 0),a.jsxDEV("p",{className:"text-sm text-gray-700 line-clamp-2",children:r.observaciones},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:242,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:240,columnNumber:27},void 0),a.jsxDEV("div",{className:"text-xs text-gray-500 space-y-1",children:[a.jsxDEV("p",{children:["Generado por: ",r.generado_por||"Sistema"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:250,columnNumber:27},void 0),a.jsxDEV("p",{children:["Fecha: ",new Date(r.fecha_generacion).toLocaleString("es-ES")]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:251,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:249,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:200,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:199,columnNumber:21},void 0),a.jsxDEV("div",{className:"bg-gray-50 px-4 py-3 border-t",children:a.jsxDEV("div",{className:"flex justify-between items-center",children:[a.jsxDEV(o,{as:s,to:`/admin/informe-guardado/${r.id}`,variant:"primary",size:"sm",children:[a.jsxDEV("i",{className:"fas fa-eye mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:265,columnNumber:27},void 0),"Ver Informe"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:259,columnNumber:25},void 0),a.jsxDEV("div",{className:"flex space-x-2",children:[a.jsxDEV(o,{onClick:()=>window.open(`/admin/informe-guardado/${r.id}`,"_blank"),variant:"outline",size:"sm",children:a.jsxDEV("i",{className:"fas fa-external-link-alt"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:275,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:270,columnNumber:27},void 0),a.jsxDEV(o,{onClick:()=>{return a=r.id,e(null,null,function*(){if(window.confirm("¿Estás seguro de que deseas eliminar este informe?"))try{const{error:e}=yield i.from("informes").delete().eq("id",a);if(e)throw e;u(t.filter(e=>e.id!==a)),d.success("Informe eliminado correctamente")}catch(e){d.error("Error al eliminar el informe")}});var a},variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:a.jsxDEV("i",{className:"fas fa-trash"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:283,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:277,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:269,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:258,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:257,columnNumber:21},void 0)]},r.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:177,columnNumber:19},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:173,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:155,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/SavedReports.jsx",lineNumber:111,columnNumber:5},void 0)};export{t as default};
