var e=Object.defineProperty,r=Object.defineProperties,i=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,t=(r,i,o)=>i in r?e(r,i,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[i]=o,n=(e,r)=>{for(var i in r||(r={}))a.call(r,i)&&t(e,i,r[i]);if(o)for(var i of o(r))s.call(r,i)&&t(e,i,r[i]);return e},l=(e,o)=>r(e,i(o)),m=(e,r,i)=>new Promise((o,a)=>{var s=e=>{try{n(i.next(e))}catch(r){a(r)}},t=e=>{try{n(i.throw(e))}catch(r){a(r)}},n=e=>e.done?o(e.value):Promise.resolve(e.value).then(s,t);n((i=i.apply(e,r)).next())});import{s as c,j as d,v as u,a7 as p,aa as f,N as b,K as N,ab as h,ac as x,w as g,Z as v,ad as j,ae as V,af as C,ag as E,ah as D,a8 as P,ai as y,y as B,z as _,g as M,aj as I,ak as w,c as S,al as R,Y as A,am as F,C as k,r as T,Q as z,i as $,an as q,ao as G,V as L,W as O,ap as U}from"./auth-BzDSP4i9.js";import{r as W}from"./react-vendor-C9XH6RF0.js";import{B as H,C as J,b as Y,a as Q,P as K}from"./admin-BucOs87s.js";import{G as Z,Q as X}from"./ui-vendor-COFtXQcG.js";import{f as ee,d as re,P as ie,B as oe}from"./index-DNP2-XsR.js";import ae from"./InformesService-C5wbmV6D.js";import"./ImprovedPinControlService-BUJ25XQR.js";class se{static deletePatientReports(e,r="all"){return m(this,null,function*(){if(!e||"string"!=typeof e)return{success:!1,message:"ID de paciente inválido"};if(!["single","all"].includes(r))return{success:!1,message:"Tipo de eliminación inválido"};try{const{data:i,error:o}=yield c.from("pacientes").select("nombre, apellido").eq("id",e).single();if(o)throw new Error("Error al obtener información del paciente");const a=`${i.nombre} ${i.apellido}`;if("single"===r){const{data:r,error:i}=yield c.from("informes_generados").select("id, titulo, fecha_generacion").eq("paciente_id",e).eq("estado","generado").order("fecha_generacion",{ascending:!1}).limit(1).single();if(i||!r)return{success:!1,message:"No hay informes generados para eliminar"};const{error:o}=yield c.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).eq("id",r.id);if(o)throw new Error("Error al eliminar el informe");return{success:!0,message:`Último informe de ${a} eliminado exitosamente`,deletedCount:1}}{const{data:r,error:i}=yield c.from("informes_generados").select("id, titulo").eq("paciente_id",e).eq("estado","generado");if(i)throw new Error("Error al buscar informes del paciente");if(!r||0===r.length)return{success:!1,message:"No hay informes generados para eliminar"};const{error:o}=yield c.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).eq("paciente_id",e).eq("estado","generado");if(o)throw new Error("Error al eliminar los informes");return{success:!0,message:`${r.length} informe(s) de ${a} eliminado(s) exitosamente`,deletedCount:r.length}}}catch(i){return{success:!1,message:i.message||"Error al eliminar informes"}}})}static batchDeleteReports(e){return m(this,null,function*(){try{if(!e||0===e.length)return{success:!1,message:"No se seleccionaron pacientes"};const{data:r,error:i}=yield c.from("informes_generados").select("id, paciente_id").in("paciente_id",e).eq("estado","generado");if(i)throw new Error("Error al contar informes a eliminar");if(!r||0===r.length)return{success:!1,message:"No hay informes generados para los pacientes seleccionados"};const{error:o}=yield c.from("informes_generados").update({estado:"eliminado",fecha_eliminacion:(new Date).toISOString()}).in("paciente_id",e).eq("estado","generado");if(o)throw new Error("Error al eliminar informes en lote");return{success:!0,message:`${r.length} informe(s) de ${e.length} paciente(s) eliminado(s) exitosamente`,deletedCount:r.length,affectedPatients:e.length}}catch(r){return{success:!1,message:r.message||"Error al eliminar informes en lote"}}})}static restorePatientReports(e){return m(this,null,function*(){try{const{data:r,error:i}=yield c.from("informes_generados").select("id, titulo").eq("paciente_id",e).eq("estado","eliminado");if(i)throw new Error("Error al buscar informes eliminados");if(!r||0===r.length)return{success:!1,message:"No hay informes eliminados para restaurar"};const{error:o}=yield c.from("informes_generados").update({estado:"generado",fecha_eliminacion:null}).eq("paciente_id",e).eq("estado","eliminado");if(o)throw new Error("Error al restaurar informes");return{success:!0,message:`${r.length} informe(s) restaurado(s) exitosamente`,restoredCount:r.length}}catch(r){return{success:!1,message:r.message||"Error al restaurar informes"}}})}static getPatientReportHistory(e){return m(this,null,function*(){try{const{data:r,error:i}=yield c.from("informes_generados").select("\n          id,\n          titulo,\n          descripcion,\n          estado,\n          fecha_generacion,\n          fecha_eliminacion,\n          contenido\n        ").eq("paciente_id",e).order("fecha_generacion",{ascending:!1});if(i)throw new Error("Error al obtener historial de informes");return r||[]}catch(r){throw r}})}static getPatientTestSummary(e){return m(this,null,function*(){var r;try{const{data:i,error:o}=yield c.from("resultados").select("\n          id,\n          created_at,\n          aptitudes(codigo, nombre)\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(o)throw new Error("Error al obtener resumen de tests");const a=(null==i?void 0:i.length)||0,s=(null==i?void 0:i.map(e=>{var r;return null==(r=e.aptitudes)?void 0:r.codigo}).filter(Boolean))||[],t=[...new Set(s)],n=null==(r=null==i?void 0:i[0])?void 0:r.created_at;return{hasResults:a>0,testCount:a,aptitudesCount:t.length,aptitudes:t,lastTestDate:n}}catch(i){throw i}})}}class te{static searchPatients(){return m(this,arguments,function*(e={},r={page:1,limit:20}){try{const{institution:i=null,gender:o=null,dateFrom:a=null,dateTo:s=null,patientName:t="",document:n="",testStatus:l="all",sortBy:m="created_at",sortOrder:d="desc"}=e,{page:u,limit:p}=r,f=(u-1)*p;let b=c.from("pacientes").select("\n          id,\n          nombre,\n          apellido,\n          documento,\n          genero,\n          fecha_nacimiento,\n          created_at,\n          instituciones:institucion_id (\n            id,\n            nombre,\n            codigo\n          ),\n          resultados (\n            id,\n            created_at,\n            aptitudes (\n              codigo,\n              nombre\n            )\n          )\n        ",{count:"exact"});if(i&&(b=b.eq("institucion_id",i)),o&&"all"!==o&&(b=b.eq("genero",o)),t.trim()){const e=`%${t.trim()}%`;b=b.or(`nombre.ilike.${e},apellido.ilike.${e}`)}n.trim()&&(b=b.ilike("documento",`%${n.trim()}%`));const N="name"===m?"nombre":m;b=b.order(N,{ascending:"asc"===d}),b=b.range(f,f+p-1);const{data:h,error:x,count:g}=yield b;if(x)throw new Error(`Error en búsqueda: ${x.message}`);return{patients:yield this.processSearchResults(h||[],{dateFrom:a,dateTo:s,testStatus:l}),total:g||0,page:u,limit:p,totalPages:Math.ceil((g||0)/p),hasNextPage:u*p<(g||0),hasPrevPage:u>1}}catch(i){throw i}})}static processSearchResults(e,r){return m(this,null,function*(){const{dateFrom:i,dateTo:o,testStatus:a}=r;return e.map(e=>{const r=e.resultados||[];let s=r;(i||o)&&(s=r.filter(e=>{const r=new Date(e.created_at),a=i?new Date(i):null,s=o?new Date(o):null;return!(a&&r<a)&&!(s&&r>s)}));const t=s.length,m=s.map(e=>{var r;return null==(r=e.aptitudes)?void 0:r.codigo}).filter(Boolean),c=[...new Set(m)],d=s.length>0?s.sort((e,r)=>new Date(r.created_at)-new Date(e.created_at))[0].created_at:null;let u="no_tests";if(u=0===t?"no_tests":c.length<8?"partial":"completed","all"!==a){if("completed"===a&&"completed"!==u)return null;if("partial"===a&&"partial"!==u)return null;if("no_tests"===a&&"no_tests"!==u)return null}return l(n({},e),{testSummary:{testCount:t,uniqueAptitudesCount:c.length,aptitudes:c,lastTestDate:d,status:u,completionPercentage:Math.round(c.length/8*100)},resultados:s})}).filter(Boolean)})}static getInstitutions(){return m(this,null,function*(){try{const{data:e,error:r}=yield c.from("instituciones").select("id, nombre, codigo").order("nombre");if(r)throw new Error(`Error al obtener instituciones: ${r.message}`);return e||[]}catch(e){throw e}})}static getPatientNameSuggestions(e,r=10){return m(this,null,function*(){try{if(!e||e.length<2)return[];const i=`%${e.trim()}%`,{data:o,error:a}=yield c.from("pacientes").select("id, nombre, apellido, documento").or(`nombre.ilike.${i},apellido.ilike.${i}`).limit(r);if(a)throw new Error(`Error en sugerencias: ${a.message}`);return(o||[]).map(e=>({id:e.id,label:`${e.nombre} ${e.apellido}`,sublabel:e.documento,value:`${e.nombre} ${e.apellido}`}))}catch(i){return[]}})}static getSearchStats(){return m(this,arguments,function*(e={}){try{const{data:e,error:r}=yield c.from("pacientes").select("\n          id,\n          genero,\n          institucion_id,\n          resultados!inner (\n            id,\n            created_at\n          )\n        ");if(r)throw new Error(`Error al obtener estadísticas: ${r.message}`);const i=(null==e?void 0:e.length)||0,o=(null==e?void 0:e.filter(e=>{var r;return null==(r=e.genero)?void 0:r.toLowerCase().startsWith("m")}).length)||0,a=(null==e?void 0:e.filter(e=>{var r;return null==(r=e.genero)?void 0:r.toLowerCase().startsWith("f")}).length)||0,s={};null==e||e.forEach(e=>{const r=e.institucion_id;s[r]=(s[r]||0)+1});const t=(null==e?void 0:e.flatMap(e=>e.resultados.map(e=>new Date(e.created_at))))||[],n=t.length>0?new Date(Math.min(...t)):null,l=t.length>0?new Date(Math.max(...t)):null;return{totalPatients:i,genderDistribution:{male:o,female:a},institutionDistribution:s,dateRange:{earliest:n,latest:l},totalTests:t.length}}catch(r){return{totalPatients:0,genderDistribution:{male:0,female:0},institutionDistribution:{},dateRange:{earliest:null,latest:null},totalTests:0}}})}static exportToCSV(e){if(!e||0===e.length)return"";return[["ID","Nombre","Apellido","Documento","Género","Institución","Tests Realizados","Aptitudes Evaluadas","Porcentaje Completado","Última Evaluación","Estado"],...e.map(e=>{var r,i,o,a,s,t;return[e.id,e.nombre,e.apellido,e.documento,e.genero,(null==(r=e.instituciones)?void 0:r.nombre)||"N/A",(null==(i=e.testSummary)?void 0:i.testCount)||0,(null==(o=e.testSummary)?void 0:o.uniqueAptitudesCount)||0,`${(null==(a=e.testSummary)?void 0:a.completionPercentage)||0}%`,(null==(s=e.testSummary)?void 0:s.lastTestDate)?new Date(e.testSummary.lastTestDate).toLocaleDateString("es-ES"):"N/A",(null==(t=e.testSummary)?void 0:t.status)||"no_tests"]})].map(e=>e.map(e=>`"${e}"`).join(",")).join("\n")}}const ne=5,le="unlimited",me="active",ce="low_pins",de="no_pins";const ue=new class{getAllPsychologists(){return m(this,null,function*(){try{const{data:e,error:r}=yield c.rpc("get_all_psychologists_with_stats");if(r)return yield this._getAllPsychologistsManual();return(e||[]).map(e=>this._transformPsychologistData(e,!0))}catch(e){return yield this._getAllPsychologistsManual()}})}_getAllPsychologistsManual(){return m(this,null,function*(){const{data:e,error:r}=yield c.from("psicologos").select("id, nombre, apellido, email").order("nombre");if(r)throw r;const{data:i,error:o}=yield c.from("psychologist_usage_control").select("*").eq("is_active",!0);if(o)throw o;const{data:a,error:s}=yield c.from("pacientes").select("psicologo_id").not("psicologo_id","is",null);if(s)throw s;const{data:t,error:n}=yield c.from("resultados").select("\n        pacientes!inner(psicologo_id)\n      ");if(n)throw n;const l=this._createCountMap(a,"psicologo_id"),m=this._createCountMap(t.map(e=>({psicologo_id:e.pacientes.psicologo_id})),"psicologo_id");return(e||[]).map(e=>{const r=null==i?void 0:i.find(r=>r.psychologist_id===e.id),o=l.get(e.id)||0,a=m.get(e.id)||0;return this._transformPsychologistData({psychologist_id:e.id,nombre:e.nombre,apellido:e.apellido,email:e.email,total_uses:(null==r?void 0:r.total_uses)||0,used_uses:(null==r?void 0:r.used_uses)||0,is_unlimited:(null==r?void 0:r.is_unlimited)||!1,plan_type:(null==r?void 0:r.plan_type)||"none",updated_at:null==r?void 0:r.updated_at,assigned_patients:o,completed_tests:a},!0)})})}_createCountMap(e,r){const i=new Map;return e.forEach(e=>{const o=e[r];o&&i.set(o,(i.get(o)||0)+1)}),i}_transformPsychologistData(e,r=!1){const i=e.total_uses||0,o=e.used_uses||0,a=e.is_unlimited?null:Math.max(0,i-o),s=this._determineStatus(e.is_unlimited,i,a),t={psychologist_id:e.psychologist_id||e.id,psychologist_name:`${e.nombre} ${e.apellido}`,psychologist_email:e.email,total_pins:i,used_pins:o,remaining_pins:a,is_unlimited:e.is_unlimited||!1,plan_type:e.plan_type||"none",usage_percentage:this._calculateUsagePercentage(o,i,e.is_unlimited),assigned_patients:e.assigned_patients||0,completed_tests:e.completed_tests||0,status:s,last_activity:e.updated_at};return r&&(t.has_control=!(void 0===e.total_uses&&!e.is_unlimited)),t}_transformPinStatsData(e){return{psychologist_id:e.psych_id,psychologist_name:e.psych_name,psychologist_email:e.psych_email,total_pins:e.total_pins,used_pins:e.used_pins,remaining_pins:e.remaining_pins,is_unlimited:e.is_unlimited,plan_type:e.plan_type,usage_percentage:parseFloat(e.usage_percentage)||0,assigned_patients:e.assigned_patients,completed_tests:e.completed_tests,pins_consumed_today:e.pins_consumed_today,status:e.status,last_activity:e.last_activity}}getPinConsumptionStats(){return m(this,null,function*(){try{const{data:e,error:r}=yield c.rpc("get_pin_stats_v2");if(r)return yield this._getPinConsumptionStatsManual();return(e||[]).map(e=>this._transformPinStatsData(e))}catch(e){return yield this._getPinConsumptionStatsManual()}})}_getPinConsumptionStatsManual(){return m(this,null,function*(){const{data:e,error:r}=yield c.from("psicologos").select("\n        id,\n        nombre,\n        apellido,\n        email,\n        psychologist_usage_control!inner (\n          total_uses,\n          used_uses,\n          is_unlimited,\n          plan_type,\n          updated_at,\n          is_active\n        )\n      ").eq("psychologist_usage_control.is_active",!0);if(r)throw r;const i=e.map(e=>e.id),{data:o}=yield c.from("pacientes").select("psicologo_id").in("psicologo_id",i),{data:a}=yield c.from("resultados").select("pacientes!inner(psicologo_id)").in("pacientes.psicologo_id",i),s=this._createCountMap(o||[],"psicologo_id"),t=this._createCountMap((a||[]).map(e=>({psicologo_id:e.pacientes.psicologo_id})),"psicologo_id");return(e||[]).map(e=>{const r=e.psychologist_usage_control[0],i=s.get(e.id)||0,o=t.get(e.id)||0;return this._transformPsychologistData({psychologist_id:e.id,nombre:e.nombre,apellido:e.apellido,email:e.email,total_uses:(null==r?void 0:r.total_uses)||0,used_uses:(null==r?void 0:r.used_uses)||0,is_unlimited:(null==r?void 0:r.is_unlimited)||!1,plan_type:(null==r?void 0:r.plan_type)||"none",updated_at:null==r?void 0:r.updated_at,assigned_patients:i,completed_tests:o})})})}assignPins(e,r,i=!1,o="assigned"){return m(this,null,function*(){try{const{data:a,error:s}=yield c.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(s&&"PGRST116"!==s.code)throw s;let t;if(a){const e=i?0:a.total_uses+r,{data:s,error:n}=yield c.from("psychologist_usage_control").update({total_uses:e,is_unlimited:i,plan_type:o,updated_at:(new Date).toISOString()}).eq("id",a.id).select().single();if(n)throw n;t=s}else{const{data:a,error:s}=yield c.from("psychologist_usage_control").insert({psychologist_id:e,total_uses:i?0:r,used_uses:0,is_unlimited:i,plan_type:o,is_active:!0}).select().single();if(s)throw s;t=a}return yield this.logPinAction(e,"pin_assigned",{pins_assigned:r,is_unlimited:i,plan_type:o}),t}catch(a){throw a}})}consumePin(e,r=null,i=null,o=null){return m(this,null,function*(){try{const{data:a,error:s}=yield c.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(s)throw new Error("No se encontró control de uso para este psicólogo");if(a.is_unlimited)return yield this.logPinAction(e,"pin_consumed",{patient_id:r,test_session_id:i,report_id:o,is_unlimited:!0},r,i,o),!0;const t=a.total_uses-a.used_uses;if(t<=0)throw new Error("No hay pines disponibles para este psicólogo");const{data:n,error:l}=yield c.from("psychologist_usage_control").update({used_uses:a.used_uses+1,updated_at:(new Date).toISOString()}).eq("id",a.id).select().single();if(l)throw l;yield this.logPinAction(e,"pin_consumed",{pins_before:t,pins_after:t-1,patient_id:r,test_session_id:i,report_id:o},r,i,o);const m=t-1;return m<=ne&&m>0&&(yield this.createLowPinNotification(e,m)),!0}catch(a){throw a}})}checkPsychologistUsage(e){return m(this,null,function*(){try{const{data:r,error:i}=yield c.from("psychologist_usage_control").select("*").eq("psychologist_id",e).eq("is_active",!0).single();if(i&&"PGRST116"!==i.code)throw i;if(!r)return{canUse:!1,reason:"No tiene pines asignados",remainingPins:0,isUnlimited:!1};if(r.is_unlimited)return{canUse:!0,reason:"Plan ilimitado",remainingPins:null,isUnlimited:!0};const o=r.total_uses-r.used_uses;return{canUse:o>0,reason:o>0?"Pines disponibles":"Sin pines disponibles",remainingPins:o,isUnlimited:!1,totalPins:r.total_uses,usedPins:r.used_uses}}catch(r){throw r}})}getPinUsageHistory(e=null,r=50){return m(this,null,function*(){try{let i=c.from("pin_usage_logs").select("\n          *,\n          psychologist:psicologos(nombre, apellido, email),\n          patient:pacientes(nombre, apellido, documento)\n        ").order("created_at",{ascending:!1}).limit(r);e&&(i=i.eq("psychologist_id",e));const{data:o,error:a}=yield i;if(a)throw a;return o||[]}catch(i){throw i}})}getPinConsumptionAlerts(){return m(this,null,function*(){try{const e=yield this.getPinConsumptionStats(),r=[];return e.forEach(e=>{"low_pins"===e.status?r.push({type:"warning",psychologist_id:e.psychologist_id,psychologist_name:e.psychologist_name,message:`${e.psychologist_name} tiene solo ${e.remaining_pins} pines restantes`,severity:"warning"}):"no_pins"===e.status&&r.push({type:"error",psychologist_id:e.psychologist_id,psychologist_name:e.psychologist_name,message:`${e.psychologist_name} no tiene pines disponibles`,severity:"error"})}),r}catch(e){throw e}})}logPinAction(e,r){return m(this,arguments,function*(e,r,i={},o=null,a=null,s=null){try{const{error:t}=yield c.from("pin_usage_logs").insert({psychologist_id:e,patient_id:o,test_session_id:a,report_id:s,action_type:r,pins_before:i.pins_before||0,pins_after:i.pins_after||0,pins_consumed:"pin_consumed"===r?1:0,description:this.getActionDescription(r,i),metadata:i})}catch(t){}})}createLowPinNotification(e,r){return m(this,null,function*(){try{const{error:i}=yield c.rpc("create_low_pin_notification",{p_psychologist_id:e,p_remaining_pins:r})}catch(i){}})}_determineStatus(e,r,i){return e?le:0===r||i<=0?de:i<=ne?ce:me}_calculateUsagePercentage(e,r,i){return i||0===r?0:Math.round(e/r*100*100)/100}getActionDescription(e,r){switch(e){case"pin_assigned":return`Se asignaron ${r.pins_assigned||0} pines${r.is_unlimited?" (plan ilimitado)":""}`;case"pin_consumed":return r.is_unlimited?"Pin consumido (plan ilimitado)":`Pin consumido. Quedan ${r.pins_after||0} pines`;case"test_completed":return"Test completado - Pin consumido automáticamente";case"report_generated":return"Informe generado - Pin consumido automáticamente";default:return`Acción: ${e}`}}getSystemSummary(){return m(this,null,function*(){try{const e=yield this.getPinConsumptionStats();return{totalPsychologists:e.length,totalPinsAssigned:e.reduce((e,r)=>e+(r.is_unlimited?0:r.total_pins),0),totalPinsUsed:e.reduce((e,r)=>e+r.used_pins,0),totalPinsRemaining:e.reduce((e,r)=>e+(r.is_unlimited?0:r.remaining_pins||0),0),unlimitedPsychologists:e.filter(e=>e.is_unlimited).length,activePsychologists:e.filter(e=>"active"===e.status).length,lowPinsPsychologists:e.filter(e=>"low_pins"===e.status).length,noPinsPsychologists:e.filter(e=>"no_pins"===e.status).length,totalPatients:e.reduce((e,r)=>e+r.assigned_patients,0),totalTests:e.reduce((e,r)=>e+r.completed_tests,0)}}catch(e){throw e}})}};ue.getMigrationRecommendation=()=>{};class pe{static getResultadosByPaciente(e){return m(this,null,function*(){try{const{data:r,error:i}=yield c.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero,\n            fecha_nacimiento,\n            created_at\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(i)throw i;return r||[]}catch(r){throw r}})}static getEstadisticasPaciente(e){return m(this,null,function*(){var r,i;try{const o=yield this.getResultadosByPaciente(e);if(0===o.length)return{totalTests:0,averagePercentile:0,averageDirectScore:0,totalErrors:0,averageErrors:0,totalTimeSeconds:0,averageTimeSeconds:0,concentracionPromedio:0,lastTestDate:null,patientInfo:null};const a=o.length,s=o.reduce((e,r)=>e+(r.percentil||0),0),t=o.reduce((e,r)=>e+(r.puntaje_directo||0),0),n=o.reduce((e,r)=>e+(r.errores||0),0),l=o.reduce((e,r)=>e+(r.tiempo_segundos||0),0),m=o.reduce((e,r)=>e+(r.concentracion||0),0);return{totalTests:a,averagePercentile:Math.round(s/a),averageDirectScore:Math.round(t/a),totalErrors:n,averageErrors:n/a,totalTimeSeconds:l,averageTimeSeconds:l/a,concentracionPromedio:m/a,lastTestDate:null==(r=o[0])?void 0:r.created_at,patientInfo:null==(i=o[0])?void 0:i.pacientes,resultadosPorAptitud:this.agruparPorAptitud(o)}}catch(o){throw o}})}static agruparPorAptitud(e){const r={};return e.forEach(e=>{var i;const o=null==(i=e.aptitudes)?void 0:i.codigo;o&&(r[o]||(r[o]={aptitud:e.aptitudes,resultados:[],promedios:{percentil:0,puntajeDirecto:0,errores:0,tiempo:0,concentracion:0}}),r[o].resultados.push(e))}),Object.keys(r).forEach(e=>{const i=r[e],o=i.resultados.length;i.promedios={percentil:Math.round(i.resultados.reduce((e,r)=>e+(r.percentil||0),0)/o),puntajeDirecto:Math.round(i.resultados.reduce((e,r)=>e+(r.puntaje_directo||0),0)/o),errores:i.resultados.reduce((e,r)=>e+(r.errores||0),0)/o,tiempo:i.resultados.reduce((e,r)=>e+(r.tiempo_segundos||0),0)/o,concentracion:i.resultados.reduce((e,r)=>e+(r.concentracion||0),0)/o}}),r}static insertarResultado(e,r=null){return m(this,null,function*(){try{const{data:o,error:a}=yield c.from("resultados").insert([{paciente_id:e.paciente_id,aptitud_id:e.aptitud_id,puntaje_directo:e.puntaje_directo,percentil:e.percentil,interpretacion:e.interpretacion,tiempo_segundos:e.tiempo_segundos,respuestas:e.respuestas,concentracion:e.concentracion,errores:e.errores||0,percentil_compared:e.percentil_compared,respuestas_correctas:e.respuestas_correctas,respuestas_incorrectas:e.respuestas_incorrectas,respuestas_sin_contestar:e.respuestas_sin_contestar,total_preguntas:e.total_preguntas}]).select().single();if(a)throw a;try{const{data:i,error:o}=yield c.from("pacientes").select("psicologo_id").eq("id",e.paciente_id).single();!o&&(null==i?void 0:i.psicologo_id)&&(yield ue.consumePin(i.psicologo_id,e.paciente_id,r,null))}catch(i){}return o}catch(o){throw o}})}static actualizarResultado(e,r){return m(this,null,function*(){try{const{data:i,error:o}=yield c.from("resultados").update(l(n({},r),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(o)throw o;return i}catch(i){throw i}})}static eliminarResultado(e){return m(this,null,function*(){try{const{error:r}=yield c.from("resultados").delete().eq("id",e);if(r)throw r;return!0}catch(r){throw r}})}static getResultadosPaginados(){return m(this,arguments,function*(e=1,r=10,i={}){try{let o=c.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre\n          )\n        ",{count:"exact"});i.paciente_id&&(o=o.eq("paciente_id",i.paciente_id)),i.aptitud_id&&(o=o.eq("aptitud_id",i.aptitud_id)),i.fecha_desde&&(o=o.gte("created_at",i.fecha_desde)),i.fecha_hasta&&(o=o.lte("created_at",i.fecha_hasta));const a=(e-1)*r,s=a+r-1;o=o.range(a,s).order("created_at",{ascending:!1});const{data:t,error:n,count:l}=yield o;if(n)throw n;return{data:t||[],total:l||0,page:e,limit:r,totalPages:Math.ceil((l||0)/r)}}catch(o){throw o}})}static getEstadisticasGenerales(){return m(this,null,function*(){try{const{data:e,error:r}=yield c.from("resultados").select("*");if(r)throw r;const{data:i,error:o}=yield c.from("pacientes").select("id");if(o)throw o;const a=(null==e?void 0:e.length)||0,s=(null==i?void 0:i.length)||0;if(0===a)return{totalResultados:0,totalPacientes:s,promedioTestsPorPaciente:0,percentilPromedio:0,puntajeDirectoPromedio:0,erroresPromedio:0,tiempoPromedioMinutos:0};const t=e.reduce((e,r)=>e+(r.percentil||0),0),n=e.reduce((e,r)=>e+(r.puntaje_directo||0),0),l=e.reduce((e,r)=>e+(r.errores||0),0),m=e.reduce((e,r)=>e+(r.tiempo_segundos||0),0);return{totalResultados:a,totalPacientes:s,promedioTestsPorPaciente:s>0?(a/s).toFixed(1):0,percentilPromedio:Math.round(t/a),puntajeDirectoPromedio:Math.round(n/a),erroresPromedio:(l/a).toFixed(1),tiempoPromedioMinutos:Math.round(m/a/60)}}catch(e){throw e}})}static buscarResultados(e){return m(this,null,function*(){try{let r=c.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          ),\n          aptitudes (\n            id,\n            codigo,\n            nombre,\n            descripcion\n          )\n        ");e.nombrePaciente&&(r=r.ilike("pacientes.nombre",`%${e.nombrePaciente}%`)),e.documentoPaciente&&(r=r.eq("pacientes.documento",e.documentoPaciente)),e.codigoAptitud&&(r=r.eq("aptitudes.codigo",e.codigoAptitud)),e.percentilMinimo&&(r=r.gte("percentil",e.percentilMinimo)),e.percentilMaximo&&(r=r.lte("percentil",e.percentilMaximo)),r=r.order("created_at",{ascending:!1});const{data:i,error:o}=yield r;if(o)throw o;return i||[]}catch(r){throw r}})}}class fe{static generateBatchReports(e){return m(this,arguments,function*(e,r=null,i={}){var o;const{reportType:a="individual",includeCharts:s=!0,format:t="pdf"}=i,n={successful:[],failed:[],total:e.length,startTime:new Date,endTime:null};try{for(let i=0;i<e.length;i++){const a=e[i],s=Math.round((i+1)/e.length*100);r&&r({current:i+1,total:e.length,percentage:s,currentPatientId:a,status:"processing"});try{const e=yield pe.getResultadosByPaciente(a);if(!e||0===e.length){n.failed.push({patientId:a,error:"No hay resultados de evaluación disponibles"});continue}const r=null==(o=e[0])?void 0:o.pacientes;if(!r){n.failed.push({patientId:a,error:"Información del paciente no encontrada"});continue}const i=yield ae.generarInformeCompleto(a,`Informe BAT-7 - ${r.nombre} ${r.apellido}`,`Informe generado en lote - ${(new Date).toLocaleDateString("es-ES")}`),s=yield ae.obtenerInforme(i);n.successful.push({patientId:a,patientName:`${r.nombre} ${r.apellido}`,reportId:i,reportData:s}),yield new Promise(e=>setTimeout(e,100))}catch(l){n.failed.push({patientId:a,error:l.message||"Error desconocido"})}}return n.endTime=new Date,n.duration=n.endTime-n.startTime,r&&r({current:e.length,total:e.length,percentage:100,status:"completed",results:n}),n}catch(l){throw n.endTime=new Date,n.duration=n.endTime-n.startTime,new Error(`Error en generación masiva: ${l.message}`)}})}static generateComparativeReport(e){return m(this,arguments,function*(e,r={}){try{const{title:i="Informe Comparativo BAT-7",includeIndividualSections:o=!1,groupBy:a="institution"}=r,s=[];for(const r of e){const e=yield pe.getResultadosByPaciente(r);if(e&&e.length>0){const i=yield pe.getEstadisticasPaciente(r);s.push({patient:e[0].pacientes,results:e,stats:i})}}if(0===s.length)throw new Error("No se encontraron datos válidos para la comparación");const t=this.generateComparativeAnalysis(s,a);return{tipo:"comparativo",titulo:i,fecha_generacion:(new Date).toISOString(),pacientes_incluidos:s.length,contenido:{resumen_ejecutivo:this.generateExecutiveSummary(t),analisis_grupal:t,estadisticas_generales:this.calculateGroupStatistics(s),recomendaciones:this.generateGroupRecommendations(t),pacientes:o?s:null}}}catch(i){throw i}})}static generateComparativeAnalysis(e,r){const i={grupos:{},aptitudes_comparadas:{},tendencias:{},outliers:[]};return e.forEach(({patient:e,stats:o})=>{var a;let s="general";switch(r){case"institution":s=(null==(a=e.instituciones)?void 0:a.nombre)||"Sin institución";break;case"gender":s=e.genero||"No especificado";break;case"age_group":const r=this.calculateAge(e.fecha_nacimiento);s=this.getAgeGroup(r)}i.grupos[s]||(i.grupos[s]={pacientes:[],estadisticas:{count:0,promedios:{},rangos:{}}}),i.grupos[s].pacientes.push({patient:e,stats:o}),i.grupos[s].estadisticas.count++}),Object.keys(i.grupos).forEach(e=>{const r=i.grupos[e];r.estadisticas=this.calculateGroupStats(r.pacientes)}),i.aptitudes_comparadas=this.compareAptitudesAcrossGroups(i.grupos),i}static calculateGroupStats(e){const r={count:e.length,promedios:{},rangos:{},distribucion_genero:{masculino:0,femenino:0}};if(0===e.length)return r;const i={};return e.forEach(({patient:e,stats:o})=>{var a;const s=null==(a=e.genero)?void 0:a.toLowerCase();(null==s?void 0:s.startsWith("m"))&&r.distribucion_genero.masculino++,(null==s?void 0:s.startsWith("f"))&&r.distribucion_genero.femenino++,o.resultadosPorAptitud&&Object.keys(o.resultadosPorAptitud).forEach(e=>{const r=o.resultadosPorAptitud[e];i[e]||(i[e]={percentiles:[],puntajesDirectos:[]}),i[e].percentiles.push(r.promedios.percentil),i[e].puntajesDirectos.push(r.promedios.puntajeDirecto)})}),Object.keys(i).forEach(e=>{const o=i[e];r.promedios[e]={percentil:Math.round(o.percentiles.reduce((e,r)=>e+r,0)/o.percentiles.length),puntajeDirecto:Math.round(o.puntajesDirectos.reduce((e,r)=>e+r,0)/o.puntajesDirectos.length)},r.rangos[e]={percentil:{min:Math.min(...o.percentiles),max:Math.max(...o.percentiles)},puntajeDirecto:{min:Math.min(...o.puntajesDirectos),max:Math.max(...o.puntajesDirectos)}}}),r}static compareAptitudesAcrossGroups(e){const r={},i=Object.keys(e),o=new Set;return i.forEach(r=>{const i=e[r];Object.keys(i.estadisticas.promedios||{}).forEach(e=>{o.add(e)})}),o.forEach(o=>{r[o]={grupos:{},mejor_grupo:null,mayor_diferencia:0};let a=-1,s=101,t=null;i.forEach(i=>{const n=e[i].estadisticas.promedios[o];n&&(r[o].grupos[i]=n,n.percentil>a&&(a=n.percentil,t=i),n.percentil<s&&(s=n.percentil))}),r[o].mejor_grupo=t,r[o].mayor_diferencia=a-s}),r}static generateExecutiveSummary(e){const r={total_grupos:Object.keys(e.grupos).length,total_pacientes:Object.values(e.grupos).reduce((e,r)=>e+r.estadisticas.count,0),aptitudes_evaluadas:Object.keys(e.aptitudes_comparadas).length,hallazgos_principales:[],recomendaciones_clave:[]},i=e.aptitudes_comparadas;return Object.keys(i).forEach(e=>{const o=i[e];o.mayor_diferencia>20&&r.hallazgos_principales.push(`Diferencia significativa en ${e}: ${o.mayor_diferencia} puntos entre grupos`)}),r}static calculateAge(e){if(!e)return 0;const r=new Date,i=new Date(e);let o=r.getFullYear()-i.getFullYear();const a=r.getMonth()-i.getMonth();return(a<0||0===a&&r.getDate()<i.getDate())&&o--,o}static getAgeGroup(e){return e<12?"Niños (< 12 años)":e<15?"Adolescentes tempranos (12-14 años)":e<18?"Adolescentes (15-17 años)":e<25?"Jóvenes adultos (18-24 años)":"Adultos (25+ años)"}static calculateGroupStatistics(e){return{total_pacientes:e.length,distribucion_genero:this.calculateGenderDistribution(e),rango_edades:this.calculateAgeRange(e),instituciones_representadas:this.getUniqueInstitutions(e),promedio_tests_por_paciente:this.calculateAverageTestsPerPatient(e)}}static generateGroupRecommendations(e){const r=[];return Object.keys(e.aptitudes_comparadas).forEach(i=>{const o=e.aptitudes_comparadas[i];o.mayor_diferencia>25&&r.push({tipo:"diferencia_significativa",aptitud:i,descripcion:`Se observa una diferencia significativa de ${o.mayor_diferencia} puntos en ${i} entre grupos`,recomendacion:`Considerar estrategias de intervención específicas para los grupos con menor rendimiento en ${i}`})}),r}static calculateGenderDistribution(e){const r={masculino:0,femenino:0,no_especificado:0};return e.forEach(({patient:e})=>{var i;const o=null==(i=e.genero)?void 0:i.toLowerCase();(null==o?void 0:o.startsWith("m"))?r.masculino++:(null==o?void 0:o.startsWith("f"))?r.femenino++:r.no_especificado++}),r}static calculateAgeRange(e){const r=e.map(({patient:e})=>this.calculateAge(e.fecha_nacimiento)).filter(e=>e>0);return 0===r.length?{min:0,max:0,promedio:0}:{min:Math.min(...r),max:Math.max(...r),promedio:Math.round(r.reduce((e,r)=>e+r,0)/r.length)}}static getUniqueInstitutions(e){const r=new Set;return e.forEach(({patient:e})=>{var i;(null==(i=e.instituciones)?void 0:i.nombre)&&r.add(e.instituciones.nombre)}),Array.from(r)}static calculateAverageTestsPerPatient(e){if(0===e.length)return 0;const r=e.reduce((e,{stats:r})=>e+(r.totalTests||0),0);return Math.round(r/e.length*10)/10}}function be(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"}}]})(e)}function Ne(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"}}]})(e)}function he(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"}}]})(e)}function xe(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}},{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}}]})(e)}function ge(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"}}]})(e)}function ve(e){return Z({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}},{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"}}]})(e)}const je=W.memo(({isOpen:e,onClose:r,reportData:i,patient:o,results:a})=>{var s,t;const[n,l]=W.useState(!1),[c,P]=W.useState(null);W.useEffect(()=>{e&&i?(P(i),l(!1)):e&&o&&a&&y()},[e,i,o,a]);const y=()=>m(null,null,function*(){if(o&&a){l(!0);try{const e=yield ae.generarInformeCompleto(o.id,`Informe BAT-7 - ${o.nombre} ${o.apellido}`,"Informe psicológico completo generado automáticamente");P(e)}catch(e){X.error("Error al generar el informe")}finally{l(!1)}}});if(!e)return null;if(n)return d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxDEV("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:84,columnNumber:13},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Generando informe profesional..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:85,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:83,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:82,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:81,columnNumber:7},void 0);if(!c)return null;const B=(null==(s=c.contenido)?void 0:s.paciente)||c.pacientes||o,_=(null==(t=c.contenido)?void 0:t.resultados)||a||[],M=(()=>{if(0===_.length)return{capacidadGeneral:0,inteligenciaFluida:0,inteligenciaCristalizada:0};const e=_.reduce((e,r)=>{var i;return e+(r.puntaje_pc||r.percentil||(null==(i=r.percentiles)?void 0:i.general)||0)},0),r=Math.round(e/_.length),i=_.find(e=>{var r;return((null==(r=e.aptitudes)?void 0:r.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("verbal")}),o=[_.find(e=>{var r;return((null==(r=e.aptitudes)?void 0:r.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("espacial")}),_.find(e=>{var r;return((null==(r=e.aptitudes)?void 0:r.nombre)||e.aptitud||e.testName||e.test||"").toLowerCase().includes("razonamiento")})].filter(Boolean),a=o.length>0?Math.round(o.reduce((e,r)=>{var i;return e+(r.puntaje_pc||r.percentil||(null==(i=r.percentiles)?void 0:i.general)||0)},0)/o.length):r,s=[i].filter(Boolean);return{capacidadGeneral:r,inteligenciaFluida:a,inteligenciaCristalizada:s.length>0?Math.round(s.reduce((e,r)=>{var i;return e+(r.puntaje_pc||r.percentil||(null==(i=r.percentiles)?void 0:i.general)||0)},0)/s.length):r}})();return d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 print:p-0 print:bg-white",children:d.jsxDEV("div",{className:"informe-modal-professional bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-hidden shadow-2xl print:shadow-none print:max-w-none print:h-auto print:max-h-none print:rounded-none",children:[d.jsxDEV("div",{className:"bg-white border-b-2 border-gray-800 text-gray-800 p-6 print:p-4",children:d.jsxDEV("div",{className:"flex justify-between items-start",children:[d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("div",{className:"flex items-center mb-2",children:[d.jsxDEV(u,{className:"text-2xl text-gray-700 mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:171,columnNumber:17},void 0),d.jsxDEV("h1",{className:"text-3xl font-bold text-gray-900 tracking-tight",children:"INFORME PSICOLÓGICO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:172,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:170,columnNumber:15},void 0),d.jsxDEV("p",{className:"text-gray-600 text-lg font-medium",children:"Batería de Aptitudes Diferenciales y Generales - BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:174,columnNumber:15},void 0),d.jsxDEV("p",{className:"text-gray-500 text-sm mt-1",children:"Evaluación Psicológica Integral"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:175,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:169,columnNumber:13},void 0),d.jsxDEV("div",{className:"flex items-center space-x-2 print:hidden",children:[d.jsxDEV(H,{onClick:()=>{window.print()},variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[d.jsxDEV(p,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:184,columnNumber:17},void 0),"PDF"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:178,columnNumber:15},void 0),d.jsxDEV(H,{onClick:()=>{window.print()},variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:[d.jsxDEV(f,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:193,columnNumber:17},void 0),"Imprimir"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:187,columnNumber:15},void 0),d.jsxDEV(H,{onClick:r,variant:"outline",size:"sm",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:d.jsxDEV(b,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:202,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:196,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:177,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:168,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:167,columnNumber:9},void 0),d.jsxDEV("div",{className:"overflow-y-auto max-h-[calc(95vh-120px)] p-8 print:p-6 space-y-8 bg-gray-50 print:bg-white",children:[d.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[d.jsxDEV("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(N,{className:"text-white text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:216,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-2xl font-bold text-white",children:"Información del Evaluado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:218,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-blue-100 text-sm",children:"Datos personales y demográficos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:219,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:217,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:215,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:214,columnNumber:13},void 0),d.jsxDEV("div",{className:"p-6 print:p-4 bg-gray-50",children:d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[d.jsxDEV("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[d.jsxDEV("div",{className:"flex items-center mb-4",children:[d.jsxDEV(h,{className:"text-blue-600 text-lg mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:229,columnNumber:21},void 0),d.jsxDEV("h3",{className:"text-lg font-semibold text-blue-600",children:"Datos Personales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:230,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:228,columnNumber:19},void 0),d.jsxDEV("div",{className:"space-y-4",children:[d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV(N,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:235,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Nombre Completo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:237,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:[B.nombre," ",B.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:238,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:236,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:234,columnNumber:21},void 0),d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV(h,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:245,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Documento de Identidad:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:247,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:B.documento||B.numero_documento},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:248,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:246,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:244,columnNumber:21},void 0),d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV("div",{className:"w-4 h-4 mt-1 mr-3 flex-shrink-0 rounded-full bg-blue-500 flex items-center justify-center",children:d.jsxDEV("span",{className:"text-white text-xs",children:"♀"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:256,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:255,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Género:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:259,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 capitalize p-2 border border-gray-200 rounded bg-gray-50",children:B.genero},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:260,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:258,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:254,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:233,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:227,columnNumber:17},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[d.jsxDEV("div",{className:"flex items-center mb-4",children:[d.jsxDEV(x,{className:"text-blue-600 text-lg mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:271,columnNumber:21},void 0),d.jsxDEV("h3",{className:"text-lg font-semibold text-blue-600",children:"Datos Demográficos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:272,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:270,columnNumber:19},void 0),d.jsxDEV("div",{className:"space-y-4",children:[d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV(x,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:277,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Fecha de Nacimiento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:279,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:ee(B.fecha_nacimiento)||"viernes, 27 de julio de 2012"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:280,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:278,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:276,columnNumber:21},void 0),d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV("div",{className:"w-4 h-4 mt-1 mr-3 flex-shrink-0 rounded-full bg-blue-500 flex items-center justify-center",children:d.jsxDEV("span",{className:"text-white text-xs",children:"⏳"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:288,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:287,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Edad:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:291,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:B.edad||"13 años"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:292,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:290,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:286,columnNumber:21},void 0),d.jsxDEV("div",{className:"flex items-start",children:[d.jsxDEV(x,{className:"text-blue-500 mt-1 mr-3 flex-shrink-0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:299,columnNumber:23},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("label",{className:"text-sm font-medium text-gray-600",children:"Fecha de Evaluación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:301,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-lg font-semibold text-gray-900 mt-1 p-2 border border-gray-200 rounded bg-gray-50",children:ee(c.fecha_generacion||Date.now())},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:302,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:300,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:298,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:275,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:269,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:225,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:224,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:212,columnNumber:11},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg border border-gray-200 shadow-sm print:shadow-none",children:[d.jsxDEV("div",{className:"bg-gray-50 border-b border-gray-200 px-6 py-4 print:px-4 print:py-3",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(g,{className:"text-gray-700 text-lg mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:317,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900",children:"Resumen General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:319,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-sm text-gray-600",children:"Estadísticas generales de la evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:320,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:318,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:316,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:315,columnNumber:13},void 0),d.jsxDEV("div",{className:"p-6 print:p-4",children:d.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[d.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:_.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:329,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600 font-medium",children:"Tests Completados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:330,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:328,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:327,columnNumber:17},void 0),d.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900 mb-1",children:(()=>{const e=_.reduce((e,r)=>{var i;return e+(r.puntaje_pc||r.percentil||(null==(i=r.percentiles)?void 0:i.general)||0)},0);return _.length>0?(e/_.length).toFixed(1):"0"})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:335,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600 font-medium",children:"Percentil Promedio"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:342,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:334,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:333,columnNumber:17},void 0),d.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-green-700 mb-1",children:_.filter(e=>{var r;return(e.puntaje_pc||e.percentil||(null==(r=e.percentiles)?void 0:r.general)||0)>=75}).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:347,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600 font-medium",children:"Aptitudes Altas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:354,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:346,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:345,columnNumber:17},void 0),d.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-orange-700 mb-1",children:_.filter(e=>{var r;return(e.puntaje_pc||e.percentil||(null==(r=e.percentiles)?void 0:r.general)||0)<50}).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:359,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600 font-medium",children:"A Reforzar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:366,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:358,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:357,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:326,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:325,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:314,columnNumber:11},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[d.jsxDEV("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3",children:d.jsxDEV(g,{className:"text-white text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:379,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:378,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-2xl font-bold text-white",children:"Resultados Gráficos por Aptitud"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:382,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-blue-100 text-sm",children:"Visualización detallada de puntuaciones y niveles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:383,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:381,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:377,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:376,columnNumber:13},void 0),d.jsxDEV("div",{className:"overflow-x-auto",children:0===_.length?d.jsxDEV("div",{className:"p-8 text-center",children:[d.jsxDEV("div",{className:"text-gray-500 text-lg mb-2",children:"No hay datos de evaluación disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:391,columnNumber:19},void 0),d.jsxDEV("div",{className:"text-gray-400 text-sm",children:"Los resultados aparecerán aquí una vez que se completen las evaluaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:392,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:390,columnNumber:17},void 0):d.jsxDEV("table",{className:"w-full",children:[d.jsxDEV("thead",{className:"bg-gray-800 text-white",children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-4 py-3 text-left text-sm font-bold uppercase tracking-wider",children:"APTITUDES EVALUADAS"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:399,columnNumber:23},void 0),d.jsxDEV("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:400,columnNumber:23},void 0),d.jsxDEV("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:401,columnNumber:23},void 0),d.jsxDEV("th",{className:"px-4 py-3 text-center text-sm font-bold uppercase tracking-wider",children:"PERFIL DE LAS APTITUDES"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:402,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:398,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:397,columnNumber:19},void 0),d.jsxDEV("tbody",{className:"bg-white",children:_.map((e,r)=>{var i,o,a,s;const t=e.puntaje_pc||e.percentil||(null==(i=e.percentiles)?void 0:i.general)||0,n=e.puntaje_directo||(null==(o=e.puntajes)?void 0:o.directo)||0,l=((null==(a=e.aptitudes)?void 0:a.nombre)||e.aptitud||e.testName||e.test||"T")[0].toUpperCase(),m=(null==(s=e.aptitudes)?void 0:s.nombre)||e.aptitud||e.testName||e.test||"N/A",c=(u=m,{V:{color:"#2563EB",icon:he,name:"Aptitud Verbal"},E:{color:"#6D28D9",icon:ge,name:"Aptitud Espacial"},A:{color:"#DC2626",icon:ve,name:"Atención"},CON:{color:"#DB2777",icon:D,name:"Concentración"},R:{color:"#D97706",icon:E,name:"Razonamiento"},N:{color:"#0F766E",icon:Ne,name:"Aptitud Numérica"},M:{color:"#374151",icon:xe,name:"Aptitud Mecánica"},O:{color:"#16A34A",icon:be,name:"Ortografía"}}[l]||{color:"#374151",icon:E,name:u});var u;const p=(f=t)>=95?{color:"#8B5CF6",level:"Muy Alto"}:f>=81?{color:"#10B981",level:"Alto"}:f>=61?{color:"#3B82F6",level:"Medio-Alto"}:f>=41?{color:"#6B7280",level:"Medio"}:f>=21?{color:"#F59E0B",level:"Medio-Bajo"}:f>=6?{color:"#F97316",level:"Bajo"}:{color:"#EF4444",level:"Muy Bajo"};var f;const b=c.icon;return d.jsxDEV("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:[d.jsxDEV("td",{className:"px-4 py-4",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0",style:{backgroundColor:c.color},children:d.jsxDEV(b,{className:"text-white text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:487,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:483,columnNumber:31},void 0),d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"text-base font-semibold text-gray-900",children:c.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:490,columnNumber:33},void 0),d.jsxDEV("p",{className:"text-sm text-gray-500",children:l},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:491,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:489,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:482,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:481,columnNumber:27},void 0),d.jsxDEV("td",{className:"px-4 py-4 text-center",children:d.jsxDEV("span",{className:"text-lg font-bold text-gray-900",children:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:496,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:495,columnNumber:27},void 0),d.jsxDEV("td",{className:"px-4 py-4 text-center",children:d.jsxDEV("span",{className:"text-lg font-bold text-gray-900",children:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:499,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:498,columnNumber:27},void 0),d.jsxDEV("td",{className:"px-4 py-4",children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{className:"flex-1 mr-4",children:d.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-6 relative overflow-hidden",children:[d.jsxDEV("div",{className:"h-6 rounded-full flex items-center justify-center text-white text-sm font-bold transition-all duration-300",style:{width:`${Math.max(Math.min(t,100),8)}%`,backgroundColor:p.color},children:t>15&&d.jsxDEV("span",{className:"text-white font-bold",children:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:513,columnNumber:39},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:505,columnNumber:35},void 0),t<=15&&d.jsxDEV("div",{className:"absolute inset-0 flex items-center justify-start pl-2",children:d.jsxDEV("span",{className:"text-xs font-bold text-gray-700",children:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:518,columnNumber:39},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:517,columnNumber:37},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:504,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:503,columnNumber:31},void 0),d.jsxDEV("div",{className:"text-right min-w-[80px]",children:d.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:p.level},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:524,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:523,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:502,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:501,columnNumber:27},void 0)]},r,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:480,columnNumber:25},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:405,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:395,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:388,columnNumber:13},void 0),d.jsxDEV("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200",children:[d.jsxDEV("h4",{className:"text-sm font-semibold text-gray-700 mb-3",children:"Leyenda de Niveles:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:538,columnNumber:15},void 0),d.jsxDEV("div",{className:"flex flex-wrap gap-4 text-xs",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-red-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:541,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Muy bajo (≤5)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:542,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:540,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-orange-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:545,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Bajo (6-20)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:546,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:544,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-yellow-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:549,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Medio-bajo (21-40)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:550,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:548,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-gray-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:553,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Medio (41-60)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:554,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:552,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-blue-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:557,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Medio-alto (61-80)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:558,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:556,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-green-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:561,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Alto (81-95)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:562,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:560,columnNumber:17},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-4 h-4 bg-purple-500 rounded mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:565,columnNumber:19},void 0),d.jsxDEV("span",{className:"font-medium",children:"Muy alto (>95)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:566,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:564,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:539,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:537,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:374,columnNumber:11},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm print:shadow-none overflow-hidden",children:[d.jsxDEV("div",{className:"bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 print:px-4 print:py-3",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3",children:d.jsxDEV(v,{className:"text-white text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:578,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:577,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-2xl font-bold text-white",children:"Análisis Cualitativo Personalizado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:581,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-blue-100 text-sm",children:"Interpretación profesional de aptitudes e índices de inteligencia"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:582,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:580,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:576,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:575,columnNumber:13},void 0),d.jsxDEV("div",{className:"p-6 bg-gray-50",children:[d.jsxDEV("div",{className:"mb-8",children:[d.jsxDEV("div",{className:"flex items-center mb-6",children:[d.jsxDEV(j,{className:"text-purple-600 text-xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:591,columnNumber:19},void 0),d.jsxDEV("h3",{className:"text-xl font-bold text-gray-800",children:"Índices de Inteligencia"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:592,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:590,columnNumber:17},void 0),d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[d.jsxDEV("div",{className:"bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm",children:d.jsxDEV("div",{className:"p-4",children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-3",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3",children:d.jsxDEV(j,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:602,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:601,columnNumber:27},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h4",{className:"font-bold text-gray-900",children:"Capacidad General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:605,columnNumber:29},void 0),d.jsxDEV("p",{className:"text-sm text-gray-600",children:"g"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:606,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:604,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:600,columnNumber:25},void 0),d.jsxDEV("div",{className:"text-right",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900",children:M.capacidadGeneral},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:610,columnNumber:27},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Percentil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:611,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:609,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:599,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:[d.jsxDEV("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:616,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Capacidad General (g) es la estimación más robusta del potencial intelectual global, representando la capacidad fundamental para procesar información, resolver problemas complejos y adaptarse a nuevas situaciones de aprendizaje."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:617,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:615,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:d.jsxDEV("div",{className:"text-white text-center py-2 rounded font-bold text-sm "+(M.capacidadGeneral>=95?"bg-purple-500":M.capacidadGeneral>=81?"bg-green-500":M.capacidadGeneral>=61?"bg-blue-500":M.capacidadGeneral>=41?"bg-gray-500":M.capacidadGeneral>=21?"bg-yellow-500":M.capacidadGeneral>=6?"bg-orange-500":"bg-red-500"),children:M.capacidadGeneral>=95?"Muy Alto":M.capacidadGeneral>=81?"Alto":M.capacidadGeneral>=61?"Medio-Alto":M.capacidadGeneral>=41?"Medio":M.capacidadGeneral>=21?"Medio-Bajo":M.capacidadGeneral>=6?"Bajo":"Muy Bajo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:624,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:623,columnNumber:23},void 0),d.jsxDEV("div",{className:"space-y-3 text-xs",children:[d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:642,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:M.capacidadGeneral>=81?"Presenta un funcionamiento intelectual superior al promedio, con excelente capacidad para procesar información compleja y resolver problemas.":M.capacidadGeneral>=61?"Presenta un funcionamiento intelectual por encima del promedio, con buena capacidad para abordar tareas cognitivas complejas.":M.capacidadGeneral>=41?"Presenta un funcionamiento intelectual dentro del rango promedio, con capacidad adecuada para la mayoría de tareas cognitivas.":M.capacidadGeneral>=21?"Presenta un funcionamiento intelectual ligeramente por debajo del promedio. Puede abordar las tareas cognitivas con esfuerzo adicional y apoyo adecuado.":"Presenta un funcionamiento intelectual por debajo del promedio. Requiere apoyo especializado y estrategias adaptadas para optimizar su potencial."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:643,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:641,columnNumber:25},void 0),d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:658,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:M.capacidadGeneral>=81?"Excelente potencial para el aprendizaje académico y profesional. Puede beneficiarse de programas de enriquecimiento y desafíos intelectuales.":M.capacidadGeneral>=61?"Buen potencial para el rendimiento académico. Puede destacar en áreas que requieran procesamiento cognitivo complejo.":M.capacidadGeneral>=41?"Potencial adecuado para el rendimiento académico estándar. Se beneficia de métodos de enseñanza variados y estructurados.":M.capacidadGeneral>=21?"Con estrategias de estudio apropiadas y apoyo pedagógico puede alcanzar objetivos académicos satisfactorios. Es importante proporcionar múltiples oportunidades de práctica y retroalimentación constructiva.":"Requiere apoyo pedagógico especializado y adaptaciones curriculares. Es fundamental implementar estrategias de enseñanza individualizadas y refuerzo continuo."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:659,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:657,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:640,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:598,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:597,columnNumber:19},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg border-l-4 border-yellow-500 shadow-sm",children:d.jsxDEV("div",{className:"p-4",children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-3",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3",children:d.jsxDEV(V,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:682,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:681,columnNumber:27},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h4",{className:"font-bold text-gray-900",children:"Inteligencia Fluida"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:685,columnNumber:29},void 0),d.jsxDEV("p",{className:"text-sm text-gray-600",children:"Gf"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:686,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:684,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:680,columnNumber:25},void 0),d.jsxDEV("div",{className:"text-right",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900",children:"30"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:690,columnNumber:27},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Percentil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:691,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:689,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:679,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:[d.jsxDEV("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:696,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Inteligencia Fluida (Gf) representa la capacidad para resolver problemas nuevos, pensar de manera lógica e identificar patrones, independientemente del conocimiento previo. Se basa en el Razonamiento, Aptitud Numérica y Aptitud Espacial."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:697,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:695,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:d.jsxDEV("div",{className:"bg-yellow-500 text-white text-center py-2 rounded font-bold text-sm",children:"Medio-Bajo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:705,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:704,columnNumber:23},void 0),d.jsxDEV("div",{className:"space-y-3 text-xs",children:[d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:712,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Presenta un funcionamiento ligeramente por debajo del promedio en Inteligencia fluida. Puede resolver problemas de complejidad moderada con esfuerzo adicional y apoyo adecuado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:713,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:711,columnNumber:25},void 0),d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:720,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Con estrategias apropiadas puede desarrollar competencias de razonamiento satisfactorias. Es importante proporcionar múltiples oportunidades de práctica y retroalimentación constructiva."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:721,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:719,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:710,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:678,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:677,columnNumber:19},void 0),d.jsxDEV("div",{className:"bg-white rounded-lg border-l-4 border-gray-500 shadow-sm",children:d.jsxDEV("div",{className:"p-4",children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-3",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:d.jsxDEV(C,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:736,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:735,columnNumber:27},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h4",{className:"font-bold text-gray-900",children:"Inteligencia Cristalizada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:739,columnNumber:29},void 0),d.jsxDEV("p",{className:"text-sm text-gray-600",children:"Gc"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:740,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:738,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:734,columnNumber:25},void 0),d.jsxDEV("div",{className:"text-right",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900",children:"50"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:744,columnNumber:27},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Percentil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:745,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:743,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:733,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:[d.jsxDEV("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Definición:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:750,columnNumber:25},void 0),d.jsxDEV("p",{className:"text-xs text-gray-600 leading-relaxed",children:"La Inteligencia Cristalizada (Gc) representa el conocimiento adquirido y las habilidades desarrolladas a través de la experiencia y la educación. Se basa en la Aptitud Verbal y la Ortografía, reflejando el aprendizaje cultural acumulado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:751,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:749,columnNumber:23},void 0),d.jsxDEV("div",{className:"mb-3",children:d.jsxDEV("div",{className:"bg-gray-500 text-white text-center py-2 rounded font-bold text-sm",children:"Medio"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:759,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:758,columnNumber:23},void 0),d.jsxDEV("div",{className:"space-y-3 text-xs",children:[d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Interpretación Integrada:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:766,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Demuestra un nivel de conocimientos adquiridos dentro del rango promedio. Posee las competencias académicas básicas y un vocabulario adecuado para su nivel educativo."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:767,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:765,columnNumber:25},void 0),d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Implicaciones Generales:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:774,columnNumber:27},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Presenta las bases de conocimiento necesarias para el éxito académico continuado. Puede beneficiarse del desarrollo sistemático de conocimientos especializados en áreas de interés."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:775,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:773,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:764,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:732,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:731,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:595,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:589,columnNumber:15},void 0),d.jsxDEV("div",{children:[d.jsxDEV("div",{className:"flex items-center mb-6",children:[d.jsxDEV(g,{className:"text-blue-600 text-xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:789,columnNumber:19},void 0),d.jsxDEV("h3",{className:"text-xl font-bold text-gray-800",children:"Interpretación por Aptitudes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:790,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:788,columnNumber:17},void 0),d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:0===_.length?d.jsxDEV("div",{className:"col-span-2 p-8 text-center",children:[d.jsxDEV("div",{className:"text-gray-500 text-lg mb-2",children:"No hay interpretaciones disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:796,columnNumber:23},void 0),d.jsxDEV("div",{className:"text-gray-400 text-sm",children:"Las interpretaciones aparecerán aquí una vez que se completen las evaluaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:797,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:795,columnNumber:21},void 0):_.map((e,r)=>{var i,o,a,s;const t=e.puntaje_pc||e.percentil||(null==(i=e.percentiles)?void 0:i.general)||0,n=e.puntaje_directo||(null==(o=e.puntajes)?void 0:o.directo)||0,l=((null==(a=e.aptitudes)?void 0:a.nombre)||e.aptitud||e.testName||e.test||"T")[0].toUpperCase(),m=(null==(s=e.aptitudes)?void 0:s.nombre)||e.aptitud||e.testName||e.test||"N/A",c=(u=m,{V:{color:"#2563EB",icon:he,name:"Aptitud Verbal",description:"La aptitud verbal evalúa la capacidad para comprender y operar con conceptos expresados verbalmente, incluyendo el manejo del vocabulario, la comprensión de relaciones semánticas y la fluidez en el procesamiento del lenguaje."},E:{color:"#6D28D9",icon:ge,name:"Aptitud Espacial",description:"Capacidad para visualizar y manipular objetos en el espacio, comprender relaciones espaciales y resolver problemas que requieren percepción tridimensional."},A:{color:"#DC2626",icon:ve,name:"Atención",description:"Capacidad para mantener la atención sostenida y concentrarse en tareas específicas durante períodos prolongados."},CON:{color:"#DB2777",icon:D,name:"Concentración",description:"Capacidad para mantener el foco atencional en una tarea específica, resistiendo distracciones internas y externas."},R:{color:"#D97706",icon:E,name:"Razonamiento",description:"Capacidad para el pensamiento lógico, análisis de patrones y resolución de problemas complejos mediante razonamiento abstracto."},N:{color:"#0F766E",icon:Ne,name:"Aptitud Numérica",description:"Capacidad para trabajar con números y conceptos matemáticos, realizar cálculos y resolver problemas cuantitativos."},M:{color:"#374151",icon:xe,name:"Aptitud Mecánica",description:"Comprensión de principios mecánicos y físicos básicos, capacidad para entender el funcionamiento de máquinas y herramientas."},O:{color:"#16A34A",icon:be,name:"Ortografía",description:"Conocimiento de las reglas ortográficas del idioma y capacidad para aplicarlas correctamente en la escritura."}}[l]||{color:"#374151",icon:E,name:u,description:"Descripción de la aptitud evaluada."});var u;const p=(f=t)>=95?{color:"#8B5CF6",level:"Muy Alto"}:f>=81?{color:"#10B981",level:"Alto"}:f>=61?{color:"#3B82F6",level:"Medio-Alto"}:f>=41?{color:"#6B7280",level:"Medio"}:f>=21?{color:"#F59E0B",level:"Medio-Bajo"}:f>=6?{color:"#F97316",level:"Bajo"}:{color:"#EF4444",level:"Muy Bajo"};var f;const b=c.icon;return d.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",style:{borderLeft:`4px solid ${c.color}`},children:d.jsxDEV("div",{className:"p-4",children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-4",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 rounded-full flex items-center justify-center mr-3",style:{backgroundColor:c.color},children:d.jsxDEV(b,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:892,columnNumber:35},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:888,columnNumber:33},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h4",{className:"font-bold text-gray-900",children:c.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:895,columnNumber:35},void 0),d.jsxDEV("p",{className:"text-sm text-gray-600",children:l},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:896,columnNumber:35},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:894,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:887,columnNumber:31},void 0),d.jsxDEV("div",{className:"text-right",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-gray-900",children:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:900,columnNumber:33},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Percentil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:901,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:899,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:886,columnNumber:29},void 0),d.jsxDEV("div",{className:"mb-4",children:[d.jsxDEV("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Descripción:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:907,columnNumber:31},void 0),d.jsxDEV("p",{className:"text-xs text-gray-600 leading-relaxed",children:c.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:908,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:906,columnNumber:29},void 0),d.jsxDEV("div",{className:"mb-4",children:d.jsxDEV("div",{className:"text-white text-center py-2 rounded font-bold text-sm",style:{backgroundColor:p.color},children:p.level},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:915,columnNumber:31},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:914,columnNumber:29},void 0),d.jsxDEV("div",{className:"mb-4",children:[d.jsxDEV("p",{className:"text-sm font-medium text-gray-700 mb-2",children:"Interpretación del Rendimiento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:925,columnNumber:31},void 0),d.jsxDEV("p",{className:"text-xs text-gray-600",children:["Rendimiento en nivel ",p.level," para ",l,". Interpretación específica en desarrollo."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:926,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:924,columnNumber:29},void 0),d.jsxDEV("div",{className:"space-y-3 text-xs",children:[d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Implicaciones Académicas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:934,columnNumber:33},void 0),d.jsxDEV("p",{className:"text-gray-600",children:["Implicaciones académicas para nivel ",p.level," en ",l,". Consulte con el profesional para detalles específicos."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:935,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:933,columnNumber:31},void 0),d.jsxDEV("div",{children:[d.jsxDEV("p",{className:"font-medium text-gray-700",children:"Implicaciones Vocacionales:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:941,columnNumber:33},void 0),d.jsxDEV("p",{className:"text-gray-600",children:["Implicaciones vocacionales para nivel ",p.level," en ",l,". Consulte con el profesional para orientación específica."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:942,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:940,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:932,columnNumber:29},void 0),d.jsxDEV("div",{className:"mt-4 pt-3 border-t border-gray-200 flex justify-between text-xs text-gray-600",children:[d.jsxDEV("div",{children:[d.jsxDEV("span",{className:"font-medium",children:"PD:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:951,columnNumber:33},void 0)," ",n]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:950,columnNumber:31},void 0),d.jsxDEV("div",{children:[d.jsxDEV("span",{className:"font-medium",children:"Errores:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:954,columnNumber:33},void 0)," 2"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:953,columnNumber:31},void 0),d.jsxDEV("div",{children:[d.jsxDEV("span",{className:"font-medium",children:"Tiempo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:957,columnNumber:33},void 0)," 21min"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:956,columnNumber:31},void 0),d.jsxDEV("div",{children:[d.jsxDEV("span",{className:"font-medium",children:"Concentración:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:960,columnNumber:33},void 0)," 93"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:959,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:949,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:884,columnNumber:27},void 0)},r,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:883,columnNumber:25},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:793,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:787,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:587,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:573,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:209,columnNumber:9},void 0),d.jsxDEV("div",{className:"footer bg-gray-50 border-t border-gray-200 p-6 text-center",children:d.jsxDEV("div",{className:"text-sm text-gray-600",children:[d.jsxDEV("p",{className:"mb-2",children:[d.jsxDEV("strong",{children:"Fecha de Generación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:982,columnNumber:15},void 0)," ",re(c.fecha_generacion||Date.now())]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:981,columnNumber:13},void 0),d.jsxDEV("p",{className:"mb-2",children:[d.jsxDEV("strong",{children:"Sistema:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:985,columnNumber:15},void 0)," BAT-7 - Batería de Aptitudes Diferenciales y Generales"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:984,columnNumber:13},void 0),d.jsxDEV("p",{className:"text-xs text-gray-500",children:"Este informe ha sido generado automaticamente por el sistema BAT-7 y contiene informacion confidencial."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:987,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:980,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:979,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:165,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformeModalProfessional.jsx",lineNumber:164,columnNumber:5},void 0)});je.displayName="InformeModalProfessional",je.propTypes={isOpen:ie.bool.isRequired,onClose:ie.func.isRequired,reportData:ie.object,patient:ie.shape({id:ie.string.isRequired,nombre:ie.string.isRequired,apellido:ie.string.isRequired,documento:ie.string,fecha_nacimiento:ie.string,edad:ie.number,genero:ie.string}),results:ie.arrayOf(ie.shape({id:ie.string,aptitud:ie.string,percentil:ie.number,puntuacion_directa:ie.number,tiempo:ie.number,errores:ie.number}))};const Ve=({value:e,label:r,color:i})=>d.jsxDEV("div",{className:"bg-white p-2 rounded-md border border-gray-200 shadow-sm",children:[d.jsxDEV("p",{className:`text-2xl font-bold ${i}`,children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:20,columnNumber:5},void 0),d.jsxDEV("p",{className:"text-xs text-gray-500",children:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:21,columnNumber:5},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:19,columnNumber:3},void 0),Ce=({patientId:e})=>{const[r,i]=W.useState([]),[o,a]=W.useState(!1);W.useEffect(()=>{m(null,null,function*(){if(e){a(!0);try{const r=yield pe.getResultadosByPaciente(e);i(r)}catch(r){}finally{a(!1)}}})},[e]);const s={E:{icon:j,name:"Espacial",bgColor:"bg-purple-500",color:"#6b5bff"},A:{icon:P,name:"Atención",bgColor:"bg-red-500",color:"#e74c3c"},O:{icon:R,name:"Ortografía",bgColor:"bg-green-500",color:"#2ecc71"},V:{icon:S,name:"Verbal",bgColor:"bg-blue-500",color:"#3498db"},N:{icon:w,name:"Numérico",bgColor:"bg-teal-500",color:"#1abc9c"},R:{icon:I,name:"Razonamiento",bgColor:"bg-orange-500",color:"#e67e22"},M:{icon:M,name:"Mecánico",bgColor:"bg-gray-600",color:"#7f8c8d"}};if(o)return d.jsxDEV("div",{className:"animate-pulse",children:[d.jsxDEV("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:61,columnNumber:9},void 0),d.jsxDEV("div",{className:"h-4 bg-gray-200 rounded w-1/2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:62,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:60,columnNumber:7},void 0);if(0===r.length)return d.jsxDEV("div",{className:"text-center text-gray-500 py-4",children:d.jsxDEV("p",{children:"No hay resultados de pruebas disponibles para este paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:70,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:69,columnNumber:7},void 0);return d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"w-full bg-white rounded-lg shadow-sm border border-gray-200",children:[d.jsxDEV("thead",{style:{backgroundColor:"#f6f9ff"},children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"TEST"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:89,columnNumber:13},void 0),d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"PUNTAJE PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:90,columnNumber:13},void 0),d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"PUNTAJE PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:91,columnNumber:13},void 0),d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"ACIERTOS"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:92,columnNumber:13},void 0),d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"ERRORES"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:93,columnNumber:13},void 0),d.jsxDEV("th",{className:"px-4 py-4 text-center text-sm font-bold text-gray-800",children:"TIEMPO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:94,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:88,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:87,columnNumber:9},void 0),d.jsxDEV("tbody",{children:r.map((e,r)=>{var i,o;const a=(null==(i=e.aptitudes)?void 0:i.codigo)||"",t=s[a]||{icon:j,name:"Desconocido",bgColor:"bg-gray-500",color:"#7f8c8d"},n=e.respuestas_correctas||0,l=e.respuestas_incorrectas||0,m=e.tiempo_total||0,c=e.percentil||0,u=e.puntaje_directo||0,p=(e=>e>=80?{level:"Alto",color:"bg-blue-100 text-blue-800",bgColor:"#e6f0ff"}:e>=60?{level:"Medio-Alto",color:"bg-blue-200 text-blue-900",bgColor:"#e6f0ff"}:e>=40?{level:"Medio",color:"bg-blue-100 text-blue-700",bgColor:"#e6f0ff"}:e>=20?{level:"Medio-Bajo",color:"bg-yellow-100 text-yellow-800",bgColor:"#fff9e6"}:{level:"Bajo",color:"bg-red-100 text-red-800",bgColor:"#ffe6e6"})(c);return d.jsxDEV("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",style:{height:"70px"},children:[d.jsxDEV("td",{className:"px-4 py-3",children:d.jsxDEV("div",{className:"flex flex-col items-center justify-center space-y-1",children:[d.jsxDEV("div",{className:`w-12 h-12 rounded-full flex items-center justify-center text-white text-xl font-bold ${t.bgColor}`,style:{backgroundColor:t.color},children:[d.jsxDEV(t.icon,{size:20},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:116,columnNumber:23},void 0),"                    "]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:112,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"text-lg font-bold text-gray-800",children:a},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:118,columnNumber:23},void 0),d.jsxDEV("div",{className:"text-base text-gray-600",children:(null==(o=e.aptitudes)?void 0:o.nombre)||t.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:119,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:117,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:111,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:110,columnNumber:17},void 0),d.jsxDEV("td",{className:"px-4 py-3 text-center",children:d.jsxDEV("span",{className:"inline-flex items-center justify-center px-4 py-2 rounded-full text-lg font-bold text-orange-700",style:{backgroundColor:"#ffe6cc",color:"#ff6600"},children:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:124,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:123,columnNumber:17},void 0),d.jsxDEV("td",{className:"px-4 py-3 text-center",children:d.jsxDEV("div",{className:"flex flex-col items-center space-y-1",children:[d.jsxDEV("span",{className:`inline-flex items-center justify-center px-4 py-2 rounded-full text-lg font-bold ${p.color}`,style:{backgroundColor:p.bgColor},children:c},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:133,columnNumber:21},void 0),d.jsxDEV("span",{className:"text-base text-gray-600",children:p.level},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:139,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:132,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:131,columnNumber:17},void 0),d.jsxDEV("td",{className:"px-4 py-3 text-center",children:d.jsxDEV("div",{className:"flex items-center justify-center space-x-2",children:[d.jsxDEV(A,{className:"text-green-500",size:18},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:144,columnNumber:21},void 0),d.jsxDEV("span",{className:"text-lg font-medium text-green-700",children:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:145,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:143,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:142,columnNumber:17},void 0),d.jsxDEV("td",{className:"px-4 py-3 text-center",children:d.jsxDEV("div",{className:"flex items-center justify-center space-x-2",children:[d.jsxDEV(F,{className:"text-red-500",size:18},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:150,columnNumber:21},void 0),d.jsxDEV("span",{className:"text-lg font-medium text-red-700",children:l},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:151,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:149,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:148,columnNumber:17},void 0),d.jsxDEV("td",{className:"px-4 py-3 text-center",children:d.jsxDEV("div",{className:"flex items-center justify-center space-x-2",children:[d.jsxDEV(k,{className:"text-gray-400",size:18},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:156,columnNumber:21},void 0),d.jsxDEV("span",{className:"text-lg font-medium text-gray-600",children:[Math.round(m/60)," min"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:157,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:155,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:154,columnNumber:17},void 0)]},e.id||r,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:109,columnNumber:15},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:97,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:86,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:85,columnNumber:5},void 0)},Ee=W.memo(({patient:e,results:r,onGenerate:i,onView:o=()=>{},onDelete:a=()=>{}})=>{var s;const[t,c]=W.useState(!1),[p,f]=W.useState(!1),[b,N]=W.useState(null),[h,x]=W.useState(!1),g=r.length,v=r.filter(e=>null!==e.puntaje_directo&&void 0!==e.puntaje_directo&&null!==(e.puntaje_pc||e.percentil)&&void 0!==(e.puntaje_pc||e.percentil)),j=v.map(e=>e.puntaje_directo),V=v.map(e=>e.puntaje_pc||e.percentil),C=j.length>0?Math.round(j.reduce((e,r)=>e+r,0)/j.length):0,E=V.length>0?Math.round(V.reduce((e,r)=>e+r,0)/V.length):0,D=V.filter(e=>e>=75).length,M=V.filter(e=>e<=25).length,I=r.map(e=>{var r;return(null==(r=e.aptitudes)?void 0:r.codigo)||e.test}).filter((e,r,i)=>i.indexOf(e)===r).filter(Boolean),w={female:{card:"bg-gradient-to-br from-pink-50 to-rose-100 border-pink-300",iconBg:"bg-pink-500",iconColor:"text-white",icon:"♀",button:"bg-pink-500 hover:bg-pink-600"},male:{card:"bg-gradient-to-br from-blue-50 to-sky-100 border-blue-300",iconBg:"bg-blue-500",iconColor:"text-white",icon:"♂",button:"bg-blue-500 hover:bg-blue-600"}},S=(null==(s=e.genero)?void 0:s.toLowerCase().startsWith("f"))?w.female:w.male;return d.jsxDEV(d.Fragment,{children:[d.jsxDEV(J,{className:`rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-4 border ${S.card}`,children:[d.jsxDEV("div",{className:"flex justify-between items-start mb-4",children:[d.jsxDEV("div",{className:"flex items-center space-x-3",children:[d.jsxDEV("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${S.iconBg}`,children:d.jsxDEV("span",{className:`text-xl ${S.iconColor}`,children:S.icon},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:290,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:289,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("h3",{className:"text-md font-bold text-gray-800",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:293,columnNumber:15},void 0),d.jsxDEV("p",{className:"text-xs text-gray-500",children:["Documento: ",e.documento||e.numero_documento]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:296,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:292,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:288,columnNumber:11},void 0),d.jsxDEV("div",{className:"flex items-center space-x-2",children:[d.jsxDEV(H,{onClick:()=>m(null,null,function*(){var o;try{if(f(!0),!r||0===r.length)return void X.warning("No hay resultados de evaluación disponibles para generar el informe");const a=yield pe.getResultadosByPaciente(e.id);if(0===a.length)return void X.warning("No se encontraron resultados actualizados para este paciente");const s=yield ae.generarInformeCompleto(e.id,`Informe BAT-7 - ${e.nombre} ${e.apellido}`,`Informe completo de evaluación BAT-7 para ${e.nombre} ${e.apellido} - ${a.length} aptitudes evaluadas`),t=yield ae.obtenerInforme(s),m=l(n({},t),{contenido:l(n({},t.contenido),{paciente:e,resultados:a,estadisticas:{totalTests:a.length,aptitudesEvaluadas:a.map(e=>{var r;return null==(r=e.aptitudes)?void 0:r.codigo}).filter(Boolean),promedioPercentil:Math.round(a.reduce((e,r)=>e+(r.percentil||0),0)/a.length),promedioPuntajeDirecto:Math.round(a.reduce((e,r)=>e+(r.puntaje_directo||0),0)/a.length),aptitudesAltas:a.filter(e=>(e.percentil||0)>=75).length,aptitudesBajas:a.filter(e=>(e.percentil||0)<=25).length,fechaUltimaEvaluacion:null==(o=a[0])?void 0:o.created_at}})});N(m),c(!0),X.success(`Informe generado exitosamente con ${a.length} aptitudes evaluadas`),i&&i(m)}catch(a){X.error("Error al generar el informe: "+(a.message||"Error desconocido"))}finally{f(!1)}}),disabled:p,className:`${S.button} text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm`,children:[d.jsxDEV(u,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:307,columnNumber:15},void 0),p?"...":"Generar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:302,columnNumber:13},void 0),d.jsxDEV(H,{onClick:()=>c(!0),className:`${S.button} text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm`,children:[d.jsxDEV(P,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:314,columnNumber:15},void 0),"Ver"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:310,columnNumber:13},void 0),d.jsxDEV("div",{className:"relative group",children:[d.jsxDEV(H,{onClick:()=>a(e.id,"all"),className:"bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-3 rounded-lg flex items-center text-sm",children:[d.jsxDEV(y,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:322,columnNumber:17},void 0),"Eliminar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:318,columnNumber:15},void 0),d.jsxDEV("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10",children:d.jsxDEV("div",{className:"py-1",children:[d.jsxDEV("button",{onClick:()=>a(e.id,"all"),className:"w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center",children:[d.jsxDEV(y,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:333,columnNumber:21},void 0),"Eliminar todos los registros"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:329,columnNumber:19},void 0),d.jsxDEV("button",{onClick:()=>a(e.id,"single"),className:"w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 flex items-center",children:[d.jsxDEV(y,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:340,columnNumber:21},void 0),"Eliminar último registro"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:336,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:328,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:327,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:317,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:301,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:287,columnNumber:9},void 0),d.jsxDEV("div",{className:"grid grid-cols-5 gap-2 text-center mb-4",children:[d.jsxDEV(Ve,{value:g,label:"Tests",color:"text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:350,columnNumber:11},void 0),d.jsxDEV(Ve,{value:C,label:"Puntaje PD",color:"text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:351,columnNumber:11},void 0),d.jsxDEV(Ve,{value:E,label:"Puntaje PC",color:"text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:352,columnNumber:11},void 0),d.jsxDEV(Ve,{value:D,label:"Aptitud Alta",color:"text-yellow-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:353,columnNumber:11},void 0),d.jsxDEV(Ve,{value:M,label:"Aptitud Bajas",color:"text-red-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:354,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:349,columnNumber:9},void 0),d.jsxDEV("div",{className:"flex items-center mb-3",children:[d.jsxDEV("button",{onClick:()=>x(!h),className:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors p-2 rounded-lg hover:bg-gray-100 mr-3",children:h?d.jsxDEV(B,{className:"text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:362,columnNumber:27},void 0):d.jsxDEV(_,{className:"text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:362,columnNumber:65},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:358,columnNumber:11},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:[d.jsxDEV("span",{className:"font-semibold",children:"Aptitudes evaluadas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:365,columnNumber:13},void 0)," ",I.join(", ")]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:364,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:357,columnNumber:9},void 0),h&&d.jsxDEV("div",{className:"border-t pt-3",children:d.jsxDEV(Ce,{patientId:e.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:371,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:370,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:286,columnNumber:7},void 0),t&&d.jsxDEV(je,{isOpen:t,onClose:()=>c(!1),reportData:b,patient:e,results:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:377,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientCard.jsx",lineNumber:285,columnNumber:5},void 0)}),De=({onSearch:e,onStatsUpdate:r,initialFilters:i={}})=>{const[o,a]=W.useState(n({institution:"",gender:"all",dateFrom:"",dateTo:"",patientName:"",document:"",testStatus:"all",sortBy:"created_at",sortOrder:"desc"},i)),[s,t]=W.useState([]),[c,u]=W.useState(!1),[p,f]=W.useState(!1),[N,h]=W.useState(null),[x,g]=W.useState([]),[v,j]=W.useState(!1);W.useEffect(()=>{V(),C()},[]);const V=()=>m(null,null,function*(){try{const e=yield te.getInstitutions();t(e)}catch(e){X.error("Error al cargar instituciones")}}),C=()=>m(null,null,function*(){try{const e=yield te.getSearchStats();h(e),r&&r(e)}catch(e){}}),E=(e,r)=>{a(i=>l(n({},i),{[e]:r}))},D=(()=>{let e=0;return o.institution&&e++,"all"!==o.gender&&e++,(o.dateFrom||o.dateTo)&&e++,o.patientName.trim()&&e++,o.document.trim()&&e++,"all"!==o.testStatus&&e++,e})();return d.jsxDEV(J,{className:"mb-6",children:[d.jsxDEV(Y,{children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(T,{className:"text-blue-600 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:139,columnNumber:13},void 0),d.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800",children:"Búsqueda Avanzada de Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:140,columnNumber:13},void 0),D>0&&d.jsxDEV("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:[D," filtro",1!==D?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:144,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:138,columnNumber:11},void 0),d.jsxDEV("div",{className:"flex items-center space-x-2",children:[N&&d.jsxDEV("span",{className:"text-sm text-gray-600",children:[N.totalPatients," pacientes con resultados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:151,columnNumber:15},void 0),d.jsxDEV(H,{onClick:()=>u(!c),variant:"outline",size:"sm",className:"flex items-center",children:[d.jsxDEV(z,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:161,columnNumber:15},void 0),c?d.jsxDEV(B,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:162,columnNumber:29},void 0):d.jsxDEV(_,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:162,columnNumber:47},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:155,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:149,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:137,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:136,columnNumber:7},void 0),d.jsxDEV(Q,{children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[d.jsxDEV("div",{className:"relative",children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre del Paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:172,columnNumber:13},void 0),d.jsxDEV("input",{type:"text",placeholder:"Buscar por nombre o apellido...",value:o.patientName,onChange:e=>{return r=e.target.value,m(null,null,function*(){if(E("patientName",r),r.length>=2)try{const e=yield te.getPatientNameSuggestions(r);g(e),j(!0)}catch(e){}else g([]),j(!1)});var r},onFocus:()=>x.length>0&&j(!0),onBlur:()=>setTimeout(()=>j(!1),200),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:175,columnNumber:13},void 0),v&&x.length>0&&d.jsxDEV("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:x.map((e,r)=>d.jsxDEV("button",{onClick:()=>(e=>{E("patientName",e.value),j(!1),g([])})(e),className:"w-full text-left px-3 py-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none",children:[d.jsxDEV("div",{className:"font-medium text-gray-900",children:e.label},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:194,columnNumber:21},void 0),d.jsxDEV("div",{className:"text-sm text-gray-500",children:e.sublabel},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:195,columnNumber:21},void 0)]},r,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:189,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:187,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:171,columnNumber:11},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:203,columnNumber:13},void 0),d.jsxDEV("select",{value:o.institution,onChange:e=>E("institution",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Todas las instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:211,columnNumber:15},void 0),s.map(e=>d.jsxDEV("option",{value:e.id,children:e.nombre},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:213,columnNumber:17},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:206,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:202,columnNumber:11},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Estado de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:221,columnNumber:13},void 0),d.jsxDEV("select",{value:o.testStatus,onChange:e=>E("testStatus",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"all",children:"Todos los estados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:229,columnNumber:15},void 0),d.jsxDEV("option",{value:"completed",children:"Evaluación completa"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:230,columnNumber:15},void 0),d.jsxDEV("option",{value:"partial",children:"Evaluación parcial"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:231,columnNumber:15},void 0),d.jsxDEV("option",{value:"no_tests",children:"Sin evaluaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:232,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:224,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:220,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:170,columnNumber:9},void 0),c&&d.jsxDEV("div",{className:"border-t pt-4 mt-4",children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:242,columnNumber:17},void 0),d.jsxDEV("input",{type:"text",placeholder:"Número de documento",value:o.document,onChange:e=>E("document",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:245,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:241,columnNumber:15},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:255,columnNumber:17},void 0),d.jsxDEV("select",{value:o.gender,onChange:e=>E("gender",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"all",children:"Todos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:263,columnNumber:19},void 0),d.jsxDEV("option",{value:"masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:264,columnNumber:19},void 0),d.jsxDEV("option",{value:"femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:265,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:258,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:254,columnNumber:15},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha Desde"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:270,columnNumber:17},void 0),d.jsxDEV("input",{type:"date",value:o.dateFrom,onChange:e=>E("dateFrom",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:273,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:269,columnNumber:15},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha Hasta"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:282,columnNumber:17},void 0),d.jsxDEV("input",{type:"date",value:o.dateTo,onChange:e=>E("dateTo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:285,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:281,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:240,columnNumber:13},void 0),d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ordenar por"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:296,columnNumber:17},void 0),d.jsxDEV("select",{value:o.sortBy,onChange:e=>E("sortBy",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"created_at",children:"Fecha de registro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:304,columnNumber:19},void 0),d.jsxDEV("option",{value:"name",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:305,columnNumber:19},void 0),d.jsxDEV("option",{value:"apellido",children:"Apellido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:306,columnNumber:19},void 0),d.jsxDEV("option",{value:"documento",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:307,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:299,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:295,columnNumber:15},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Orden"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:312,columnNumber:17},void 0),d.jsxDEV("select",{value:o.sortOrder,onChange:e=>E("sortOrder",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"desc",children:"Descendente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:320,columnNumber:19},void 0),d.jsxDEV("option",{value:"asc",children:"Ascendente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:321,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:315,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:311,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:294,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:239,columnNumber:11},void 0),d.jsxDEV("div",{className:"flex items-center justify-between mt-4 pt-4 border-t",children:[d.jsxDEV("div",{className:"flex items-center space-x-2",children:[d.jsxDEV(H,{onClick:()=>m(null,null,function*(){f(!0);try{e&&(yield e(o))}catch(r){X.error("Error en la búsqueda")}finally{f(!1)}}),disabled:p,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center",children:[d.jsxDEV(T,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:336,columnNumber:15},void 0),p?"Buscando...":"Buscar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:331,columnNumber:13},void 0),d.jsxDEV(H,{onClick:()=>{a({institution:"",gender:"all",dateFrom:"",dateTo:"",patientName:"",document:"",testStatus:"all",sortBy:"created_at",sortOrder:"desc"}),g([]),j(!1)},variant:"outline",className:"flex items-center",children:[d.jsxDEV(b,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:345,columnNumber:15},void 0),"Limpiar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:340,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:330,columnNumber:11},void 0),N&&d.jsxDEV("div",{className:"text-sm text-gray-600",children:[d.jsxDEV("span",{className:"mr-4",children:["Total: ",N.totalPatients," pacientes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:352,columnNumber:15},void 0),d.jsxDEV("span",{className:"mr-4",children:["♂ ",N.genderDistribution.male]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:355,columnNumber:15},void 0),d.jsxDEV("span",{children:["♀ ",N.genderDistribution.female]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:358,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:351,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:329,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:168,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchForm.jsx",lineNumber:135,columnNumber:5},void 0)},Pe=({searchResults:e,isLoading:r,onPatientSelect:i,onBatchGenerate:o,onBatchDelete:a,onViewPatient:s,onPageChange:t,selectedPatients:n=[],onSelectionChange:l})=>{const[m,c]=W.useState(new Set(n)),[p,f]=W.useState(!1);W.useEffect(()=>{c(new Set(n))},[n]),W.useEffect(()=>{if(null==e?void 0:e.patients){const r=e.patients.length>0&&e.patients.every(e=>m.has(e.id));f(r)}},[m,null==e?void 0:e.patients]);const b=e=>{const r=Array.from(m);0!==r.length?"generate"===e&&o?o(r):"delete"===e&&a&&a(r):X.warning("Selecciona al menos un paciente")},N=(e,r)=>{const i={completed:{color:"bg-green-100 text-green-800",text:"Completo",icon:"✓"},partial:{color:"bg-yellow-100 text-yellow-800",text:`${r}% Completo`,icon:"⚡"},no_tests:{color:"bg-gray-100 text-gray-800",text:"Sin Tests",icon:"○"}},o=i[e]||i.no_tests;return d.jsxDEV("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${o.color}`,children:[d.jsxDEV("span",{className:"mr-1",children:o.icon},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:125,columnNumber:9},void 0),o.text]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:124,columnNumber:7},void 0)};if(r)return d.jsxDEV(J,{children:d.jsxDEV(Q,{children:d.jsxDEV("div",{className:"flex items-center justify-center py-12",children:[d.jsxDEV("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:145,columnNumber:13},void 0),d.jsxDEV("span",{className:"text-gray-600",children:"Buscando pacientes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:146,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:144,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:143,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:142,columnNumber:7},void 0);if(!e||0===e.patients.length)return d.jsxDEV(J,{children:d.jsxDEV(Q,{children:d.jsxDEV("div",{className:"text-center py-12",children:[d.jsxDEV($,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:158,columnNumber:13},void 0),d.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No se encontraron pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:159,columnNumber:13},void 0),d.jsxDEV("p",{className:"text-gray-500",children:"Intenta ajustar los filtros de búsqueda para encontrar más resultados."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:162,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:157,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:156,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:155,columnNumber:7},void 0);const{patients:g,total:v,page:j,totalPages:V,hasNextPage:E,hasPrevPage:D}=e,B=m.size;return d.jsxDEV("div",{className:"space-y-4",children:[d.jsxDEV(J,{children:d.jsxDEV(Y,{children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{className:"flex items-center space-x-4",children:[d.jsxDEV("div",{className:"flex items-center",children:d.jsxDEV("button",{onClick:()=>{if(!(null==e?void 0:e.patients))return;const r=new Set;p||e.patients.forEach(e=>{r.add(e.id)}),c(r),f(!p),l&&l(Array.from(r))},className:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900",children:[p?d.jsxDEV(q,{className:"text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:186,columnNumber:32},void 0):d.jsxDEV(G,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:186,columnNumber:78},void 0),d.jsxDEV("span",{className:"ml-2",children:["Seleccionar todos (",g.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:187,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:182,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:181,columnNumber:15},void 0),B>0&&d.jsxDEV("div",{className:"flex items-center space-x-2",children:[d.jsxDEV("span",{className:"text-sm text-gray-600",children:[B," seleccionado",1!==B?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:195,columnNumber:19},void 0),d.jsxDEV(H,{onClick:()=>b("generate"),size:"sm",className:"bg-blue-600 hover:bg-blue-700 text-white",children:[d.jsxDEV(u,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:204,columnNumber:21},void 0),"Generar Informes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:199,columnNumber:19},void 0),d.jsxDEV(H,{onClick:()=>b("delete"),size:"sm",variant:"outline",className:"text-red-600 border-red-300 hover:bg-red-50",children:[d.jsxDEV(y,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:214,columnNumber:21},void 0),"Eliminar Informes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:208,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:194,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:180,columnNumber:13},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:["Mostrando ",20*(j-1)+1,"-",Math.min(20*j,v)," de ",v," resultados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:221,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:179,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:178,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:177,columnNumber:7},void 0),d.jsxDEV("div",{className:"grid grid-cols-1 gap-4",children:g.map(e=>{var r,o,a,t,n,p,f,b;const g=m.has(e.id),v=null==(r=e.genero)?void 0:r.toLowerCase().startsWith("f");return d.jsxDEV(J,{className:"transition-all duration-200 "+(g?"ring-2 ring-blue-500 bg-blue-50":"hover:shadow-md"),children:d.jsxDEV(Q,{className:"p-4",children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{className:"flex items-center space-x-4",children:[d.jsxDEV("button",{onClick:()=>((e,r)=>{const i=new Set(m);r?i.add(e):i.delete(e),c(i),l&&l(Array.from(i))})(e.id,!g),className:"flex-shrink-0",children:g?d.jsxDEV(q,{className:"text-blue-600 text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:253,columnNumber:25},void 0):d.jsxDEV(G,{className:"text-gray-400 text-lg hover:text-gray-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:254,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:248,columnNumber:21},void 0),d.jsxDEV("div",{className:"w-10 h-10 rounded-full flex items-center justify-center "+(v?"bg-pink-100 text-pink-600":"bg-blue-100 text-blue-600"),children:d.jsxDEV("span",{className:"text-lg font-bold",children:v?"♀":"♂"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:262,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:259,columnNumber:21},void 0),d.jsxDEV("div",{className:"flex-1",children:[d.jsxDEV("div",{className:"flex items-center space-x-4 mb-2",children:[d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:270,columnNumber:25},void 0),N(null==(o=e.testSummary)?void 0:o.status,null==(a=e.testSummary)?void 0:a.completionPercentage)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:269,columnNumber:23},void 0),d.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(h,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:281,columnNumber:27},void 0),d.jsxDEV("span",{children:e.documento},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:282,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:280,columnNumber:25},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(C,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:286,columnNumber:27},void 0),d.jsxDEV("span",{children:(null==(t=e.instituciones)?void 0:t.nombre)||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:287,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:285,columnNumber:25},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(x,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:291,columnNumber:27},void 0),d.jsxDEV("span",{children:(j=null==(n=e.testSummary)?void 0:n.lastTestDate,j?new Date(j).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric"}):"N/A")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:292,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:290,columnNumber:25},void 0),d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(u,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:298,columnNumber:27},void 0),d.jsxDEV("span",{children:[(null==(p=e.testSummary)?void 0:p.testCount)||0," test",1!==((null==(f=e.testSummary)?void 0:f.testCount)||0)?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:299,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:297,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:279,columnNumber:23},void 0),(null==(b=e.testSummary)?void 0:b.aptitudes)&&e.testSummary.aptitudes.length>0&&d.jsxDEV("div",{className:"mt-2",children:[d.jsxDEV("span",{className:"text-xs text-gray-500 mr-2",children:"Aptitudes:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:308,columnNumber:27},void 0),d.jsxDEV("div",{className:"inline-flex flex-wrap gap-1",children:e.testSummary.aptitudes.map((e,r)=>d.jsxDEV("span",{className:"inline-block px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded",children:e},r,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:311,columnNumber:31},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:309,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:307,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:268,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:246,columnNumber:19},void 0),d.jsxDEV("div",{className:"flex items-center space-x-2",children:[d.jsxDEV(H,{onClick:()=>s&&s(e.id),size:"sm",variant:"outline",className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:[d.jsxDEV(P,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:332,columnNumber:23},void 0),"Ver"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:326,columnNumber:21},void 0),d.jsxDEV(H,{onClick:()=>i&&i(e),size:"sm",className:"bg-green-600 hover:bg-green-700 text-white",children:[d.jsxDEV(u,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:341,columnNumber:23},void 0),"Generar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:336,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:325,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:244,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:243,columnNumber:15},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:235,columnNumber:13},void 0);var j})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:229,columnNumber:7},void 0),V>1&&d.jsxDEV(J,{children:d.jsxDEV(Q,{children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{className:"text-sm text-gray-600",children:["Página ",j," de ",V]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:357,columnNumber:15},void 0),d.jsxDEV("div",{className:"flex items-center space-x-2",children:[d.jsxDEV(H,{onClick:()=>t&&t(j-1),disabled:!D,variant:"outline",size:"sm",children:[d.jsxDEV(L,{className:"mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:368,columnNumber:19},void 0),"Anterior"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:362,columnNumber:17},void 0),d.jsxDEV("span",{className:"px-3 py-1 bg-gray-100 rounded text-sm",children:j},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:372,columnNumber:17},void 0),d.jsxDEV(H,{onClick:()=>t&&t(j+1),disabled:!E,variant:"outline",size:"sm",children:["Siguiente",d.jsxDEV(O,{className:"ml-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:383,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:376,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:361,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:356,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:355,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:354,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/PatientSearchResults.jsx",lineNumber:175,columnNumber:5},void 0)},ye=()=>{const[e,r]=W.useState([]),[i,o]=W.useState(!0),[a,s]=W.useState(""),[t,u]=W.useState(""),[p,f]=W.useState([]),[b,N]=W.useState(new Set),[h,x]=W.useState("list"),[v,j]=W.useState(null),[V,C]=W.useState(!1),[E,D]=W.useState([]),[P,y]=W.useState(!1),[B,_]=W.useState(null),[M,I]=W.useState(!1),[w,S]=W.useState(null);W.useEffect(()=>{A(),R()},[]);const R=()=>m(null,null,function*(){try{const{data:e,error:r}=yield c.from("aptitudes").select("*").order("codigo");if(r)throw r;f(e||[])}catch(e){X.error("Error al cargar las aptitudes")}}),A=()=>m(null,null,function*(){try{o(!0);const{data:e,error:i}=yield c.from("resultados").select("\n          id,\n          puntaje_directo,\n          percentil,\n          errores,\n          tiempo_segundos,\n          concentracion,\n          created_at,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          ),\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").order("created_at",{ascending:!1});if(i)return void X.error("Error al cargar los resultados");const a=new Map;e.forEach(e=>{var r,i,o,s;const t=`${null==(r=e.pacientes)?void 0:r.id}-${null==(i=e.aptitudes)?void 0:i.codigo}`;if(!(null==(o=e.pacientes)?void 0:o.id)||!(null==(s=e.aptitudes)?void 0:s.codigo))return;const n=a.get(t);(!n||new Date(e.created_at)>new Date(n.created_at))&&a.set(t,e)});const s=Array.from(a.values()).reduce((e,r)=>{var i,o,a;const s=null==(i=r.pacientes)?void 0:i.id;if(!s)return e;e[s]||(e[s]={paciente:r.pacientes,resultados:[],fechaUltimaEvaluacion:r.created_at});const t=r.percentil?oe.obtenerInterpretacionPC(r.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[s].resultados.push({id:r.id,test:(null==(o=r.aptitudes)?void 0:o.codigo)||"N/A",testName:(null==(a=r.aptitudes)?void 0:a.nombre)||"Test Desconocido",puntajePD:r.puntaje_directo||0,puntajePC:r.percentil||"N/A",errores:r.errores||0,tiempo:r.tiempo_segundos?`${Math.round(r.tiempo_segundos/60)}:${String(r.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:r.concentracion?`${r.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(r.created_at).toLocaleDateString("es-ES"),interpretacion:t.nivel,interpretacionColor:t.color,interpretacionBg:t.bg}),new Date(r.created_at)>new Date(e[s].fechaUltimaEvaluacion)&&(e[s].fechaUltimaEvaluacion=r.created_at),e},{}),t=Object.values(s).sort((e,r)=>new Date(r.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));r(t),o(!1)}catch(e){X.error("Error al cargar los resultados"),o(!1)}}),F=e.filter(e=>{const r=e.paciente;return!!(!a||(null==r?void 0:r.nombre)&&r.nombre.toLowerCase().includes(a.toLowerCase())||(null==r?void 0:r.apellido)&&r.apellido.toLowerCase().includes(a.toLowerCase())||(null==r?void 0:r.documento)&&r.documento.toLowerCase().includes(a.toLowerCase()))&&(!t||e.resultados.some(e=>e.test===t))}),k=(e,...r)=>m(null,[e,...r],function*(e,r={page:1,limit:20}){try{C(!0);const i=yield te.searchPatients(e,r);j(l(n({},i),{currentFilters:e}))}catch(i){X.error("Error en la búsqueda: "+i.message)}finally{C(!1)}}),z=(e,r="all")=>m(null,null,function*(){try{const i=yield se.deletePatientReports(e,r);i.success?(X.success(i.message),"search"===h&&v?yield k(v.currentFilters):yield A()):X.warning(i.message)}catch(i){X.error("Error al eliminar los registros")}});return d.jsxDEV("div",{children:[d.jsxDEV(K,{title:"Gestión de Informes BAT-7",subtitle:"Busca pacientes, genera informes individuales o masivos, y gestiona reportes",icon:g},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:392,columnNumber:7},void 0),d.jsxDEV("div",{className:"container mx-auto px-4 py-8",children:[d.jsxDEV("div",{className:"mb-6",children:d.jsxDEV("div",{className:"flex items-center space-x-2 bg-white rounded-lg p-1 shadow-sm border border-gray-200 w-fit",children:[d.jsxDEV(H,{onClick:()=>x("list"),variant:"list"===h?"primary":"outline",size:"sm",className:"flex items-center",children:[d.jsxDEV(U,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:409,columnNumber:15},void 0),"Vista Lista"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:403,columnNumber:13},void 0),d.jsxDEV(H,{onClick:()=>x("search"),variant:"search"===h?"primary":"outline",size:"sm",className:"flex items-center",children:[d.jsxDEV(T,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:418,columnNumber:15},void 0),"Búsqueda Avanzada"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:412,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:402,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:401,columnNumber:9},void 0),P&&B&&d.jsxDEV(J,{className:"mb-6",children:d.jsxDEV(Q,{children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-2",children:[d.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:"Procesando informes en lote..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:429,columnNumber:17},void 0),d.jsxDEV("span",{className:"text-sm text-gray-500",children:[B.current," de ",B.total]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:432,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:428,columnNumber:15},void 0),d.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:d.jsxDEV("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${B.percentage}%`}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:437,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:436,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:427,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:426,columnNumber:11},void 0),"search"===h&&d.jsxDEV("div",{className:"space-y-6",children:[d.jsxDEV(De,{onSearch:k,onStatsUpdate:e=>{}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:449,columnNumber:13},void 0),d.jsxDEV(Pe,{searchResults:v,isLoading:V,selectedPatients:E,onSelectionChange:e=>{D(e)},onBatchGenerate:e=>m(null,null,function*(){if(e&&0!==e.length)try{y(!0),_({current:0,total:e.length,percentage:0});const r=yield fe.generateBatchReports(e,e=>{_(e)});_(null),r.successful.length>0&&X.success(`${r.successful.length} informe(s) generado(s) exitosamente`),r.failed.length>0&&X.warning(`${r.failed.length} informe(s) fallaron al generarse`),D([])}catch(r){X.error("Error en la generación masiva: "+r.message)}finally{y(!1),_(null)}else X.warning("Selecciona al menos un paciente")}),onBatchDelete:e=>m(null,null,function*(){if(e&&0!==e.length){if(confirm(`¿Estás seguro de que deseas eliminar los informes de ${e.length} paciente(s) seleccionado(s)?`))try{const r=yield se.batchDeleteReports(e);r.success?(X.success(r.message),D([]),"search"===h&&v?yield k(v.currentFilters):yield A()):X.warning(r.message)}catch(r){X.error("Error en la eliminación masiva")}}else X.warning("Selecciona al menos un paciente")}),onViewPatient:e=>{let r=null,i=[];if("search"===h&&v){const o=v.patients.find(r=>r.id===e);o&&(r=o,i=o.resultados||[])}else{const o=F.find(r=>r.paciente.id===e);o&&(r=o.paciente,i=o.resultados.map(e=>({id:e.id,aptitudes:{codigo:e.test,nombre:e.testName},puntaje_directo:e.puntajePD,percentil:e.puntajePC,puntaje_pc:e.puntajePC,errores:e.errores,tiempo_segundos:e.tiempo,created_at:e.fecha,test:e.test})))}r&&i.length>0?(S({patient:r,results:i}),I(!0)):X.warning("No se encontraron datos del paciente")},onPageChange:e=>m(null,null,function*(){(null==v?void 0:v.currentFilters)&&(yield k(v.currentFilters,{page:e,limit:20}))}),onPatientSelect:e=>{S({patient:e,results:e.resultados||[]}),I(!0)}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:454,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:448,columnNumber:11},void 0),"list"===h&&d.jsxDEV("div",{children:[d.jsxDEV(J,{className:"mb-6",children:[d.jsxDEV(Y,{children:d.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800",children:[d.jsxDEV("i",{className:"fas fa-filter mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:479,columnNumber:13},void 0),"Filtros de Búsqueda"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:478,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:477,columnNumber:9},void 0),d.jsxDEV(Q,{children:d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Buscar Paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:487,columnNumber:15},void 0),d.jsxDEV("div",{className:"relative",children:[d.jsxDEV("input",{type:"text",placeholder:"Nombre, apellido o documento...",value:a,onChange:e=>s(e.target.value),className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:491,columnNumber:17},void 0),d.jsxDEV("i",{className:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:498,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:490,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:486,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filtrar por Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:504,columnNumber:15},void 0),d.jsxDEV("select",{value:t,onChange:e=>u(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Todos los tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:512,columnNumber:17},void 0),p.map(e=>d.jsxDEV("option",{value:e.codigo,children:[e.codigo," - ",e.nombre]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:514,columnNumber:19},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:507,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:503,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:484,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:483,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:476,columnNumber:7},void 0),d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[d.jsxDEV(J,{children:d.jsxDEV(Q,{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:e.reduce((e,r)=>e+r.resultados.filter(e=>null!==e.puntajePD&&void 0!==e.puntajePD&&null!==e.puntajePC&&void 0!==e.puntajePC&&"N/A"!==e.puntajePC).length,0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:528,columnNumber:13},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Resultados Válidos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:536,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:527,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:526,columnNumber:9},void 0),d.jsxDEV(J,{children:d.jsxDEV(Q,{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>e.resultados.length>0).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:541,columnNumber:13},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Pacientes con Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:544,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:540,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:539,columnNumber:9},void 0),d.jsxDEV(J,{children:d.jsxDEV(Q,{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-purple-600",children:(()=>{const r=new Set;return e.forEach(e=>{e.resultados.forEach(e=>{e.test&&null!==e.puntajePD&&void 0!==e.puntajePD&&r.add(e.test)})}),r.size})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:549,columnNumber:13},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Aptitudes Contestadas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:562,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:548,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:547,columnNumber:9},void 0),d.jsxDEV(J,{children:d.jsxDEV(Q,{className:"text-center",children:[d.jsxDEV("div",{className:"text-2xl font-bold text-orange-600",children:(()=>{const r=e.reduce((e,r)=>{const i=r.resultados.filter(e=>null!==e.puntajePC&&void 0!==e.puntajePC&&"N/A"!==e.puntajePC&&"number"==typeof e.puntajePC);return e.concat(i)},[]);if(0===r.length)return 0;const i=r.reduce((e,r)=>e+r.puntajePC,0);return Math.round(i/r.length)})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:567,columnNumber:13},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:"Promedio PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:582,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:566,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:565,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:525,columnNumber:7},void 0),d.jsxDEV("div",{className:"flex justify-between items-center mb-6",children:[d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-2xl font-bold text-blue-800",children:[d.jsxDEV("i",{className:"fas fa-chart-line mr-3 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:591,columnNumber:13},void 0),"Resultados Detallados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:590,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-600 mt-1",children:[F.length," paciente",1!==F.length?"s":""," con resultados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:594,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:589,columnNumber:9},void 0),F.length>0&&d.jsxDEV("div",{className:"flex items-center space-x-3",children:[d.jsxDEV("div",{className:"text-sm text-gray-500 mr-2",children:[b.size," de ",F.length," expandidos"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:602,columnNumber:13},void 0),d.jsxDEV(H,{onClick:()=>{const e=new Set(F.map(e=>e.paciente.id));N(e)},variant:"outline",size:"sm",className:"text-green-600 border-green-300 hover:bg-green-50",children:[d.jsxDEV("i",{className:"fas fa-expand-arrows-alt mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:611,columnNumber:15},void 0),"Expandir Todo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:605,columnNumber:13},void 0),d.jsxDEV(H,{onClick:()=>{N(new Set)},variant:"outline",size:"sm",className:"text-orange-600 border-orange-300 hover:bg-orange-50",children:[d.jsxDEV("i",{className:"fas fa-compress-arrows-alt mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:620,columnNumber:15},void 0),"Contraer Todo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:614,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:601,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:588,columnNumber:7},void 0),i?d.jsxDEV("div",{className:"py-16 text-center",children:[d.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:628,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-500",children:"Cargando resultados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:629,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:627,columnNumber:9},void 0):d.jsxDEV(d.Fragment,{children:0===F.length?d.jsxDEV(J,{children:d.jsxDEV(Q,{children:d.jsxDEV("div",{className:"py-8 text-center",children:[d.jsxDEV("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:637,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:638,columnNumber:19},void 0),d.jsxDEV("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:639,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:636,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:635,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:634,columnNumber:13},void 0):d.jsxDEV("div",{className:"grid grid-cols-1 gap-4",children:F.map(e=>d.jsxDEV(Ee,{patient:{id:e.paciente.id,nombre:e.paciente.nombre,apellido:e.paciente.apellido,documento:e.paciente.documento,genero:e.paciente.genero},results:e.resultados.map(e=>({id:e.id,aptitudes:{codigo:e.test,nombre:e.testName},puntaje_directo:e.puntajePD,percentil:e.puntajePC,puntaje_pc:e.puntajePC,errores:e.errores,tiempo_segundos:e.tiempo,created_at:e.fecha,test:e.test})),onGenerate:e=>{X.success("Informe generado exitosamente")},onView:e=>{X.info('Funcionalidad de ver reporte disponible en el botón "Ver" de cada tarjeta')},onDelete:z},e.paciente.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:648,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:646,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:632,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:473,columnNumber:11},void 0),M&&w&&d.jsxDEV(je,{isOpen:M,onClose:()=>{I(!1),S(null)},patient:w.patient,results:w.results},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:691,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:398,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Reports.jsx",lineNumber:390,columnNumber:5},void 0)};export{ye as default};
