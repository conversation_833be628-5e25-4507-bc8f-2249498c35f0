{"name": "sistema-gestion-psicologica", "private": true, "version": "0.1.0", "type": "module", "homepage": "https://activa-tumente.github.io/Bat-7-Version-fial-para-github", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox", "e2e": "start-server-and-test dev http://localhost:5173 cypress:run", "e2e:open": "start-server-and-test dev http://localhost:5173 cypress:open", "test:all": "npm run test:run && npm run e2e"}, "dependencies": {"@heroicons/react": "^2.2.0", "@playwright/test": "^1.54.2", "@reduxjs/toolkit": "^2.8.0", "@supabase/supabase-js": "^2.33.1", "dotenv": "^17.2.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "pg": "^8.16.3", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.10.1", "react-redux": "^9.2.0", "react-router-dom": "^6.23.1", "react-toastify": "^9.1.3", "react-window": "^1.8.11", "recharts": "^2.15.3", "yup": "^1.6.1"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@rollup/rollup-win32-x64-msvc": "^4.46.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.15", "cypress": "^14.5.1", "gh-pages": "^6.3.0", "jsdom": "^26.1.0", "msw": "^2.10.4", "postcss": "^8.4.28", "start-server-and-test": "^2.0.12", "tailwindcss": "^3.3.3", "terser": "^5.43.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}