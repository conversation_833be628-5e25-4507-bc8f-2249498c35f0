var e=(e,a,i)=>new Promise((s,r)=>{var o=e=>{try{t(i.next(e))}catch(a){r(a)}},n=e=>{try{t(i.throw(e))}catch(a){r(a)}},t=e=>e.done?s(e.value):Promise.resolve(e.value).then(o,n);t((i=i.apply(e,a)).next())});import{j as a,H as i,D as s,P as r,N as o,u as n,F as t,i as l,h as m,b as d,w as c,s as u}from"./auth-Cw5QfmsP.js";import{r as b}from"./react-vendor-C9XH6RF0.js";import{e as N}from"./enhancedSupabaseService-l8rh_Z40.js";import{P as h,p as x}from"./ImprovedPinControlService-XBKYJtn2.js";import"./ui-vendor-COFtXQcG.js";class g{getDisplayInfo(e){throw new Error("getDisplayInfo must be implemented by subclass")}}class f extends g{getDisplayInfo(e){return{color:"bg-green-100 text-green-800 border-green-200",icon:"FaInfinity",text:"Pines Ilimitados",showProgress:!1,severity:"success"}}}class p extends g{getDisplayInfo(e){const a=e.remainingPins<=h.THRESHOLDS.LOW_PIN_WARNING;return{color:a?"bg-yellow-100 text-yellow-800 border-yellow-200":"bg-blue-100 text-blue-800 border-blue-200",icon:"FaCoins",text:`${e.remainingPins} Pines`,showProgress:!0,severity:a?"warning":"info"}}}class v extends g{getDisplayInfo(e){return{color:"bg-red-100 text-red-800 border-red-200",icon:"FaExclamationTriangle",text:"Sin Pines",showProgress:!1,severity:"error"}}}class j{static createStrategy(e){return e?e.isUnlimited?new f:e.canUse?new p:new v:new v}}const V=({psychologistId:n,className:t=""})=>{const[l,m]=b.useState(null),[d,c]=b.useState(!0),[u,N]=b.useState(!1);b.useEffect(()=>{n&&h()},[n,h]);const h=b.useCallback(()=>e(null,null,function*(){try{c(!0);const e=yield x.checkPsychologistUsage(n);m(e)}catch(e){}finally{c(!1)}}),[n]),g=b.useMemo(()=>{if(d||!l)return null;return j.createStrategy(l).getDisplayInfo(l)},[d,l]),f=b.useMemo(()=>{if(!g)return null;switch(g.icon){case"FaInfinity":return a.jsxDEV(r,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:45,columnNumber:16},void 0);case"FaExclamationTriangle":return a.jsxDEV(s,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:47,columnNumber:16},void 0);default:return a.jsxDEV(i,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:49,columnNumber:16},void 0)}},[g]);return!d&&l&&g?a.jsxDEV("div",{className:`relative ${t}`,children:[a.jsxDEV("div",{className:`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border cursor-pointer transition-all ${g.color}`,onClick:()=>N(!u),children:[f,a.jsxDEV("span",{className:"text-sm font-medium",children:g.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:64,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:59,columnNumber:7},void 0),u&&a.jsxDEV("div",{className:"absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50",children:[a.jsxDEV("div",{className:"flex items-center justify-between mb-3",children:[a.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Estado de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:70,columnNumber:13},void 0),a.jsxDEV("button",{onClick:()=>N(!1),className:"text-gray-400 hover:text-gray-600",children:a.jsxDEV(o,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:75,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:71,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:69,columnNumber:11},void 0),a.jsxDEV("div",{className:"space-y-3",children:[a.jsxDEV("div",{className:"flex justify-between items-center",children:[a.jsxDEV("span",{className:"text-sm text-gray-600",children:"Estado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:81,columnNumber:15},void 0),a.jsxDEV("span",{className:"text-sm font-medium "+(l.canUse?"text-green-600":"text-red-600"),children:l.reason},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:82,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:80,columnNumber:13},void 0),!l.isUnlimited&&a.jsxDEV(a.Fragment,{children:[a.jsxDEV("div",{className:"flex justify-between items-center",children:[a.jsxDEV("span",{className:"text-sm text-gray-600",children:"Pines Totales:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:92,columnNumber:19},void 0),a.jsxDEV("span",{className:"text-sm font-medium text-gray-900",children:l.totalPins||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:93,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:91,columnNumber:17},void 0),a.jsxDEV("div",{className:"flex justify-between items-center",children:[a.jsxDEV("span",{className:"text-sm text-gray-600",children:"Pines Usados:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:99,columnNumber:19},void 0),a.jsxDEV("span",{className:"text-sm font-medium text-gray-900",children:l.usedPins||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:100,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:98,columnNumber:17},void 0),a.jsxDEV("div",{className:"flex justify-between items-center",children:[a.jsxDEV("span",{className:"text-sm text-gray-600",children:"Pines Restantes:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:106,columnNumber:19},void 0),a.jsxDEV("span",{className:"text-sm font-medium text-gray-900",children:l.remainingPins||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:107,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:105,columnNumber:17},void 0),l.totalPins>0&&a.jsxDEV("div",{className:"mt-3",children:[a.jsxDEV("div",{className:"flex justify-between text-sm text-gray-600 mb-1",children:[a.jsxDEV("span",{children:"Progreso de uso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:115,columnNumber:23},void 0),a.jsxDEV("span",{children:[Math.round((l.usedPins||0)/l.totalPins*100),"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:116,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:114,columnNumber:21},void 0),a.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:a.jsxDEV("div",{className:"h-2 rounded-full transition-all "+(l.remainingPins<=5?"bg-red-500":l.remainingPins<=10?"bg-yellow-500":"bg-blue-500"),style:{width:`${Math.min((l.usedPins||0)/l.totalPins*100,100)}%`}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:119,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:118,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:113,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:90,columnNumber:15},void 0),l.isUnlimited&&a.jsxDEV("div",{className:"text-center py-2",children:[a.jsxDEV(r,{className:"w-8 h-8 text-green-500 mx-auto mb-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:136,columnNumber:17},void 0),a.jsxDEV("p",{className:"text-sm text-gray-600",children:"Tienes acceso ilimitado al sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:137,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:135,columnNumber:15},void 0),!l.canUse&&a.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mt-3",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV(s,{className:"w-4 h-4 text-red-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:146,columnNumber:19},void 0),a.jsxDEV("p",{className:"text-sm text-red-700",children:"Contacta al administrador para obtener más pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:147,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:145,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:144,columnNumber:15},void 0),l.canUse&&l.remainingPins<=5&&!l.isUnlimited&&a.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV(s,{className:"w-4 h-4 text-yellow-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:157,columnNumber:19},void 0),a.jsxDEV("p",{className:"text-sm text-yellow-700",children:"Quedan pocos pines disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:158,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:156,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:155,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:79,columnNumber:11},void 0),a.jsxDEV("div",{className:"mt-4 pt-3 border-t border-gray-200",children:a.jsxDEV("button",{onClick:h,className:"w-full px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Actualizar Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:167,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:166,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:68,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/PinStatusIndicator.jsx",lineNumber:58,columnNumber:5},void 0):null},D=()=>{const{user:i,isAdmin:s}=n(),[r,o]=b.useState({totalPatients:0,totalPsychologists:0,totalInstitutions:0,totalPins:0,usedPins:0,activePsychologists:0,psychologistsWithLowPins:0}),[h,x]=b.useState(null),[g,f]=b.useState(!0);return b.useEffect(()=>{e(null,null,function*(){f(!0);try{const[e,a,r,n]=yield Promise.all([N.getPatients(),N.getPsychologists(),N.getInstitutions(),s?u.rpc("get_all_psychologists_with_stats"):u.rpc("get_psychologist_pin_stats_optimized")]),t=Array.isArray(e.data)?e.data.length:0,l=Array.isArray(a.data)?a.data.length:0,m=Array.isArray(r.data)?r.data.length:0;let d=0,c=0,b=0,h=0;if(s&&n.data)d=n.data.reduce((e,a)=>e+(a.total_uses||0),0),c=n.data.reduce((e,a)=>e+(a.used_uses||0),0),b=n.data.filter(e=>e.total_uses>0).length,h=n.data.filter(e=>{const a=e.total_uses-e.used_uses;return a<=5&&a>0}).length;else if(n.data){const e=n.data.find(e=>e.psychologist_id===(null==i?void 0:i.id));e&&(d=e.total_uses||0,c=e.used_uses||0,x(e))}o({totalPatients:t,totalPsychologists:l,totalInstitutions:m,totalPins:d,usedPins:c,activePsychologists:b,psychologistsWithLowPins:h})}catch(e){}finally{f(!1)}})},[s,null==i?void 0:i.id]),a.jsxDEV("div",{className:"min-h-screen bg-gray-50",children:a.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsxDEV("div",{className:"mb-8",children:[a.jsxDEV("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:92,columnNumber:11},void 0),a.jsxDEV("p",{className:"mt-2 text-sm text-gray-600",children:["Bienvenido ",(null==i?void 0:i.full_name)||"al sistema"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:93,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:91,columnNumber:9},void 0),g?a.jsxDEV("div",{className:"flex justify-center items-center p-12",children:a.jsxDEV(t,{className:"animate-spin text-blue-600 text-3xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:100,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:99,columnNumber:11},void 0):a.jsxDEV(a.Fragment,{children:[a.jsxDEV("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:[a.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsxDEV("div",{className:"p-5",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"flex-shrink-0 bg-blue-500 rounded-md p-3",children:a.jsxDEV(l,{className:"h-6 w-6 text-white"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:110,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:109,columnNumber:21},void 0),a.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:a.jsxDEV("dl",{children:[a.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:114,columnNumber:25},void 0),a.jsxDEV("dd",{children:a.jsxDEV("div",{className:"text-lg font-medium text-gray-900",children:r.totalPatients},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:116,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:115,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:113,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:112,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:108,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:107,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:106,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsxDEV("div",{className:"p-5",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"flex-shrink-0 bg-green-500 rounded-md p-3",children:a.jsxDEV(m,{className:"h-6 w-6 text-white"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:128,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:127,columnNumber:21},void 0),a.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:a.jsxDEV("dl",{children:[a.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:132,columnNumber:25},void 0),a.jsxDEV("dd",{children:a.jsxDEV("div",{className:"text-lg font-medium text-gray-900",children:r.totalPsychologists},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:134,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:133,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:131,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:130,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:126,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:125,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:124,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsxDEV("div",{className:"p-5",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"flex-shrink-0 bg-purple-500 rounded-md p-3",children:a.jsxDEV(d,{className:"h-6 w-6 text-white"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:146,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:145,columnNumber:21},void 0),a.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:a.jsxDEV("dl",{children:[a.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:150,columnNumber:25},void 0),a.jsxDEV("dd",{children:a.jsxDEV("div",{className:"text-lg font-medium text-gray-900",children:r.totalInstitutions},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:152,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:151,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:149,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:148,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:144,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:143,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:142,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsxDEV("div",{className:"p-5",children:a.jsxDEV("div",{className:"flex items-center",children:[a.jsxDEV("div",{className:"flex-shrink-0 bg-yellow-500 rounded-md p-3",children:a.jsxDEV(c,{className:"h-6 w-6 text-white"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:164,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:163,columnNumber:21},void 0),a.jsxDEV("div",{className:"ml-5 w-0 flex-1",children:a.jsxDEV("dl",{children:[a.jsxDEV("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Evaluaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:168,columnNumber:25},void 0),a.jsxDEV("dd",{children:a.jsxDEV("div",{className:"text-lg font-medium text-gray-900",children:r.totalPins},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:170,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:169,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:167,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:166,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:162,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:161,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:160,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:105,columnNumber:13},void 0),s?a.jsxDEV("div",{className:"mb-8",children:a.jsxDEV("div",{className:"bg-white overflow-hidden shadow rounded-lg p-6",children:[a.jsxDEV("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Estado de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:183,columnNumber:19},void 0),a.jsxDEV(V,{psychologistId:null==i?void 0:i.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:184,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:182,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:181,columnNumber:15},void 0):a.jsxDEV("div",{className:"bg-white shadow rounded-lg p-6 mb-8",children:[a.jsxDEV("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Estado de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:189,columnNumber:17},void 0),a.jsxDEV(V,{psychologistId:null==i?void 0:i.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:190,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:188,columnNumber:15},void 0),a.jsxDEV("div",{className:"bg-white shadow rounded-lg p-6",children:[a.jsxDEV("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Acciones Rápidas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:196,columnNumber:15},void 0),a.jsxDEV("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[a.jsxDEV("a",{href:"/patients",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[a.jsxDEV(l,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:202,columnNumber:19},void 0),a.jsxDEV("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:203,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:198,columnNumber:17},void 0),a.jsxDEV("a",{href:"/evaluations",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[a.jsxDEV(c,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:209,columnNumber:19},void 0),a.jsxDEV("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Evaluaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:210,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:205,columnNumber:17},void 0),a.jsxDEV("a",{href:"/reports",className:"relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[a.jsxDEV(m,{className:"mx-auto h-12 w-12 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:216,columnNumber:19},void 0),a.jsxDEV("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Ver Reportes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:217,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:212,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:197,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:195,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:103,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:90,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Dashboard.jsx",lineNumber:89,columnNumber:5},void 0)};export{D as default};
