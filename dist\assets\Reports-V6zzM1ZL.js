import{j as e}from"./auth-Cw5QfmsP.js";import"./react-vendor-C9XH6RF0.js";import{C as s,b as a,a as i}from"./admin-COBm5LhQ.js";import"./ui-vendor-COFtXQcG.js";const r=()=>e.jsxDEV("div",{className:"container mx-auto py-6",children:[e.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Reportes de Estudiantes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:7,columnNumber:7},void 0),e.jsxDEV(s,{children:[e.jsxDEV(a,{children:e.jsxDEV("h2",{className:"text-lg font-medium",children:"Panel de Reportes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:11,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:10,columnNumber:9},void 0),e.jsxDEV(i,{children:e.jsxDEV("p",{className:"text-gray-600",children:"Esta sección permitirá visualizar reportes y estadísticas de los estudiantes asignados (componente en desarrollo)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:14,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:13,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:9,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/professional/Reports.jsx",lineNumber:6,columnNumber:5},void 0);export{r as default};
