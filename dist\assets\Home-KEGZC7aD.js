import{j as e}from"./auth-Cw5QfmsP.js";import"./react-vendor-C9XH6RF0.js";import{C as i,a,b as s}from"./admin-COBm5LhQ.js";import{g as o}from"./index-OwywlZoY.js";import"./ui-vendor-COFtXQcG.js";const r=()=>e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"mb-8",children:e.jsxDEV(i,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:scale-[1.02]",children:e.jsxDEV(a,{className:"p-0",children:e.jsxDEV("div",{className:"relative w-full h-[350px] overflow-hidden rounded-lg",children:[e.jsxDEV("div",{className:"absolute inset-0",children:[e.jsxDEV("img",{src:o("banner.png"),alt:"BAT-7 Evaluación de Aptitudes",className:"w-full h-full object-cover transition-transform duration-700 hover:scale-105"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:15,columnNumber:17},void 0),e.jsxDEV("div",{className:"absolute inset-0 opacity-30",children:[e.jsxDEV("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/30 to-cyan-500/20 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:23,columnNumber:19},void 0),e.jsxDEV("div",{className:"absolute inset-0 bg-gradient-to-l from-indigo-500/20 via-pink-500/20 to-blue-500/30 banner-shimmer"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:24,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:22,columnNumber:17},void 0),e.jsxDEV("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-purple-600/10 banner-glow"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:28,columnNumber:17},void 0),e.jsxDEV("div",{className:"absolute inset-0 overflow-hidden",children:e.jsxDEV("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:32,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:31,columnNumber:17},void 0),e.jsxDEV("div",{className:"absolute inset-0 overflow-hidden opacity-40",children:[e.jsxDEV("div",{className:"absolute top-10 left-10 w-2 h-2 bg-cyan-300 rounded-full animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:37,columnNumber:19},void 0),e.jsxDEV("div",{className:"absolute top-20 right-20 w-1 h-1 bg-yellow-300 rounded-full animate-ping"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:38,columnNumber:19},void 0),e.jsxDEV("div",{className:"absolute bottom-20 left-1/4 w-3 h-3 bg-pink-300 rounded-full animate-bounce"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:39,columnNumber:19},void 0),e.jsxDEV("div",{className:"absolute bottom-10 right-1/3 w-2 h-2 bg-green-300 rounded-full animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:40,columnNumber:19},void 0),e.jsxDEV("div",{className:"absolute top-1/2 left-1/2 w-1 h-1 bg-purple-300 rounded-full animate-ping"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:41,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:36,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:14,columnNumber:15},void 0),e.jsxDEV("div",{className:"relative z-10 flex items-center justify-center h-full",children:e.jsxDEV("div",{className:"absolute bottom-6 right-6 flex space-x-2 opacity-70",children:[e.jsxDEV("div",{className:"w-2 h-2 bg-cyan-400 rounded-full animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:49,columnNumber:19},void 0),e.jsxDEV("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-100"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:50,columnNumber:19},void 0),e.jsxDEV("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-200"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:51,columnNumber:19},void 0),e.jsxDEV("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-300"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:52,columnNumber:19},void 0),e.jsxDEV("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse delay-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:53,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:48,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:46,columnNumber:15},void 0),e.jsxDEV("div",{className:"absolute inset-0 opacity-30",children:e.jsxDEV("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:59,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:58,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:12,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:11,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:10,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:9,columnNumber:7},void 0),e.jsxDEV("div",{className:"mb-8",children:e.jsxDEV(i,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[e.jsxDEV(s,{className:"bg-gradient-to-r from-[#31375a] to-[#31375a] border-b border-blue-300",children:e.jsxDEV("h3",{className:"text-xl font-semibold text-white text-center py-2 flex items-center justify-center",children:[e.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:71,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:70,columnNumber:15},void 0),"Descripción"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:69,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:68,columnNumber:11},void 0),e.jsxDEV(a,{children:e.jsxDEV("p",{className:"text-gray-700 mb-4 text-center leading-relaxed px-4 py-2",children:"A través de esta plataforma digital, podrás realizar la prueba de forma segura y eficiente. El objetivo es obtener una visión integral de tus fortalezas y potencial, asegurando un proceso de selección equitativo y orientado a identificar a los candidatos mejor preparados para los desafíos y oportunidades que ofrece cada institución."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:77,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:76,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:67,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:66,columnNumber:7},void 0),e.jsxDEV("div",{className:"mb-8",children:e.jsxDEV(i,{className:"shadow-md rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[e.jsxDEV(s,{className:"bg-gradient-to-r from-[#31375a] to-[#31375a] border-b border-green-300",children:e.jsxDEV("h3",{className:"text-xl font-semibold text-white text-center py-2 flex items-center justify-center",children:[e.jsxDEV("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:91,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:90,columnNumber:15},void 0),"Aptitudes evaluadas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:89,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:88,columnNumber:11},void 0),e.jsxDEV(a,{children:e.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"V"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:101,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:100,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:103,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Comprensión y razonamiento verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:104,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:99,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"E"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:109,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:108,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Espacial"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:111,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Visualización y orientación espacial"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:112,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:107,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:117,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:116,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Atención"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:119,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Capacidad de atención sostenida"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:120,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:115,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-red-100 flex items-center justify-center text-red-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"R"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:126,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:125,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Razonamiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:128,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Pensamiento lógico y analítico"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:129,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:124,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"N"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:134,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:133,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Numérica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:136,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Habilidades matemáticas y cálculo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:137,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:132,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"M"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:142,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:141,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Mecánica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:144,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Comprensión de principios mecánicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:145,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:140,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex flex-col items-center text-center",children:[e.jsxDEV("div",{className:"w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600 mb-2",children:e.jsxDEV("span",{className:"font-semibold",children:"O"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:150,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:149,columnNumber:17},void 0),e.jsxDEV("span",{className:"font-medium",children:"Ortografía"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:152,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:"Corrección ortográfica y lingüística"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:153,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:148,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:97,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:96,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:87,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:86,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/home/<USER>",lineNumber:8,columnNumber:5},void 0);export{r as default};
