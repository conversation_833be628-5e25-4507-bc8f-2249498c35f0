var e=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,t=(a,i,n)=>i in a?e(a,i,{enumerable:!0,configurable:!0,writable:!0,value:n}):a[i]=n,c=(e,a)=>{for(var i in a||(a={}))r.call(a,i)&&t(e,i,a[i]);if(n)for(var i of n(a))o.call(a,i)&&t(e,i,a[i]);return e},s=(e,n)=>a(e,i(n)),d=(e,a,i)=>new Promise((n,r)=>{var o=e=>{try{c(i.next(e))}catch(a){r(a)}},t=e=>{try{c(i.throw(e))}catch(a){r(a)}},c=e=>e.done?n(e.value):Promise.resolve(e.value).then(o,t);c((i=i.apply(e,a)).next())});import{s as l}from"./auth-Cw5QfmsP.js";import{Q as m}from"./ui-vendor-COFtXQcG.js";import{c as u}from"./index-OwywlZoY.js";import{p}from"./ImprovedPinControlService-XBKYJtn2.js";import"./react-vendor-C9XH6RF0.js";import"./admin-COBm5LhQ.js";const f={obtenerNivelPorPercentil:e=>e<=5?{id:1,nombre:"Muy Bajo",color:"text-red-600"}:e<=20?{id:2,nombre:"Bajo",color:"text-red-500"}:e<=40?{id:3,nombre:"Medio-Bajo",color:"text-orange-500"}:e<=60?{id:4,nombre:"Medio",color:"text-yellow-600"}:e<=80?{id:5,nombre:"Medio-Alto",color:"text-blue-500"}:e<=95?{id:6,nombre:"Alto",color:"text-green-600"}:{id:7,nombre:"Muy Alto",color:"text-green-700"},interpretaciones:{V:{1:{rendimiento:"Presenta dificultades significativas en la comprensión y manejo de conceptos verbales.",academico:"Puede presentar dificultades en asignaturas como Lengua y Literatura, Historia, Filosofía.",vocacional:"Se beneficiaría de actividades profesionales que no dependan del procesamiento verbal complejo."},2:{rendimiento:"Muestra un rendimiento por debajo del promedio en tareas verbales.",academico:"Requiere apoyo adicional en materias con alta carga verbal.",vocacional:"Puede desenvolverse en profesiones que combinen habilidades verbales básicas con otras competencias."},3:{rendimiento:"Demuestra una capacidad verbal ligeramente por debajo del promedio.",academico:"Puede manejar contenido verbal básico pero requiere apoyo en textos complejos.",vocacional:"Adecuado para profesiones con demandas verbales moderadas."},4:{rendimiento:"Demuestra una capacidad verbal dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias con componente verbal.",vocacional:"Posee las competencias verbales necesarias para una amplia gama de profesiones."},5:{rendimiento:"Muestra una capacidad verbal por encima del promedio.",academico:"Destaca en materias que requieren comprensión verbal y puede abordar textos complejos.",vocacional:"Tiene potencial para profesiones que requieren habilidades verbales avanzadas."},6:{rendimiento:"Demuestra una capacidad verbal superior.",academico:"Destaca significativamente en materias verbales y puede manejar contenido académico avanzado.",vocacional:"Posee las competencias para destacar en profesiones altamente verbales."},7:{rendimiento:"Presenta una capacidad verbal excepcional.",academico:"Puede destacar significativamente en todas las materias con componente verbal.",vocacional:"Posee el potencial para sobresalir en las profesiones más exigentes verbalmente."}},E:{4:{rendimiento:"Demuestra una capacidad espacial dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias con componente espacial.",vocacional:"Posee las competencias espaciales necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad espacial por encima del promedio.",academico:"Destaca en materias que requieren visualización espacial.",vocacional:"Tiene potencial para profesiones técnicas y de diseño."},6:{rendimiento:"Demuestra una capacidad espacial superior.",academico:"Destaca en matemáticas, física y materias técnicas.",vocacional:"Excelente para ingeniería, arquitectura y diseño."}},R:{4:{rendimiento:"Demuestra una capacidad de razonamiento dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias que requieren razonamiento.",vocacional:"Posee las competencias de razonamiento necesarias para una amplia gama de profesiones."},5:{rendimiento:"Muestra una capacidad de razonamiento por encima del promedio.",academico:"Destaca en materias que requieren análisis lógico.",vocacional:"Tiene potencial para profesiones analíticas y de resolución de problemas."},6:{rendimiento:"Demuestra una capacidad de razonamiento superior.",academico:"Destaca en materias que requieren razonamiento complejo.",vocacional:"Excelente para investigación, análisis y consultoría."}},N:{4:{rendimiento:"Demuestra una capacidad numérica dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en matemáticas.",vocacional:"Posee las competencias numéricas necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad numérica por encima del promedio.",academico:"Destaca en matemáticas y materias con componente numérico.",vocacional:"Tiene potencial para profesiones que requieren habilidades matemáticas."},6:{rendimiento:"Demuestra una capacidad numérica superior.",academico:"Destaca significativamente en matemáticas y ciencias exactas.",vocacional:"Excelente para ingeniería, finanzas y ciencias."}},A:{4:{rendimiento:"Demuestra una capacidad atencional dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en tareas que requieren concentración.",vocacional:"Posee las competencias atencionales necesarias para diversas profesiones."},5:{rendimiento:"Muestra una capacidad atencional por encima del promedio.",academico:"Destaca en tareas que requieren concentración sostenida.",vocacional:"Tiene potencial para profesiones que demandan alta concentración."},6:{rendimiento:"Demuestra una capacidad atencional superior.",academico:"Destaca en todas las actividades académicas que requieren concentración.",vocacional:"Excelente para profesiones de precisión y control de calidad."}},M:{4:{rendimiento:"Demuestra una comprensión mecánica dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en materias técnicas.",vocacional:"Posee las competencias mecánicas necesarias para diversas profesiones técnicas."},5:{rendimiento:"Muestra una comprensión mecánica por encima del promedio.",academico:"Destaca en materias técnicas y de ciencias aplicadas.",vocacional:"Tiene potencial para profesiones de ingeniería y tecnología."},6:{rendimiento:"Demuestra una comprensión mecánica superior.",academico:"Destaca significativamente en todas las materias técnicas.",vocacional:"Excelente para ingeniería mecánica y desarrollo tecnológico."}},O:{4:{rendimiento:"Demuestra un conocimiento ortográfico dentro del rango promedio.",academico:"Presenta un rendimiento satisfactorio en escritura y comunicación.",vocacional:"Posee las competencias ortográficas necesarias para diversas profesiones."},5:{rendimiento:"Muestra un conocimiento ortográfico por encima del promedio.",academico:"Destaca en materias de lengua y comunicación escrita.",vocacional:"Tiene potencial para profesiones que requieren comunicación escrita precisa."},6:{rendimiento:"Demuestra un conocimiento ortográfico superior.",academico:"Destaca significativamente en todas las materias de comunicación.",vocacional:"Excelente para periodismo, edición y comunicación profesional."}}},obtenerInterpretacionAptitud:(e,a)=>{var i;const n=f.obtenerNivelPorPercentil(a),r=null==(i=f.interpretaciones[e])?void 0:i[n.id];return r?{nivel_nombre:n.nombre,rendimiento:r.rendimiento,academico:r.academico,vocacional:r.vocacional}:{nivel_nombre:n.nombre,rendimiento:"Interpretación no disponible para este nivel.",academico:"Se requiere evaluación adicional.",vocacional:"Consulte con un profesional para orientación específica."}}};class v{static obtenerNivelPorPercentil(e){return d(this,null,function*(){try{return f.obtenerNivelPorPercentil(e)}catch(a){throw a}})}static obtenerInterpretacionAptitud(e,a){return d(this,null,function*(){try{return f.obtenerInterpretacionAptitud(e,a)}catch(i){throw i}})}static obtenerInterpretacionIndice(e,a){return d(this,null,function*(){try{const{data:i,error:n}=yield l.rpc("obtener_interpretacion_indice",{p_indice_codigo:e,p_percentil:a});if(n)throw n;return i}catch(i){throw i}})}static obtenerInterpretacionesMultiples(e){return d(this,null,function*(){try{return yield Promise.all(e.map(e=>d(this,null,function*(){const a=yield this.obtenerInterpretacionAptitud(e.aptitud_codigo,e.percentil);return s(c({},e),{interpretacion:a})})))}catch(a){throw a}})}static generarResumenCualitativo(e){return d(this,arguments,function*(e,a=[]){try{const i=yield this.obtenerInterpretacionesMultiples(e),n=yield Promise.all(a.map(e=>d(this,null,function*(){const a=yield this.obtenerInterpretacionIndice(e.indice_codigo,e.percentil);return s(c({},e),{interpretacion:a})}))),r=i.filter(e=>e.percentil>=75).map(e=>{var a,i;return{aptitud:e.aptitud_codigo,percentil:e.percentil,nivel:null==(a=e.interpretacion)?void 0:a.nivel_nombre,descripcion:null==(i=e.interpretacion)?void 0:i.rendimiento}}),o=i.filter(e=>e.percentil<=25).map(e=>{var a,i;return{aptitud:e.aptitud_codigo,percentil:e.percentil,nivel:null==(a=e.interpretacion)?void 0:a.nivel_nombre,descripcion:null==(i=e.interpretacion)?void 0:i.rendimiento}}),t=this._generarRecomendacionesAcademicas(i);return{interpretacionesAptitudes:i,interpretacionesIndices:n,fortalezas:r,debilidades:o,recomendacionesAcademicas:t,orientacionVocacional:this._generarOrientacionVocacional(i),resumenGeneral:this._generarResumenGeneral(i,n)}}catch(i){throw i}})}static _generarRecomendacionesAcademicas(e){const a=[];return e.forEach(e=>{var i;(null==(i=e.interpretacion)?void 0:i.academico)&&a.push({aptitud:e.aptitud_codigo,nivel:e.interpretacion.nivel_nombre,recomendacion:e.interpretacion.academico})}),a}static _generarOrientacionVocacional(e){const a=[];return e.forEach(e=>{var i;(null==(i=e.interpretacion)?void 0:i.vocacional)&&a.push({aptitud:e.aptitud_codigo,nivel:e.interpretacion.nivel_nombre,orientacion:e.interpretacion.vocacional})}),a}static _generarResumenGeneral(e,a){const i=e.length>0?e.reduce((e,a)=>e+a.percentil,0)/e.length:50;let n="Promedio";i>=85?n="Superior":i>=75?n="Por encima del promedio":i<=15?n="Por debajo del promedio":i<=25&&(n="Ligeramente por debajo del promedio");const r=e.filter(e=>e.percentil>=75).map(e=>e.aptitud_codigo),o=e.filter(e=>e.percentil<=25).map(e=>e.aptitud_codigo);return{nivelGeneral:n,promedioPercentil:Math.round(i),aptitudesDestacadas:r,aptitudesAMejorar:o,perfilCognitivo:this._determinarPerfilCognitivo(e)}}static _determinarPerfilCognitivo(e){const a={verbal:["V","O"],numerico:["N","R"],espacial:["E","M"],atencion:["A"]},i={};Object.keys(a).forEach(n=>{const r=e.filter(e=>a[n].includes(e.aptitud_codigo));r.length>0&&(i[n]=r.reduce((e,a)=>e+a.percentil,0)/r.length)});const n=Object.keys(i);return{perfilDominante:n.length>0?n.reduce((e,a)=>i[e]>i[a]?e:a):"equilibrado",promedios:i}}static obtenerAptitudes(){return d(this,null,function*(){try{const{data:e,error:a}=yield l.from("aptitudes_interpretacion").select("*").order("codigo");if(a)throw a;return e}catch(e){throw e}})}static obtenerIndices(){return d(this,null,function*(){try{const{data:e,error:a}=yield l.from("indices_inteligencia").select("*").order("codigo");if(a)throw a;return e}catch(e){throw e}})}static obtenerNiveles(){return d(this,null,function*(){try{const{data:e,error:a}=yield l.from("niveles_rendimiento").select("*").order("percentil_minimo");if(a)throw a;return e}catch(e){throw e}})}}const h={obtenerInformesPaciente(e){return d(this,null,function*(){try{const{data:a,error:i}=yield l.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento\n          )\n        ").eq("paciente_id",e).order("fecha_generacion",{ascending:!1});if(i)throw i;return a||[]}catch(a){throw m.error("Error al cargar los informes del paciente"),a}})},obtenerInforme(e){return d(this,null,function*(){try{const{data:a,error:i}=yield l.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento,\n            genero,\n            institucion_id\n          )\n        ").eq("id",e).single();if(i)throw i;return a&&a.pacientes&&a.pacientes.fecha_nacimiento&&(a.pacientes.edad=u(a.pacientes.fecha_nacimiento)),a}catch(a){throw m.error("Error al cargar el informe"),a}})},generarInformeCompleto(e,a=null,i=null,n=!0){return d(this,null,function*(){try{const{data:t,error:c}=yield l.from("pacientes").select("*").eq("id",e).single();if(c)throw c;t&&t.fecha_nacimiento&&(t.edad=u(t.fecha_nacimiento));const{data:s,error:d}=yield l.from("resultados").select("*").eq("paciente_id",e).order("created_at",{ascending:!1});if(d)throw d;let f=null;if(n&&s&&s.length>0)try{f=yield this._generarInterpretacionesCualitativas(s)}catch(r){}const v={paciente:t,resultados:s||[],estadisticas:this._calcularEstadisticas(s||[]),evaluacion:this._generarEvaluacion(s||[]),interpretacionesCualitativas:f},{data:h,error:g}=yield l.from("informes_generados").insert({paciente_id:e,tipo_informe:"completo",titulo:a||`Informe Completo - ${t.nombre} ${t.apellido}`,descripcion:i||"Informe completo de evaluación psicológica",contenido:v,estado:"generado",fecha_generacion:(new Date).toISOString(),metadatos:{version:"2.0",generado_por:"sistema",total_resultados:(null==s?void 0:s.length)||0,incluye_interpretaciones:!!f}}).select().single();if(g)throw g;try{if(t.psicologo_id){const{data:a,error:i}=yield l.from("test_sessions").select("id, paciente_id, fecha_fin").eq("paciente_id",e).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1}).limit(1).single();a&&(yield p.consumePin(t.psicologo_id,e,a.id,h.id),yield l.from("test_sessions").update({pin_consumed_at:(new Date).toISOString()}).eq("id",a.id),m.info("Se ha consumido 1 pin para generar el informe."))}}catch(o){if(o.message.includes("No hay pines disponibles")||o.message.includes("Sin pines"))throw yield l.from("informes_generados").delete().eq("id",h.id),new Error(`No se puede generar el informe: ${o.message}`)}return m.success("Informe completo generado exitosamente"),h.id}catch(r){throw m.error("Error al generar el informe completo"),r}})},generarInformeIndividual(e,a=null,i=null){return d(this,null,function*(){try{const{data:r,error:o}=yield l.from("resultados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento,\n            fecha_nacimiento,\n            genero,\n            institucion\n          )\n        ").eq("id",e).single();if(o)throw o;r&&r.pacientes&&r.pacientes.fecha_nacimiento&&(r.pacientes.edad=u(r.pacientes.fecha_nacimiento));const t={paciente:r.pacientes,resultados:[r],estadisticas:this._calcularEstadisticas([r]),evaluacion:this._generarEvaluacion([r])},{data:c,error:s}=yield l.from("informes_generados").insert({paciente_id:r.paciente_id,tipo_informe:"individual",titulo:a||`Informe Individual - ${r.pacientes.nombre} ${r.pacientes.apellido}`,descripcion:i||"Informe individual de evaluación psicológica",contenido:t,estado:"generado",fecha_generacion:(new Date).toISOString(),metadatos:{version:"1.0",generado_por:"sistema",resultado_id:e}}).select().single();if(s)throw s;try{const{data:e,error:a}=yield l.from("pacientes").select("psicologo_id").eq("id",r.paciente_id).single();if(!a&&(null==e?void 0:e.psicologo_id)){const{data:a,error:i}=yield l.from("test_sessions").select("id, paciente_id, fecha_fin").eq("paciente_id",r.paciente_id).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1}).limit(1).single();a&&(yield p.consumePin(e.psicologo_id,r.paciente_id,a.id,c.id),yield l.from("test_sessions").update({pin_consumed_at:(new Date).toISOString()}).eq("id",a.id),m.info("Se ha consumido 1 pin para generar el informe."))}}catch(n){if(n.message.includes("No hay pines disponibles")||n.message.includes("Sin pines"))throw yield l.from("informes_generados").delete().eq("id",c.id),new Error(`No se puede generar el informe: ${n.message}`)}return m.success("Informe individual generado exitosamente"),c.id}catch(r){throw m.error("Error al generar el informe individual"),r}})},archivarInforme(e){return d(this,null,function*(){try{const{error:a}=yield l.from("informes_generados").update({estado:"archivado",fecha_archivado:(new Date).toISOString()}).eq("id",e);if(a)throw a;m.success("Informe archivado exitosamente")}catch(a){throw m.error("Error al archivar el informe"),a}})},eliminarInforme(e){return d(this,null,function*(){try{const{error:a}=yield l.from("informes_generados").delete().eq("id",e);if(a)throw a;m.success("Informe eliminado exitosamente")}catch(a){throw m.error("Error al eliminar el informe"),a}})},obtenerTodosLosInformes(){return d(this,null,function*(){try{const{data:e,error:a}=yield l.from("informes_generados").select("\n          *,\n          pacientes (\n            id,\n            nombre,\n            apellido,\n            documento\n          )\n        ").order("fecha_generacion",{ascending:!1});if(a)throw a;return e||[]}catch(e){throw m.error("Error al cargar los informes"),e}})},_calcularEstadisticas(e){var a,i;if(!e||0===e.length)return{total_evaluaciones:0,promedio_percentiles:{},aptitudes_destacadas:[],areas_mejora:[]};const n={};["verbal","espacial","atencion_concentracion","razonamiento","numerica","mecanica","ortografia"].forEach(a=>{const i=e.map(e=>{var i;return null==(i=e.percentiles)?void 0:i[a]}).filter(e=>null!=e&&!isNaN(e));i.length>0&&(n[a]=i.reduce((e,a)=>e+a,0)/i.length,i.length)});const r=Object.entries(n).filter(([e,a])=>a>75).map(([e,a])=>({aptitud:e,promedio:Math.round(a)})),o=Object.entries(n).filter(([e,a])=>a<25).map(([e,a])=>({aptitud:e,promedio:Math.round(a)}));return{total_evaluaciones:e.length,promedio_percentiles:Object.fromEntries(Object.entries(n).map(([e,a])=>[e,Math.round(a)])),aptitudes_destacadas:r,areas_mejora:o,fecha_primera_evaluacion:null==(a=e[e.length-1])?void 0:a.fecha_evaluacion,fecha_ultima_evaluacion:null==(i=e[0])?void 0:i.fecha_evaluacion}},_generarInterpretacionesCualitativas(e){return d(this,null,function*(){try{const a=e.map(e=>{var a;return{aptitud_codigo:e.aptitud_id,percentil:(null==(a=e.percentiles)?void 0:a.verbal)||e.percentil,puntaje_directo:e.puntaje_directo,interpretacion:e.interpretacion}});return yield v.generarResumenCualitativo(a)}catch(a){throw a}})},_generarEvaluacion(e){if(!e||0===e.length)return{resumen:"No hay resultados disponibles para evaluar.",recomendaciones:[],observaciones:[]};const a=this._calcularEstadisticas(e),i=[],n=[];a.aptitudes_destacadas.length>0&&i.push(`Potenciar las aptitudes destacadas: ${a.aptitudes_destacadas.map(e=>e.aptitud).join(", ")}`),a.areas_mejora.length>0&&i.push(`Trabajar en las áreas de mejora: ${a.areas_mejora.map(e=>e.aptitud).join(", ")}`),e.length>1&&n.push("Se observa un historial de evaluaciones que permite analizar la evolución.");return{resumen:`Evaluación basada en ${a.total_evaluaciones} evaluación${1!==a.total_evaluaciones?"es":""}. \n${a.aptitudes_destacadas.length>0?`Aptitudes destacadas: ${a.aptitudes_destacadas.length}. `:""}\n${a.areas_mejora.length>0?`Áreas de mejora identificadas: ${a.areas_mejora.length}.`:""}`.trim(),recomendaciones:i,observaciones:n}},generarInformeAutomatico(e,a=null){return d(this,null,function*(){try{const{data:a,error:i}=yield l.from("informes_generados").select("id").eq("paciente_id",e).eq("tipo_informe","completo").eq("estado","generado").single();if(i&&"PGRST116"!==i.code)throw i;if(a)return null;const{data:n,error:r}=yield l.from("pacientes").select("*").eq("id",e).single();if(r)throw r;return yield this.generarInformeCompleto(e,`Informe Automático BAT-7 - ${n.nombre} ${n.apellido}`,"Informe generado automáticamente al completar la evaluación")}catch(a){return null}})},necesitaInformeAutomatico(e){return d(this,null,function*(){try{const{data:a,error:i}=yield l.from("resultados").select("id").eq("paciente_id",e);if(i)throw i;if(!a||0===a.length)return!1;const{data:n,error:r}=yield l.from("informes_generados").select("id").eq("paciente_id",e).eq("tipo_informe","completo").eq("estado","generado").single();if(r&&"PGRST116"!==r.code)throw r;return!n}catch(a){return!1}})}};export{h as default};
