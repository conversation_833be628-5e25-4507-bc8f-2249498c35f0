var e=Object.defineProperty,i=Object.defineProperties,a=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(i,a,n)=>a in i?e(i,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[a]=n,s=(e,i)=>{for(var a in i||(i={}))r.call(i,a)&&o(e,a,i[a]);if(n)for(var a of n(i))t.call(i,a)&&o(e,a,i[a]);return e},l=(e,n)=>i(e,a(n)),m=(e,i,a)=>new Promise((n,r)=>{var t=e=>{try{s(a.next(e))}catch(i){r(i)}},o=e=>{try{s(a.throw(e))}catch(i){r(i)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(t,o);s((a=a.apply(e,i)).next())});import{j as d,K as c,N as u,p as b,O as p,F as N,i as x,r as f,a as g,c as v,d as h,s as j}from"./auth-BzDSP4i9.js";import{r as V}from"./react-vendor-C9XH6RF0.js";import{Q as C}from"./ui-vendor-COFtXQcG.js";import{P as y}from"./admin-BucOs87s.js";const P=({isOpen:e,onClose:i,patient:a,onSave:n,title:r="Editar Paciente",psychologists:t=[],institutions:o=[]})=>{const[p,N]=V.useState({nombre:"",apellido:"",email:"",documento:"",genero:"",fecha_nacimiento:"",nivel_educativo:"",ocupacion:"",psicologo_id:"",institucion_id:""}),[x,f]=V.useState({}),[g,v]=V.useState(!1);V.useEffect(()=>{N(a?{nombre:a.nombre||"",apellido:a.apellido||"",email:a.email||"",documento:a.documento||"",genero:a.genero||"",fecha_nacimiento:a.fecha_nacimiento||"",nivel_educativo:a.nivel_educativo||"",ocupacion:a.ocupacion||"",psicologo_id:a.psicologo_id||"",institucion_id:a.institucion_id||""}:{nombre:"",apellido:"",email:"",documento:"",genero:"",fecha_nacimiento:"",nivel_educativo:"",ocupacion:"",psicologo_id:"",institucion_id:""}),f({})},[a,e]);const h=e=>{const{name:i,value:a}=e.target;N(e=>l(s({},e),{[i]:a})),x[i]&&f(e=>l(s({},e),{[i]:""}))};return e?d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:d.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[d.jsxDEV("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(c,{className:"text-blue-600 mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:124,columnNumber:13},void 0),d.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900",children:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:125,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:123,columnNumber:11},void 0),d.jsxDEV("button",{onClick:i,className:"text-gray-400 hover:text-gray-600 transition-colors",children:d.jsxDEV(u,{size:20},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:131,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:127,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:122,columnNumber:9},void 0),d.jsxDEV("form",{onSubmit:e=>m(null,null,function*(){if(e.preventDefault(),(()=>{const e={};return p.nombre.trim()||(e.nombre="El nombre es requerido"),p.apellido.trim()||(e.apellido="El apellido es requerido"),p.email.trim()&&!/\S+@\S+\.\S+/.test(p.email)&&(e.email="El email no es válido"),f(e),0===Object.keys(e).length})()){v(!0);try{yield n(p),i()}catch(a){}finally{v(!1)}}}),className:"p-6",children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:140,columnNumber:15},void 0),d.jsxDEV("input",{type:"text",name:"nombre",value:p.nombre,onChange:h,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(x.nombre?"border-red-500":"border-gray-300"),placeholder:"Ingrese el nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:143,columnNumber:15},void 0),x.nombre&&d.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:x.nombre},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:154,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:139,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:160,columnNumber:15},void 0),d.jsxDEV("input",{type:"text",name:"apellido",value:p.apellido,onChange:h,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(x.apellido?"border-red-500":"border-gray-300"),placeholder:"Ingrese el apellido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:163,columnNumber:15},void 0),x.apellido&&d.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:x.apellido},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:174,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:159,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:180,columnNumber:15},void 0),d.jsxDEV("input",{type:"email",name:"email",value:p.email,onChange:h,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "+(x.email?"border-red-500":"border-gray-300"),placeholder:"<EMAIL>"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:183,columnNumber:15},void 0),x.email&&d.jsxDEV("p",{className:"text-red-500 text-xs mt-1",children:x.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:194,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:179,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:200,columnNumber:15},void 0),d.jsxDEV("input",{type:"text",name:"documento",value:p.documento,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Número de documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:203,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:199,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:215,columnNumber:15},void 0),d.jsxDEV("select",{name:"genero",value:p.genero,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Seleccionar género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:224,columnNumber:17},void 0),d.jsxDEV("option",{value:"masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:225,columnNumber:17},void 0),d.jsxDEV("option",{value:"femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:226,columnNumber:17},void 0),d.jsxDEV("option",{value:"otro",children:"Otro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:227,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:218,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:214,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:233,columnNumber:15},void 0),d.jsxDEV("input",{type:"date",name:"fecha_nacimiento",value:p.fecha_nacimiento,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:236,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:232,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nivel Educativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:247,columnNumber:15},void 0),d.jsxDEV("select",{name:"nivel_educativo",value:p.nivel_educativo,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Seleccionar nivel educativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:256,columnNumber:17},void 0),d.jsxDEV("option",{value:"primaria",children:"Primaria"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:257,columnNumber:17},void 0),d.jsxDEV("option",{value:"secundaria",children:"Secundaria"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:258,columnNumber:17},void 0),d.jsxDEV("option",{value:"tecnico",children:"Técnico"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:259,columnNumber:17},void 0),d.jsxDEV("option",{value:"universitario",children:"Universitario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:260,columnNumber:17},void 0),d.jsxDEV("option",{value:"posgrado",children:"Posgrado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:261,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:250,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:246,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ocupación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:267,columnNumber:15},void 0),d.jsxDEV("input",{type:"text",name:"ocupacion",value:p.ocupacion,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Ocupación del paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:270,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:266,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo Asignado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:282,columnNumber:15},void 0),d.jsxDEV("select",{name:"psicologo_id",value:p.psicologo_id,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Seleccionar psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:291,columnNumber:17},void 0),t.map(e=>d.jsxDEV("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:293,columnNumber:19},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:285,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:281,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:302,columnNumber:15},void 0),d.jsxDEV("select",{name:"institucion_id",value:p.institucion_id,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"",children:"Seleccionar institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:311,columnNumber:17},void 0),o.map(e=>d.jsxDEV("option",{value:e.id,children:e.nombre},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:313,columnNumber:19},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:305,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:301,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:137,columnNumber:11},void 0),d.jsxDEV("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200",children:[d.jsxDEV("button",{type:"button",onClick:i,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",disabled:g,children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:323,columnNumber:13},void 0),d.jsxDEV("button",{type:"submit",disabled:g,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:g?d.jsxDEV(d.Fragment,{children:[d.jsxDEV("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:338,columnNumber:19},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:337,columnNumber:17},void 0):d.jsxDEV(d.Fragment,{children:[d.jsxDEV(b,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:343,columnNumber:19},void 0),"Guardar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:342,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:331,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:322,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:136,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:120,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientModal.jsx",lineNumber:119,columnNumber:5},void 0):null},E=()=>{const{user:e,isAdmin:i,isPsicologo:a,loading:n}=p(),[r,t]=V.useState([]),[o,s]=V.useState(!1),[l,c]=V.useState(""),[u,b]=V.useState(!1),[E,D]=V.useState(null),[B,w]=V.useState(""),[M,k]=V.useState([]),[S,_]=V.useState([]);if(n)return d.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(N,{className:"animate-spin text-blue-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:35,columnNumber:11},void 0),d.jsxDEV("span",{className:"text-gray-600",children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:36,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:34,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:33,columnNumber:7},void 0);if(!i&&!a)return d.jsxDEV("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:d.jsxDEV("div",{className:"text-center p-8 bg-white rounded-lg shadow-lg max-w-md",children:[d.jsxDEV("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsxDEV(x,{className:"text-red-600 text-2xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:47,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:46,columnNumber:11},void 0),d.jsxDEV("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Acceso Denegado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:49,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-600 mb-6",children:"Solo los administradores y psicólogos pueden gestionar pacientes."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:50,columnNumber:11},void 0),d.jsxDEV("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Volver"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:53,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:45,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:44,columnNumber:7},void 0);const O=()=>m(null,null,function*(){s(!0);try{const{data:e,error:i}=yield j.from("pacientes").select("*").order("created_at",{ascending:!1});if(i)throw i;t(e||[])}catch(e){C.error("Error al cargar los pacientes")}finally{s(!1)}});V.useEffect(()=>{O(),m(null,null,function*(){try{const{data:e,error:i}=yield j.from("psicologos").select("id, nombre, apellido").order("nombre",{ascending:!0});if(i)throw i;k(e||[])}catch(e){}}),m(null,null,function*(){try{const{data:e,error:i}=yield j.from("instituciones").select("id, nombre").order("nombre",{ascending:!0});if(i)throw i;_(e||[])}catch(e){}});m(null,null,function*(){try{const{count:e}=yield j.from("pacientes").select("*",{count:"exact",head:!0}),{count:i}=yield j.from("resultados").select("*",{count:"exact",head:!0})}catch(e){}})},[]);const A=r.filter(e=>{var i,a,n,r;if(!l)return!0;const t=l.toLowerCase();return(null==(i=e.nombre)?void 0:i.toLowerCase().includes(t))||(null==(a=e.apellido)?void 0:a.toLowerCase().includes(t))||(null==(n=e.documento)?void 0:n.toLowerCase().includes(t))||(null==(r=e.email)?void 0:r.toLowerCase().includes(t))}),G=e=>m(null,null,function*(){try{s(!0);const{error:i}=yield j.from("pacientes").insert([e]);if(i)throw i;C.success("Paciente creado correctamente"),O()}catch(i){throw C.error("Error al crear el paciente"),i}finally{s(!1)}}),q=(e,i)=>m(null,null,function*(){try{s(!0);const{error:a}=yield j.from("pacientes").update(i).eq("id",e);if(a)throw a;C.success("Paciente actualizado correctamente"),O()}catch(a){C.error("Error al actualizar el paciente")}finally{s(!1)}});return d.jsxDEV("div",{className:"min-h-screen bg-gray-50",children:[d.jsxDEV(y,{title:"Gestión de Pacientes",subtitle:"Administra la información y el historial de tus pacientes registrados en la plataforma",icon:x},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:262,columnNumber:7},void 0),d.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[d.jsxDEV("div",{className:"mb-6",children:d.jsxDEV("div",{className:"flex items-center justify-between",children:[d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:276,columnNumber:15},void 0),d.jsxDEV("p",{className:"text-gray-600 mt-1",children:["Administre los pacientes registrados en el sistema (",A.length," pacientes)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:277,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:275,columnNumber:13},void 0),d.jsxDEV("div",{className:"flex items-center space-x-3",children:[d.jsxDEV("div",{className:"relative",children:[d.jsxDEV(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:284,columnNumber:17},void 0),d.jsxDEV("input",{type:"text",placeholder:"Buscar paciente...",value:l,onChange:e=>c(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:285,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:283,columnNumber:15},void 0),i&&d.jsxDEV("button",{onClick:()=>{D(null),w("Nuevo Paciente"),b(!0)},className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[d.jsxDEV(g,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:300,columnNumber:19},void 0),"Nuevo Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:296,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:281,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:274,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:273,columnNumber:9},void 0),d.jsxDEV("div",{className:"bg-white shadow-sm rounded-lg overflow-hidden",children:d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[d.jsxDEV("thead",{className:"bg-blue-600 text-white",children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nombre Completo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:314,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:317,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:320,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:323,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Fecha de Nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:326,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Nivel Educativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:329,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Ocupación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:332,columnNumber:19},void 0),i&&d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:336,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:313,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:312,columnNumber:15},void 0),d.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:o?d.jsxDEV("tr",{children:d.jsxDEV("td",{colSpan:i?8:7,className:"px-6 py-12 text-center",children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV(N,{className:"animate-spin text-blue-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:348,columnNumber:25},void 0),d.jsxDEV("span",{className:"text-gray-600",children:"Cargando pacientes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:349,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:347,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:346,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:345,columnNumber:19},void 0):0===A.length?d.jsxDEV("tr",{children:d.jsxDEV("td",{colSpan:i?8:7,className:"px-6 py-12 text-center text-gray-500",children:0===r.length?"No hay pacientes registrados":"No se encontraron pacientes que coincidan con la búsqueda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:355,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:354,columnNumber:19},void 0):A.map(e=>{var a,n;return d.jsxDEV("tr",{className:"hover:bg-gray-50",children:[d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3",children:d.jsxDEV("span",{className:"text-white text-sm font-medium",children:null==(n=null==(a=e.nombre)?void 0:a.charAt(0))?void 0:n.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:365,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:364,columnNumber:27},void 0),d.jsxDEV("div",{children:[d.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:370,columnNumber:29},void 0),d.jsxDEV("div",{className:"text-sm text-gray-500",children:e.telefono&&`Tel: ${e.telefono}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:373,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:369,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:363,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:362,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.email||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:380,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:d.jsxDEV("span",{className:"px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full",children:e.documento||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:385,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:384,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:d.jsxDEV("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+("masculino"===e.genero?"bg-blue-100 text-blue-800":"femenino"===e.genero?"bg-pink-100 text-pink-800":"bg-gray-100 text-gray-800"),children:e.genero?e.genero.charAt(0).toUpperCase()+e.genero.slice(1):"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:391,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:390,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.fecha_nacimiento?new Date(e.fecha_nacimiento).toLocaleDateString("es-ES"):"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:400,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.nivel_educativo||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:407,columnNumber:23},void 0),d.jsxDEV("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.ocupacion||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:411,columnNumber:23},void 0),i&&d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:d.jsxDEV("div",{className:"flex space-x-2",children:[d.jsxDEV("button",{onClick:()=>(e=>{D(e),w("Editar Paciente"),b(!0)})(e),className:"text-blue-600 hover:text-blue-900 transition-colors",title:"Editar paciente",children:d.jsxDEV(v,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:423,columnNumber:31},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:418,columnNumber:29},void 0),d.jsxDEV("button",{onClick:()=>(e=>m(null,null,function*(){if(window.confirm(`¿Está seguro de eliminar al paciente ${e.nombre} ${e.apellido}?`))try{s(!0);const{error:i}=yield j.from("pacientes").delete().eq("id",e.id);if(i)throw i;C.success("Paciente eliminado correctamente"),O()}catch(i){C.error("Error al eliminar el paciente")}finally{s(!1)}}))(e),className:"text-red-600 hover:text-red-900 transition-colors",title:"Eliminar paciente",children:d.jsxDEV(h,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:430,columnNumber:31},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:425,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:417,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:416,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:361,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:343,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:311,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:310,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:309,columnNumber:9},void 0),d.jsxDEV("div",{className:"mt-8 text-center text-sm text-gray-500",children:"© 2025 Sistema de Gestión Psicológica - Panel de Administración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:444,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:271,columnNumber:7},void 0),d.jsxDEV(P,{isOpen:u,onClose:()=>{b(!1),D(null),w("")},patient:E,onSave:e=>m(null,null,function*(){E?yield q(E.id,e):yield G(e)}),title:B,psychologists:M,institutions:S},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:450,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Patients.jsx",lineNumber:260,columnNumber:5},void 0)};export{E as default};
