var e=(e,t,r)=>new Promise((s,n)=>{var i=e=>{try{a(r.next(e))}catch(t){n(t)}},o=e=>{try{a(r.throw(e))}catch(t){n(t)}},a=e=>e.done?s(e.value):Promise.resolve(e.value).then(i,o);a((r=r.apply(e,t)).next())});import{G as t}from"./ui-vendor-COFtXQcG.js";import{a as r,r as s,g as n,b as i}from"./react-vendor-C9XH6RF0.js";var o,a,c={exports:{}},l={};function u(){return o||(o=1,function(){var e=r(),t=Symbol.for("react.element"),s=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen"),g=Symbol.iterator;var y=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];!function(e,t,r){var s=y.ReactDebugCurrentFrame.getStackAddendum();""!==s&&(t+="%s",r=r.concat([s]));var n=r.map(function(e){return String(e)});n.unshift("Warning: "+t),Function.prototype.apply.call(console[e],console,n)}("error",e,r)}var b;function _(e){return e.displayName||"Context"}function w(e){if(null==e)return null;if("number"==typeof e.tag&&m("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case n:return"Fragment";case s:return"Portal";case o:return"Profiler";case i:return"StrictMode";case h:return"Suspense";case d:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case c:return _(e)+".Consumer";case a:return _(e._context)+".Provider";case u:return function(e,t,r){var s=e.displayName;if(s)return s;var n=t.displayName||t.name||"";return""!==n?r+"("+n+")":r}(e,e.render,"ForwardRef");case f:var t=e.displayName||null;return null!==t?t:w(e.type)||"Memo";case p:var r=e,l=r._payload,v=r._init;try{return w(v(l))}catch(g){return null}}return null}b=Symbol.for("react.module.reference");var k,S,T,j,E,C,P,O=Object.assign,x=0;function z(){}z.__reactDisabledLog=!0;var $,A=y.ReactCurrentDispatcher;function R(e,t,r){if(void 0===$)try{throw Error()}catch(n){var s=n.stack.trim().match(/\n( *(at )?)/);$=s&&s[1]||""}return"\n"+$+e}var I,L=!1,M="function"==typeof WeakMap?WeakMap:Map;function B(e,t){if(!e||L)return"";var r,s=I.get(e);if(void 0!==s)return s;L=!0;var n,i=Error.prepareStackTrace;Error.prepareStackTrace=void 0,n=A.current,A.current=null,function(){if(0===x){k=console.log,S=console.info,T=console.warn,j=console.error,E=console.group,C=console.groupCollapsed,P=console.groupEnd;var e={configurable:!0,enumerable:!0,value:z,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}x++}();try{if(t){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(o,[])}catch(p){r=p}Reflect.construct(e,[],o)}else{try{o.call()}catch(p){r=p}e.call(o.prototype)}}else{try{throw Error()}catch(p){r=p}e()}}catch(v){if(v&&r&&"string"==typeof v.stack){for(var a=v.stack.split("\n"),c=r.stack.split("\n"),l=a.length-1,u=c.length-1;l>=1&&u>=0&&a[l]!==c[u];)u--;for(;l>=1&&u>=0;l--,u--)if(a[l]!==c[u]){if(1!==l||1!==u)do{if(l--,--u<0||a[l]!==c[u]){var h="\n"+a[l].replace(" at new "," at ");return e.displayName&&h.includes("<anonymous>")&&(h=h.replace("<anonymous>",e.displayName)),"function"==typeof e&&I.set(e,h),h}}while(l>=1&&u>=0);break}}}finally{L=!1,A.current=n,function(){if(0===--x){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:O({},e,{value:k}),info:O({},e,{value:S}),warn:O({},e,{value:T}),error:O({},e,{value:j}),group:O({},e,{value:E}),groupCollapsed:O({},e,{value:C}),groupEnd:O({},e,{value:P})})}x<0&&m("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=i}var d=e?e.displayName||e.name:"",f=d?R(d):"";return"function"==typeof e&&I.set(e,f),f}function U(e,t,r){if(null==e)return"";if("function"==typeof e)return B(e,!(!(s=e.prototype)||!s.isReactComponent));var s;if("string"==typeof e)return R(e);switch(e){case h:return R("Suspense");case d:return R("SuspenseList")}if("object"==typeof e)switch(e.$$typeof){case u:return B(e.render,!1);case f:return U(e.type,t,r);case p:var n=e,i=n._payload,o=n._init;try{return U(o(i),t,r)}catch(a){}}return""}I=new M;var H=Object.prototype.hasOwnProperty,D={},N=y.ReactDebugCurrentFrame;function V(e){if(e){var t=e._owner,r=U(e.type,e._source,t?t.type:null);N.setExtraStackFrame(r)}else N.setExtraStackFrame(null)}var q=Array.isArray;function F(e){return q(e)}function W(e){return""+e}function K(e){if(function(e){try{return W(e),!1}catch(t){return!0}}(e))return m("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(e){return"function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}(e)),W(e)}var J,G,Y,X=y.ReactCurrentOwner,Z={key:!0,ref:!0,__self:!0,__source:!0};Y={};function Q(e,r,s,n,i){var o,a={},c=null,l=null;for(o in void 0!==s&&(K(s),c=""+s),function(e){if(H.call(e,"key")){var t=Object.getOwnPropertyDescriptor(e,"key").get;if(t&&t.isReactWarning)return!1}return void 0!==e.key}(r)&&(K(r.key),c=""+r.key),function(e){if(H.call(e,"ref")){var t=Object.getOwnPropertyDescriptor(e,"ref").get;if(t&&t.isReactWarning)return!1}return void 0!==e.ref}(r)&&(l=r.ref,function(e,t){if("string"==typeof e.ref&&X.current&&t&&X.current.stateNode!==t){var r=w(X.current.type);Y[r]||(m('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',w(X.current.type),e.ref),Y[r]=!0)}}(r,i)),r)H.call(r,o)&&!Z.hasOwnProperty(o)&&(a[o]=r[o]);if(e&&e.defaultProps){var u=e.defaultProps;for(o in u)void 0===a[o]&&(a[o]=u[o])}if(c||l){var h="function"==typeof e?e.displayName||e.name||"Unknown":e;c&&function(e,t){var r=function(){J||(J=!0,m("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};r.isReactWarning=!0,Object.defineProperty(e,"key",{get:r,configurable:!0})}(a,h),l&&function(e,t){var r=function(){G||(G=!0,m("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};r.isReactWarning=!0,Object.defineProperty(e,"ref",{get:r,configurable:!0})}(a,h)}return function(e,r,s,n,i,o,a){var c={$$typeof:t,type:e,key:r,ref:s,props:a,_owner:o,_store:{}};return Object.defineProperty(c._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(c,"_self",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(c,"_source",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(c.props),Object.freeze(c)),c}(e,c,l,i,n,X.current,a)}var ee,te=y.ReactCurrentOwner,re=y.ReactDebugCurrentFrame;function se(e){if(e){var t=e._owner,r=U(e.type,e._source,t?t.type:null);re.setExtraStackFrame(r)}else re.setExtraStackFrame(null)}function ne(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}function ie(){if(te.current){var e=w(te.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}ee=!1;var oe={};function ae(e,t){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var r=function(e){var t=ie();if(!t){var r="string"==typeof e?e:e.displayName||e.name;r&&(t="\n\nCheck the top-level render call using <"+r+">.")}return t}(t);if(!oe[r]){oe[r]=!0;var s="";e&&e._owner&&e._owner!==te.current&&(s=" It was passed a child from "+w(e._owner.type)+"."),se(e),m('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,s),se(null)}}}function ce(e,t){if("object"==typeof e)if(F(e))for(var r=0;r<e.length;r++){var s=e[r];ne(s)&&ae(s,t)}else if(ne(e))e._store&&(e._store.validated=!0);else if(e){var n=function(e){if(null===e||"object"!=typeof e)return null;var t=g&&e[g]||e["@@iterator"];return"function"==typeof t?t:null}(e);if("function"==typeof n&&n!==e.entries)for(var i,o=n.call(e);!(i=o.next()).done;)ne(i.value)&&ae(i.value,t)}}function le(e){var t,r=e.type;if(null!=r&&"string"!=typeof r){if("function"==typeof r)t=r.propTypes;else{if("object"!=typeof r||r.$$typeof!==u&&r.$$typeof!==f)return;t=r.propTypes}if(t){var s=w(r);!function(e,t,r,s,n){var i=Function.call.bind(H);for(var o in e)if(i(e,o)){var a=void 0;try{if("function"!=typeof e[o]){var c=Error((s||"React class")+": "+r+" type `"+o+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[o]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw c.name="Invariant Violation",c}a=e[o](t,o,s,r,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(l){a=l}!a||a instanceof Error||(V(n),m("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",s||"React class",r,o,typeof a),V(null)),a instanceof Error&&!(a.message in D)&&(D[a.message]=!0,V(n),m("Failed %s type: %s",r,a.message),V(null))}}(t,e.props,"prop",s,e)}else if(void 0!==r.PropTypes&&!ee){ee=!0,m("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",w(r)||"Unknown")}"function"!=typeof r.getDefaultProps||r.getDefaultProps.isReactClassApproved||m("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}var ue={};var he=function(e,r,s,l,g,y){var _=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===o||e===i||e===h||e===d||e===v||"object"==typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===f||e.$$typeof===a||e.$$typeof===c||e.$$typeof===u||e.$$typeof===b||void 0!==e.getModuleId)}(e);if(!_){var k="";(void 0===e||"object"==typeof e&&null!==e&&0===Object.keys(e).length)&&(k+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var S,T=function(e){return void 0!==e?"\n\nCheck your code at "+e.fileName.replace(/^.*[\\\/]/,"")+":"+e.lineNumber+".":""}(g);k+=T||ie(),null===e?S="null":F(e)?S="array":void 0!==e&&e.$$typeof===t?(S="<"+(w(e.type)||"Unknown")+" />",k=" Did you accidentally export a JSX literal instead of a component?"):S=typeof e,m("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",S,k)}var j=Q(e,r,s,g,y);if(null==j)return j;if(_){var E=r.children;if(void 0!==E)if(l)if(F(E)){for(var C=0;C<E.length;C++)ce(E[C],e);Object.freeze&&Object.freeze(E)}else m("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ce(E,e)}if(H.call(r,"key")){var P=w(e),O=Object.keys(r).filter(function(e){return"key"!==e}),x=O.length>0?"{key: someKey, "+O.join(": ..., ")+": ...}":"{key: someKey}";if(!ue[P+x])m('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',x,P,O.length>0?"{"+O.join(": ..., ")+": ...}":"{}",P),ue[P+x]=!0}return e===n?function(e){for(var t=Object.keys(e.props),r=0;r<t.length;r++){var s=t[r];if("children"!==s&&"key"!==s){se(e),m("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",s),se(null);break}}null!==e.ref&&(se(e),m("Invalid attribute `ref` supplied to `React.Fragment`."),se(null))}(j):le(j),j};l.Fragment=n,l.jsxDEV=he}()),l}var h=(a||(a=1,c.exports=u()),c.exports);const d=s.createContext(),f=({children:e})=>{const[t,r]=s.useState("administrador"),n={user:{id:"dev-user",email:"<EMAIL>",nombre:"Usuario",apellido:"Desarrollo",tipo_usuario:t},loading:!1,error:null,login:()=>Promise.resolve({success:!0}),logout:()=>Promise.resolve({success:!0}),isAuthenticated:!0,userRole:t,isAdmin:"administrador"===t,isPsicologo:"psicologo"===t,isCandidato:"candidato"===t,setUserType:r};return h.jsxDEV(d.Provider,{value:n,children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/context/NoAuthContext.jsx",lineNumber:38,columnNumber:5},void 0)},p=()=>{const e=s.useContext(d);if(!e)throw new Error("useNoAuth debe usarse dentro de NoAuthProvider");return e},v={},g=function(e,t,r){let s=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const r=document.querySelector("meta[property=csp-nonce]"),n=(null==r?void 0:r.nonce)||(null==r?void 0:r.getAttribute("nonce"));s=e(t.map(e=>{if((e=function(e){return"/Bat-7-Version-fial-para-github/"+e}(e))in v)return;v[e]=!0;const t=e.endsWith(".css"),r=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${r}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script"),s.crossOrigin="",s.href=e,n&&s.setAttribute("nonce",n),document.head.appendChild(s),t?new Promise((t,r)=>{s.addEventListener("load",t),s.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return s.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})};function y(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M34.9 289.5l-22.2-22.2c-9.4-9.4-9.4-24.6 0-33.9L207 39c9.4-9.4 24.6-9.4 33.9 0l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9L413 289.4c-9.5 9.5-25 9.3-34.3-.4L264 168.6V456c0 13.3-10.7 24-24 24h-32c-13.3 0-24-10.7-24-24V168.6L69.2 289.1c-9.3 9.8-24.8 10-34.3.4z"}}]})(e)}function m(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 512c35.32 0 63.97-28.65 63.97-64H160.03c0 35.35 28.65 64 63.97 64zm215.39-149.71c-19.32-20.76-55.47-51.99-55.47-154.29 0-77.7-54.48-139.9-127.94-155.16V32c0-17.67-14.32-32-31.98-32s-31.98 14.33-31.98 32v20.84C118.56 68.1 64.08 130.3 64.08 208c0 102.3-36.15 133.53-55.47 154.29-6 6.45-8.66 14.16-8.61 21.71.11 16.4 12.98 32 32.1 32h383.8c19.12 0 32-15.6 32.1-32 .05-7.55-2.61-15.27-8.61-21.71z"}}]})(e)}function b(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 360V24c0-13.3-10.7-24-24-24H96C43 0 0 43 0 96v320c0 53 43 96 96 96h328c13.3 0 24-10.7 24-24v-16c0-7.5-3.5-14.3-8.9-18.7-4.2-15.4-4.2-59.3 0-74.7 5.4-4.3 8.9-11.1 8.9-18.6zM128 134c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm0 64c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm253.4 250H96c-17.7 0-32-14.3-32-32 0-17.6 14.4-32 32-32h285.4c-1.9 17.1-1.9 46.9 0 64z"}}]})(e)}function _(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M208 0c-29.9 0-54.7 20.5-61.8 48.2-.8 0-1.4-.2-2.2-.2-35.3 0-64 28.7-64 64 0 4.8.6 9.5 1.7 14C52.5 138 32 166.6 32 200c0 12.6 3.2 24.3 8.3 34.9C16.3 248.7 0 274.3 0 304c0 33.3 20.4 61.9 49.4 73.9-.9 4.6-1.4 9.3-1.4 14.1 0 39.8 32.2 72 72 72 4.1 0 8.1-.5 12-1.2 9.6 28.5 36.2 49.2 68 49.2 39.8 0 72-32.2 72-72V64c0-35.3-28.7-64-64-64zm368 304c0-29.7-16.3-55.3-40.3-69.1 5.2-10.6 8.3-22.3 8.3-34.9 0-33.4-20.5-62-49.7-74 1-4.5 1.7-9.2 1.7-14 0-35.3-28.7-64-64-64-.8 0-1.5.2-2.2.2C422.7 20.5 397.9 0 368 0c-35.3 0-64 28.6-64 64v376c0 39.8 32.2 72 72 72 31.8 0 58.4-20.7 68-49.2 3.9.7 7.9 1.2 12 1.2 39.8 0 72-32.2 72-72 0-4.8-.5-9.5-1.4-14.1 29-12 49.4-40.6 49.4-73.9z"}}]})(e)}function w(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M436 480h-20V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v456H12c-6.627 0-12 5.373-12 12v20h448v-20c0-6.627-5.373-12-12-12zM128 76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76zm0 96c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40zm52 148h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12zm76 160h-64v-84c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v84zm64-172c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40z"}}]})(e)}function k(e){return t({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M248 8C111.03 8 0 119.03 0 256s111.03 248 248 248 248-111.03 248-248S384.97 8 248 8zm0 432c-101.69 0-184-82.29-184-184 0-101.69 82.29-184 184-184 101.69 0 184 82.29 184 184 0 101.69-82.29 184-184 184zm0-312c-70.69 0-128 57.31-128 128s57.31 128 128 128 128-57.31 128-128-57.31-128-128-128zm0 192c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"}}]})(e)}function S(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 0H48C22.4 0 0 22.4 0 48v416c0 25.6 22.4 48 48 48h352c25.6 0 48-22.4 48-48V48c0-25.6-22.4-48-48-48zM128 435.2c0 6.4-6.4 12.8-12.8 12.8H76.8c-6.4 0-12.8-6.4-12.8-12.8v-38.4c0-6.4 6.4-12.8 12.8-12.8h38.4c6.4 0 12.8 6.4 12.8 12.8v38.4zm0-128c0 6.4-6.4 12.8-12.8 12.8H76.8c-6.4 0-12.8-6.4-12.8-12.8v-38.4c0-6.4 6.4-12.8 12.8-12.8h38.4c6.4 0 12.8 6.4 12.8 12.8v38.4zm128 128c0 6.4-6.4 12.8-12.8 12.8h-38.4c-6.4 0-12.8-6.4-12.8-12.8v-38.4c0-6.4 6.4-12.8 12.8-12.8h38.4c6.4 0 12.8 6.4 12.8 12.8v38.4zm0-128c0 6.4-6.4 12.8-12.8 12.8h-38.4c-6.4 0-12.8-6.4-12.8-12.8v-38.4c0-6.4 6.4-12.8 12.8-12.8h38.4c6.4 0 12.8 6.4 12.8 12.8v38.4zm128 128c0 6.4-6.4 12.8-12.8 12.8h-38.4c-6.4 0-12.8-6.4-12.8-12.8V268.8c0-6.4 6.4-12.8 12.8-12.8h38.4c6.4 0 12.8 6.4 12.8 12.8v166.4zm0-256c0 6.4-6.4 12.8-12.8 12.8H76.8c-6.4 0-12.8-6.4-12.8-12.8V76.8C64 70.4 70.4 64 76.8 64h294.4c6.4 0 12.8 6.4 12.8 12.8v102.4z"}}]})(e)}function T(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"}}]})(e)}function j(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M12 192h424c6.6 0 12 5.4 12 12v260c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V204c0-6.6 5.4-12 12-12zm436-44v-36c0-26.5-21.5-48-48-48h-48V12c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v52H160V12c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v52H48C21.5 64 0 85.5 0 112v36c0 6.6 5.4 12 12 12h424c6.6 0 12-5.4 12-12z"}}]})(e)}function E(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M332.8 320h38.4c6.4 0 12.8-6.4 12.8-12.8V172.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V76.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-288 0h38.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zM496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z"}}]})(e)}function C(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM464 96H345.94c-21.38 0-32.09 25.85-16.97 40.97l32.4 32.4L288 242.75l-73.37-73.37c-12.5-12.5-32.76-12.5-45.25 0l-68.69 68.69c-6.25 6.25-6.25 16.38 0 22.63l22.62 22.62c6.25 6.25 16.38 6.25 22.63 0L192 237.25l73.37 73.37c12.5 12.5 32.76 12.5 45.25 0l96-96 32.4 32.4c15.12 15.12 40.97 4.41 40.97-16.97V112c.01-8.84-7.15-16-15.99-16z"}}]})(e)}function P(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"}}]})(e)}function O(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 480H48c-26.51 0-48-21.49-48-48V80c0-26.51 21.49-48 48-48h352c26.51 0 48 21.49 48 48v352c0 26.51-21.49 48-48 48zm-204.686-98.059l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.248-16.379-6.249-22.628 0L184 302.745l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.25 16.379 6.25 22.628.001z"}}]})(e)}function x(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z"}}]})(e)}function z(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"}}]})(e)}function $(e){return t({attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z"}}]})(e)}function A(e){return t({attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"}}]})(e)}function R(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M240.971 130.524l194.343 194.343c9.373 9.373 9.373 24.569 0 33.941l-22.667 22.667c-9.357 9.357-24.522 9.375-33.901.04L224 227.495 69.255 381.516c-9.379 9.335-24.544 9.317-33.901-.04l-22.667-22.667c-9.373-9.373-9.373-24.569 0-33.941L207.03 130.525c9.372-9.373 24.568-9.373 33.941-.001z"}}]})(e)}function I(e){return t({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM192 40c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm121.2 231.8l-143 141.8c-4.7 4.7-12.3 4.6-17-.1l-82.6-83.3c-4.7-4.7-4.6-12.3.1-17L99.1 285c4.7-4.7 12.3-4.6 17 .1l46 46.4 106-105.2c4.7-4.7 12.3-4.6 17 .1l28.2 28.4c4.7 4.8 4.6 12.3-.1 17z"}}]})(e)}function L(e){return t({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM96 424c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm96-192c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm128 368c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16z"}}]})(e)}function M(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"}}]})(e)}function B(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.4 315.7l-42.6-24.6c4.3-23.2 4.3-47 0-70.2l42.6-24.6c4.9-2.8 7.1-8.6 5.5-14-11.1-35.6-30-67.8-54.7-94.6-3.8-4.1-10-5.1-14.8-2.3L380.8 110c-17.9-15.4-38.5-27.3-60.8-35.1V25.8c0-5.6-3.9-10.5-9.4-11.7-36.7-8.2-74.3-7.8-109.2 0-5.5 1.2-9.4 6.1-9.4 11.7V75c-22.2 7.9-42.8 19.8-60.8 35.1L88.7 85.5c-4.9-2.8-11-1.9-14.8 2.3-24.7 26.7-43.6 58.9-54.7 94.6-1.7 5.4.6 11.2 5.5 14L67.3 221c-4.3 23.2-4.3 47 0 70.2l-42.6 24.6c-4.9 2.8-7.1 8.6-5.5 14 11.1 35.6 30 67.8 54.7 94.6 3.8 4.1 10 5.1 14.8 2.3l42.6-24.6c17.9 15.4 38.5 27.3 60.8 35.1v49.2c0 5.6 3.9 10.5 9.4 11.7 36.7 8.2 74.3 7.8 109.2 0 5.5-1.2 9.4-6.1 9.4-11.7v-49.2c22.2-7.9 42.8-19.8 60.8-35.1l42.6 24.6c4.9 2.8 11 1.9 14.8-2.3 24.7-26.7 43.6-58.9 54.7-94.6 1.5-5.5-.7-11.3-5.6-14.1zM256 336c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"}}]})(e)}function U(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M512.1 191l-8.2 14.3c-3 5.3-9.4 7.5-15.1 5.4-11.8-4.4-22.6-10.7-32.1-18.6-4.6-3.8-5.8-10.5-2.8-15.7l8.2-14.3c-6.9-8-12.3-17.3-15.9-27.4h-16.5c-6 0-11.2-4.3-12.2-10.3-2-12-2.1-24.6 0-37.1 1-6 6.2-10.4 12.2-10.4h16.5c3.6-10.1 9-19.4 15.9-27.4l-8.2-14.3c-3-5.2-1.9-11.9 2.8-15.7 9.5-7.9 20.4-14.2 32.1-18.6 5.7-2.1 12.1.1 15.1 5.4l8.2 14.3c10.5-1.9 21.2-1.9 31.7 0L552 6.3c3-5.3 9.4-7.5 15.1-5.4 11.8 4.4 22.6 10.7 32.1 18.6 4.6 3.8 5.8 10.5 2.8 15.7l-8.2 14.3c6.9 8 12.3 17.3 15.9 27.4h16.5c6 0 11.2 4.3 12.2 10.3 2 12 2.1 24.6 0 37.1-1 6-6.2 10.4-12.2 10.4h-16.5c-3.6 10.1-9 19.4-15.9 27.4l8.2 14.3c3 5.2 1.9 11.9-2.8 15.7-9.5 7.9-20.4 14.2-32.1 18.6-5.7 2.1-12.1-.1-15.1-5.4l-8.2-14.3c-10.4 1.9-21.2 1.9-31.7 0zm-10.5-58.8c38.5 29.6 82.4-14.3 52.8-52.8-38.5-29.7-82.4 14.3-52.8 52.8zM386.3 286.1l33.7 16.8c10.1 5.8 14.5 18.1 10.5 29.1-8.9 24.2-26.4 46.4-42.6 65.8-7.4 8.9-20.2 11.1-30.3 5.3l-29.1-16.8c-16 13.7-34.6 24.6-54.9 31.7v33.6c0 11.6-8.3 21.6-19.7 23.6-24.6 4.2-50.4 4.4-75.9 0-11.5-2-20-11.9-20-23.6V418c-20.3-7.2-38.9-18-54.9-31.7L74 403c-10 5.8-22.9 3.6-30.3-5.3-16.2-19.4-33.3-41.6-42.2-65.7-4-10.9.4-23.2 10.5-29.1l33.3-16.8c-3.9-20.9-3.9-42.4 0-63.4L12 205.8c-10.1-5.8-14.6-18.1-10.5-29 8.9-24.2 26-46.4 42.2-65.8 7.4-8.9 20.2-11.1 30.3-5.3l29.1 16.8c16-13.7 34.6-24.6 54.9-31.7V57.1c0-11.5 8.2-21.5 19.6-23.5 24.6-4.2 50.5-4.4 76-.1 11.5 2 20 11.9 20 23.6v33.6c20.3 7.2 38.9 18 54.9 31.7l29.1-16.8c10-5.8 22.9-3.6 30.3 5.3 16.2 19.4 33.2 41.6 42.1 65.8 4 10.9.1 23.2-10 29.1l-33.7 16.8c3.9 21 3.9 42.5 0 63.5zm-117.6 21.1c59.2-77-28.7-164.9-105.7-105.7-59.2 77 28.7 164.9 105.7 105.7zm243.4 182.7l-8.2 14.3c-3 5.3-9.4 7.5-15.1 5.4-11.8-4.4-22.6-10.7-32.1-18.6-4.6-3.8-5.8-10.5-2.8-15.7l8.2-14.3c-6.9-8-12.3-17.3-15.9-27.4h-16.5c-6 0-11.2-4.3-12.2-10.3-2-12-2.1-24.6 0-37.1 1-6 6.2-10.4 12.2-10.4h16.5c3.6-10.1 9-19.4 15.9-27.4l-8.2-14.3c-3-5.2-1.9-11.9 2.8-15.7 9.5-7.9 20.4-14.2 32.1-18.6 5.7-2.1 12.1.1 15.1 5.4l8.2 14.3c10.5-1.9 21.2-1.9 31.7 0l8.2-14.3c3-5.3 9.4-7.5 15.1-5.4 11.8 4.4 22.6 10.7 32.1 18.6 4.6 3.8 5.8 10.5 2.8 15.7l-8.2 14.3c6.9 8 12.3 17.3 15.9 27.4h16.5c6 0 11.2 4.3 12.2 10.3 2 12 2.1 24.6 0 37.1-1 6-6.2 10.4-12.2 10.4h-16.5c-3.6 10.1-9 19.4-15.9 27.4l8.2 14.3c3 5.2 1.9 11.9-2.8 15.7-9.5 7.9-20.4 14.2-32.1 18.6-5.7 2.1-12.1-.1-15.1-5.4l-8.2-14.3c-10.4 1.9-21.2 1.9-31.7 0zM501.6 431c38.5 29.6 82.4-14.3 52.8-52.8-38.5-29.6-82.4 14.3-52.8 52.8z"}}]})(e)}function H(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M0 405.3V448c0 35.3 86 64 192 64s192-28.7 192-64v-42.7C342.7 434.4 267.2 448 192 448S41.3 434.4 0 405.3zM320 128c106 0 192-28.7 192-64S426 0 320 0 128 28.7 128 64s86 64 192 64zM0 300.4V352c0 35.3 86 64 192 64s192-28.7 192-64v-51.6c-41.3 34-116.9 51.6-192 51.6S41.3 334.4 0 300.4zm416 11c57.3-11.1 96-31.7 96-55.4v-42.7c-23.2 16.4-57.3 27.6-96 34.5v63.6zM192 160C86 160 0 195.8 0 240s86 80 192 80 192-35.8 192-80-86-80-192-80zm219.3 56.3c60-10.8 100.7-32 100.7-56.3v-42.7c-35.5 25.1-96.5 38.6-160.7 41.8 29.5 14.3 51.2 33.5 60 57.2z"}}]})(e)}function D(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 73.143v45.714C448 159.143 347.667 192 224 192S0 159.143 0 118.857V73.143C0 32.857 100.333 0 224 0s224 32.857 224 73.143zM448 176v102.857C448 319.143 347.667 352 224 352S0 319.143 0 278.857V176c48.125 33.143 136.208 48.572 224 48.572S399.874 209.143 448 176zm0 160v102.857C448 479.143 347.667 512 224 512S0 479.143 0 438.857V336c48.125 33.143 136.208 48.572 224 48.572S399.874 369.143 448 336z"}}]})(e)}function N(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"}}]})(e)}function V(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M402.6 83.2l90.2 90.2c3.8 3.8 3.8 10 0 13.8L274.4 405.6l-92.8 10.3c-12.4 1.4-22.9-9.1-21.5-21.5l10.3-92.8L388.8 83.2c3.8-3.8 10-3.8 13.8 0zm162-22.9l-48.8-48.8c-15.2-15.2-39.9-15.2-55.2 0l-35.4 35.4c-3.8 3.8-3.8 10 0 13.8l90.2 90.2c3.8 3.8 10 3.8 13.8 0l35.4-35.4c15.2-15.3 15.2-40 0-55.2zM384 346.2V448H64V128h229.8c3.2 0 6.2-1.3 8.5-3.5l40-40c7.6-7.6 2.2-20.5-8.5-20.5H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V306.2c0-10.7-12.9-16-20.5-8.5l-40 40c-2.2 2.3-3.5 5.3-3.5 8.5z"}}]})(e)}function q(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z"}}]})(e)}function F(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"}}]})(e)}function W(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"}}]})(e)}function K(e){return t({attr:{viewBox:"0 0 256 512"},child:[{tag:"path",attr:{d:"M128 0c35.346 0 64 28.654 64 64s-28.654 64-64 64c-35.346 0-64-28.654-64-64S92.654 0 128 0m119.283 354.179l-48-192A24 24 0 0 0 176 144h-11.36c-22.711 10.443-49.59 10.894-73.28 0H80a24 24 0 0 0-23.283 18.179l-48 192C4.935 369.305 16.383 384 32 384h56v104c0 13.255 10.745 24 24 24h32c13.255 0 24-10.745 24-24V384h56c15.591 0 27.071-14.671 23.283-29.821z"}}]})(e)}function J(e){return t({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm64 236c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-64c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12v8zm0-72v8c0 6.6-5.4 12-12 12H108c-6.6 0-12-5.4-12-12v-8c0-6.6 5.4-12 12-12h168c6.6 0 12 5.4 12 12zm96-114.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"}}]})(e)}function G(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.976 0H24.028C2.71 0-8.047 25.866 7.058 40.971L192 225.941V432c0 7.831 3.821 15.17 10.237 19.662l80 55.98C298.02 518.69 320 507.493 320 487.98V225.941l184.947-184.97C520.021 25.896 509.338 0 487.976 0z"}}]})(e)}function Y(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M622.34 153.2L343.4 67.5c-15.2-4.67-31.6-4.67-46.79 0L17.66 153.2c-23.54 7.23-23.54 38.36 0 45.59l48.63 14.94c-10.67 13.19-17.23 29.28-17.88 46.9C38.78 266.15 32 276.11 32 288c0 10.78 5.68 19.85 13.86 25.65L20.33 428.53C18.11 438.52 25.71 448 35.94 448h56.11c10.24 0 17.84-9.48 15.62-19.47L82.14 313.65C90.32 307.85 96 298.78 96 288c0-11.57-6.47-21.25-15.66-26.87.76-15.02 8.44-28.3 20.69-36.72L296.6 284.5c9.06 2.78 26.44 6.25 46.79 0l278.95-85.7c23.55-7.24 23.55-38.36 0-45.6zM352.79 315.09c-28.53 8.76-52.84 3.92-65.59 0l-145.02-44.55L128 384c0 35.35 85.96 64 192 64s192-28.65 192-64l-14.18-113.47-145.03 44.56z"}}]})(e)}function X(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 255.531c.253 136.64-111.18 248.372-247.82 248.468-59.015.042-113.223-20.53-155.822-54.911-11.077-8.94-11.905-25.541-1.839-35.607l11.267-11.267c8.609-8.609 22.353-9.551 31.891-1.984C173.062 425.135 212.781 440 256 440c101.705 0 184-82.311 184-184 0-101.705-82.311-184-184-184-48.814 0-93.149 18.969-126.068 49.932l50.754 50.754c10.08 10.08 2.941 27.314-11.313 27.314H24c-8.837 0-16-7.163-16-16V38.627c0-14.254 17.234-21.393 27.314-11.314l49.372 49.372C129.209 34.136 189.552 8 256 8c136.81 0 247.747 110.78 248 247.531zm-180.912 78.784l9.823-12.63c8.138-10.463 6.253-25.542-4.21-33.679L288 256.349V152c0-13.255-10.745-24-24-24h-16c-13.255 0-24 10.745-24 24v135.651l65.409 50.874c10.463 8.137 25.541 6.253 33.679-4.21z"}}]})(e)}function Z(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M528 32H48C21.5 32 0 53.5 0 80v16h576V80c0-26.5-21.5-48-48-48zM0 432c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V128H0v304zm352-232c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H360c-4.4 0-8-3.6-8-8v-16zm0 64c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H360c-4.4 0-8-3.6-8-8v-16zm0 64c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8H360c-4.4 0-8-3.6-8-8v-16zM176 192c35.3 0 64 28.7 64 64s-28.7 64-64 64-64-28.7-64-64 28.7-64 64-64zM67.1 396.2C75.5 370.5 99.6 352 128 352h8.2c12.3 5.1 25.7 8 39.8 8s27.6-2.9 39.8-8h8.2c28.4 0 52.5 18.5 60.9 44.2 3.2 9.9-5.2 19.8-15.6 19.8H82.7c-10.4 0-18.8-10-15.6-19.8z"}}]})(e)}function Q(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M471.1 96C405 96 353.3 137.3 320 174.6 286.7 137.3 235 96 168.9 96 75.8 96 0 167.8 0 256s75.8 160 168.9 160c66.1 0 117.8-41.3 151.1-78.6 33.3 37.3 85 78.6 151.1 78.6 93.1 0 168.9-71.8 168.9-160S564.2 96 471.1 96zM168.9 320c-40.2 0-72.9-28.7-72.9-64s32.7-64 72.9-64c38.2 0 73.4 36.1 94 64-20.4 27.6-55.9 64-94 64zm302.2 0c-38.2 0-73.4-36.1-94-64 20.4-27.6 55.9-64 94-64 40.2 0 72.9 28.7 72.9 64s-32.7 64-72.9 64z"}}]})(e)}function ee(e){return t({attr:{viewBox:"0 0 192 512"},child:[{tag:"path",attr:{d:"M20 424.229h20V279.771H20c-11.046 0-20-8.954-20-20V212c0-11.046 8.954-20 20-20h112c11.046 0 20 8.954 20 20v212.229h20c11.046 0 20 8.954 20 20V492c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20v-47.771c0-11.046 8.954-20 20-20zM96 0C56.235 0 24 32.235 24 72s32.235 72 72 72 72-32.235 72-72S135.764 0 96 0z"}}]})(e)}function te(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M512 176.001C512 273.203 433.202 352 336 352c-11.22 0-22.19-1.062-32.827-3.069l-24.012 27.014A23.999 23.999 0 0 1 261.223 384H224v40c0 13.255-10.745 24-24 24h-40v40c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24v-78.059c0-6.365 2.529-12.47 7.029-16.971l161.802-161.802C163.108 213.814 160 195.271 160 176 160 78.798 238.797.001 335.999 0 433.488-.001 512 78.511 512 176.001zM336 128c0 26.51 21.49 48 48 48s48-21.49 48-48-21.49-48-48-48-48 21.49-48 48z"}}]})(e)}function re(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M152.1 236.2c-3.5-12.1-7.8-33.2-7.8-33.2h-.5s-4.3 21.1-7.8 33.2l-11.1 37.5H163zM616 96H336v320h280c13.3 0 24-10.7 24-24V120c0-13.3-10.7-24-24-24zm-24 120c0 6.6-5.4 12-12 12h-11.4c-6.9 23.6-21.7 47.4-42.7 69.9 8.4 6.4 17.1 12.5 26.1 18 5.5 3.4 7.3 10.5 4.1 16.2l-7.9 13.9c-3.4 5.9-10.9 7.8-16.7 4.3-12.6-7.8-24.5-16.1-35.4-24.9-10.9 8.7-22.7 17.1-35.4 24.9-5.8 3.5-13.3 1.6-16.7-4.3l-7.9-13.9c-3.2-5.6-1.4-12.8 4.2-16.2 9.3-5.7 18-11.7 26.1-18-7.9-8.4-14.9-17-21-25.7-4-5.7-2.2-13.6 3.7-17.1l6.5-3.9 7.3-4.3c5.4-3.2 12.4-1.7 16 3.4 5 7 10.8 14 17.4 20.9 13.5-14.2 23.8-28.9 30-43.2H412c-6.6 0-12-5.4-12-12v-16c0-6.6 5.4-12 12-12h64v-16c0-6.6 5.4-12 12-12h16c6.6 0 12 5.4 12 12v16h64c6.6 0 12 5.4 12 12zM0 120v272c0 13.3 10.7 24 24 24h280V96H24c-13.3 0-24 10.7-24 24zm58.9 216.1L116.4 167c1.7-4.9 6.2-8.1 11.4-8.1h32.5c5.1 0 9.7 3.3 11.4 8.1l57.5 169.1c2.6 7.8-3.1 15.9-11.4 15.9h-22.9a12 12 0 0 1-11.5-8.6l-9.4-31.9h-60.2l-9.1 31.8c-1.5 5.1-6.2 8.7-11.5 8.7H70.3c-8.2 0-14-8.1-11.4-15.9z"}}]})(e)}function se(e){return t({attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M96.06 454.35c.01 6.29 1.87 12.45 5.36 17.69l17.09 25.69a31.99 31.99 0 0 0 26.64 14.28h61.71a31.99 31.99 0 0 0 26.64-14.28l17.09-25.69a31.989 31.989 0 0 0 5.36-17.69l.04-38.35H96.01l.05 38.35zM0 176c0 44.37 16.45 84.85 43.56 115.78 16.52 18.85 42.36 58.23 52.21 91.45.04.26.07.52.11.78h160.24c.04-.26.07-.51.11-.78 9.85-33.22 35.69-72.6 52.21-91.45C335.55 260.85 352 220.37 352 176 352 78.61 272.91-.3 175.45 0 73.44.31 0 82.97 0 176zm176-80c-44.11 0-80 35.89-80 80 0 8.84-7.16 16-16 16s-16-7.16-16-16c0-61.76 50.24-112 112-112 8.84 0 16 7.16 16 16s-7.16 16-16 16z"}}]})(e)}function ne(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M326.612 185.391c59.747 59.809 58.927 155.698.36 214.59-.11.12-.24.25-.36.37l-67.2 67.2c-59.27 59.27-155.699 59.262-214.96 0-59.27-59.26-59.27-155.7 0-214.96l37.106-37.106c9.84-9.84 26.786-3.3 27.294 10.606.648 17.722 3.826 35.527 9.69 52.721 1.986 5.822.567 12.262-3.783 16.612l-13.087 13.087c-28.026 28.026-28.905 73.66-1.155 101.96 28.024 28.579 74.086 28.749 102.325.51l67.2-67.19c28.191-28.191 28.073-73.757 0-101.83-3.701-3.694-7.429-6.564-10.341-8.569a16.037 16.037 0 0 1-6.947-12.606c-.396-10.567 3.348-21.456 11.698-29.806l21.054-21.055c5.521-5.521 14.182-6.199 20.584-1.731a152.482 152.482 0 0 1 20.522 17.197zM467.547 44.449c-59.261-59.262-155.69-59.27-214.96 0l-67.2 67.2c-.12.12-.25.25-.36.37-58.566 58.892-59.387 154.781.36 214.59a152.454 152.454 0 0 0 20.521 17.196c6.402 4.468 15.064 3.789 20.584-1.731l21.054-21.055c8.35-8.35 12.094-19.239 11.698-29.806a16.037 16.037 0 0 0-6.947-12.606c-2.912-2.005-6.64-4.875-10.341-8.569-28.073-28.073-28.191-73.639 0-101.83l67.2-67.19c28.239-28.239 74.3-28.069 102.325.51 27.75 28.3 26.872 73.934-1.155 101.96l-13.087 13.087c-4.35 4.35-5.769 10.79-3.783 16.612 5.864 17.194 9.042 34.999 9.69 52.721.509 13.906 17.454 20.446 27.294 10.606l37.106-37.106c59.271-59.259 59.271-155.699.001-214.959z"}}]})(e)}function ie(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M80 368H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16zm0-320H16A16 16 0 0 0 0 64v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16V64a16 16 0 0 0-16-16zm0 160H16a16 16 0 0 0-16 16v64a16 16 0 0 0 16 16h64a16 16 0 0 0 16-16v-64a16 16 0 0 0-16-16zm416 176H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-320H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16V80a16 16 0 0 0-16-16zm0 160H176a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h320a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16z"}}]})(e)}function oe(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 224h-24v-72C376 68.2 307.8 0 224 0S72 68.2 72 152v72H48c-26.5 0-48 21.5-48 48v192c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V272c0-26.5-21.5-48-48-48zm-104 0H152v-72c0-39.7 32.3-72 72-72s72 32.3 72 72v72z"}}]})(e)}function ae(e){return t({attr:{viewBox:"0 0 192 512"},child:[{tag:"path",attr:{d:"M96 0c35.346 0 64 28.654 64 64s-28.654 64-64 64-64-28.654-64-64S60.654 0 96 0m48 144h-11.36c-22.711 10.443-49.59 10.894-73.28 0H48c-26.51 0-48 21.49-48 48v136c0 13.255 10.745 24 24 24h16v136c0 13.255 10.745 24 24 24h64c13.255 0 24-10.745 24-24V352h16c13.255 0 24-10.745 24-24V192c0-26.51-21.49-48-48-48z"}}]})(e)}function ce(e){return t({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M372 64h-79c-10.7 0-16 12.9-8.5 20.5l16.9 16.9-80.7 80.7c-22.2-14-48.5-22.1-76.7-22.1C64.5 160 0 224.5 0 304s64.5 144 144 144 144-64.5 144-144c0-28.2-8.1-54.5-22.1-76.7l80.7-80.7 16.9 16.9c7.6 7.6 20.5 2.2 20.5-8.5V76c0-6.6-5.4-12-12-12zM144 384c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"}}]})(e)}function le(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h384c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"}}]})(e)}function ue(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z"}}]})(e)}function he(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"}}]})(e)}function de(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"}}]})(e)}function fe(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm144 276c0 6.6-5.4 12-12 12h-92v92c0 6.6-5.4 12-12 12h-56c-6.6 0-12-5.4-12-12v-92h-92c-6.6 0-12-5.4-12-12v-56c0-6.6 5.4-12 12-12h92v-92c0-6.6 5.4-12 12-12h56c6.6 0 12 5.4 12 12v92h92c6.6 0 12 5.4 12 12v56z"}}]})(e)}function pe(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"}}]})(e)}function ve(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M448 192V77.25c0-8.49-3.37-16.62-9.37-22.63L393.37 9.37c-6-6-14.14-9.37-22.63-9.37H96C78.33 0 64 14.33 64 32v160c-35.35 0-64 28.65-64 64v112c0 8.84 7.16 16 16 16h48v96c0 17.67 14.33 32 32 32h320c17.67 0 32-14.33 32-32v-96h48c8.84 0 16-7.16 16-16V256c0-35.35-28.65-64-64-64zm-64 256H128v-96h256v96zm0-224H128V64h192v48c0 8.84 7.16 16 16 16h48v96zm48 72c-13.25 0-24-10.75-24-24 0-13.26 10.75-24 24-24s24 10.74 24 24c0 13.25-10.75 24-24 24z"}}]})(e)}function ge(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M519.442 288.651c-41.519 0-59.5 31.593-82.058 31.593C377.409 320.244 432 144 432 144s-196.288 80-196.288-3.297c0-35.827 36.288-46.25 36.288-85.985C272 19.216 243.885 0 210.539 0c-34.654 0-66.366 18.891-66.366 56.346 0 41.364 31.711 59.277 31.711 81.75C175.885 207.719 0 166.758 0 166.758v333.237s178.635 41.047 178.635-28.662c0-22.473-40-40.107-40-81.471 0-37.456 29.25-56.346 63.577-56.346 33.673 0 61.788 19.216 61.788 54.717 0 39.735-36.288 50.158-36.288 85.985 0 60.803 129.675 25.73 181.23 25.73 0 0-34.725-120.101 25.827-120.101 35.962 0 46.423 36.152 86.308 36.152C556.712 416 576 387.99 576 354.443c0-34.199-18.962-65.792-56.558-65.792z"}}]})(e)}function ye(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zM262.655 90c-54.497 0-89.255 22.957-116.549 63.758-3.536 5.286-2.353 12.415 2.715 16.258l34.699 26.31c5.205 3.947 12.621 3.008 16.665-2.122 17.864-22.658 30.113-35.797 57.303-35.797 20.429 0 45.698 13.148 45.698 32.958 0 14.976-12.363 22.667-32.534 33.976C247.128 238.528 216 254.941 216 296v4c0 6.627 5.373 12 12 12h56c6.627 0 12-5.373 12-12v-1.333c0-28.462 83.186-29.647 83.186-106.667 0-58.002-60.165-102-116.531-102zM256 338c-25.365 0-46 20.635-46 46 0 25.364 20.635 46 46 46s46-20.636 46-46c0-25.365-20.635-46-46-46z"}}]})(e)}function me(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M500.33 0h-47.41a12 12 0 0 0-12 12.57l4 82.76A247.42 247.42 0 0 0 256 8C119.34 8 7.9 119.53 8 256.19 8.1 393.07 119.1 504 256 504a247.1 247.1 0 0 0 166.18-63.91 12 12 0 0 0 .48-17.43l-34-34a12 12 0 0 0-16.38-.55A176 176 0 1 1 402.1 157.8l-101.53-4.87a12 12 0 0 0-12.57 12v47.41a12 12 0 0 0 12 12h200.33a12 12 0 0 0 12-12V12a12 12 0 0 0-12-12z"}}]})(e)}function be(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M433.941 129.941l-83.882-83.882A48 48 0 0 0 316.118 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h352c26.51 0 48-21.49 48-48V163.882a48 48 0 0 0-14.059-33.941zM224 416c-35.346 0-64-28.654-64-64 0-35.346 28.654-64 64-64s64 28.654 64 64c0 35.346-28.654 64-64 64zm96-304.52V212c0 6.627-5.373 12-12 12H76c-6.627 0-12-5.373-12-12V108c0-6.627 5.373-12 12-12h228.52c3.183 0 6.235 1.264 8.485 3.515l3.48 3.48A11.996 11.996 0 0 1 320 111.48z"}}]})(e)}function _e(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"}}]})(e)}function we(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M466.5 83.7l-192-80a48.15 48.15 0 0 0-36.9 0l-192 80C27.7 91.1 16 108.6 16 128c0 198.5 114.5 335.7 221.5 380.3 11.8 4.9 25.1 4.9 36.9 0C360.1 472.6 496 349.3 496 128c0-19.4-11.7-36.9-29.5-44.3zM256.1 446.3l-.1-381 175.9 73.3c-3.3 151.4-82.1 261.1-175.8 307.7z"}}]})(e)}function ke(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M176 352h-48V48a16 16 0 0 0-16-16H80a16 16 0 0 0-16 16v304H16c-14.19 0-21.36 17.24-11.29 27.31l80 96a16 16 0 0 0 22.62 0l80-96C197.35 369.26 190.22 352 176 352zm240-64H288a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h56l-61.26 70.45A32 32 0 0 0 272 446.37V464a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16h-56l61.26-70.45A32 32 0 0 0 432 321.63V304a16 16 0 0 0-16-16zm31.06-85.38l-59.27-160A16 16 0 0 0 372.72 32h-41.44a16 16 0 0 0-15.07 10.62l-59.27 160A16 16 0 0 0 272 224h24.83a16 16 0 0 0 15.23-11.08l4.42-12.92h71l4.41 12.92A16 16 0 0 0 407.16 224H432a16 16 0 0 0 15.06-21.38zM335.61 144L352 96l16.39 48z"}}]})(e)}function Se(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 160h48v304a16 16 0 0 0 16 16h32a16 16 0 0 0 16-16V160h48c14.21 0 21.38-17.24 11.31-27.31l-80-96a16 16 0 0 0-22.62 0l-80 96C-5.35 142.74 1.78 160 16 160zm400 128H288a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h56l-61.26 70.45A32 32 0 0 0 272 446.37V464a16 16 0 0 0 16 16h128a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16h-56l61.26-70.45A32 32 0 0 0 432 321.63V304a16 16 0 0 0-16-16zm31.06-85.38l-59.27-160A16 16 0 0 0 372.72 32h-41.44a16 16 0 0 0-15.07 10.62l-59.27 160A16 16 0 0 0 272 224h24.83a16 16 0 0 0 15.23-11.08l4.42-12.92h71l4.41 12.92A16 16 0 0 0 407.16 224H432a16 16 0 0 0 15.06-21.38zM335.61 144L352 96l16.39 48z"}}]})(e)}function Te(e){return t({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M272 256h91.36c43.2 0 82-32.2 84.51-75.34a79.82 79.82 0 0 0-25.26-63.07 79.81 79.81 0 0 0 9.06-44.91C427.9 30.57 389.3 0 347 0h-75a16 16 0 0 0-16 16v224a16 16 0 0 0 16 16zm40-200h40a24 24 0 0 1 0 48h-40zm0 96h56a24 24 0 0 1 0 48h-56zM155.12 22.25A32 32 0 0 0 124.64 0H99.36a32 32 0 0 0-30.48 22.25L.59 235.73A16 16 0 0 0 16 256h24.93a16 16 0 0 0 15.42-11.73L68.29 208h87.42l11.94 36.27A16 16 0 0 0 183.07 256H208a16 16 0 0 0 15.42-20.27zM89.37 144L112 75.3l22.63 68.7zm482 132.48l-45.21-45.3a15.88 15.88 0 0 0-22.59 0l-151.5 151.5-55.41-55.5a15.88 15.88 0 0 0-22.59 0l-45.3 45.3a16 16 0 0 0 0 22.59l112 112.21a15.89 15.89 0 0 0 22.6 0l208-208.21a16 16 0 0 0-.02-22.59z"}}]})(e)}function je(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48 48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48c0-26.509-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.491-48-48-48z"}}]})(e)}function Ee(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M400 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48z"}}]})(e)}function Ce(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 160c-52.9 0-96 43.1-96 96s43.1 96 96 96 96-43.1 96-96-43.1-96-96-96zm246.4 80.5l-94.7-47.3 33.5-100.4c4.5-13.6-8.4-26.5-21.9-21.9l-100.4 33.5-47.4-94.8c-6.4-12.8-24.6-12.8-31 0l-47.3 94.7L92.7 70.8c-13.6-4.5-26.5 8.4-21.9 21.9l33.5 100.4-94.7 47.4c-12.8 6.4-12.8 24.6 0 31l94.7 47.3-33.5 100.5c-4.5 13.6 8.4 26.5 21.9 21.9l100.4-33.5 47.3 94.7c6.4 12.8 24.6 12.8 31 0l47.3-94.7 100.4 33.5c13.6 4.5 26.5-8.4 21.9-21.9l-33.5-100.4 94.7-47.3c13-6.5 13-24.7.2-31.1zm-155.9 106c-49.9 49.9-131.1 49.9-181 0-49.9-49.9-49.9-131.1 0-181 49.9-49.9 131.1-49.9 181 0 49.9 49.9 49.9 131.1 0 181z"}}]})(e)}function Pe(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V80c0-26.51-21.49-48-48-48zM224 416H64v-96h160v96zm0-160H64v-96h160v96zm224 160H288v-96h160v96zm0-160H288v-96h160v96z"}}]})(e)}function Oe(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M149.333 56v80c0 13.255-10.745 24-24 24H24c-13.255 0-24-10.745-24-24V56c0-13.255 10.745-24 24-24h101.333c13.255 0 24 10.745 24 24zm181.334 240v-80c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.256 0 24.001-10.745 24.001-24zm32-240v80c0 13.255 10.745 24 24 24H488c13.255 0 24-10.745 24-24V56c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24zm-32 80V56c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.256 0 24.001-10.745 24.001-24zm-205.334 56H24c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24zM0 376v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H24c-13.255 0-24 10.745-24 24zm386.667-56H488c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24zm0 160H488c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H386.667c-13.255 0-24 10.745-24 24v80c0 13.255 10.745 24 24 24zM181.333 376v80c0 13.255 10.745 24 24 24h101.333c13.255 0 24-10.745 24-24v-80c0-13.255-10.745-24-24-24H205.333c-13.255 0-24 10.745-24 24z"}}]})(e)}function xe(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z"}}]})(e)}function ze(e){return t({attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"}}]})(e)}function $e(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M32 464a48 48 0 0 0 48 48h288a48 48 0 0 0 48-48V128H32zm272-256a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zm-96 0a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zm-96 0a16 16 0 0 1 32 0v224a16 16 0 0 1-32 0zM432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16z"}}]})(e)}function Ae(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"}}]})(e)}function Re(e){return t({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304.083 405.907c4.686 4.686 4.686 12.284 0 16.971l-44.674 44.674c-59.263 59.262-155.693 59.266-214.961 0-59.264-59.265-59.264-155.696 0-214.96l44.675-44.675c4.686-4.686 12.284-4.686 16.971 0l39.598 39.598c4.686 4.686 4.686 12.284 0 16.971l-44.675 44.674c-28.072 28.073-28.072 73.75 0 101.823 28.072 28.072 73.75 28.073 101.824 0l44.674-44.674c4.686-4.686 12.284-4.686 16.971 0l39.597 39.598zm-56.568-260.216c4.686 4.686 12.284 4.686 16.971 0l44.674-44.674c28.072-28.075 73.75-28.073 101.824 0 28.072 28.073 28.072 73.75 0 101.823l-44.675 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.598 39.598c4.686 4.686 12.284 4.686 16.971 0l44.675-44.675c59.265-59.265 59.265-155.695 0-214.96-59.266-59.264-155.695-59.264-214.961 0l-44.674 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.597 39.598zm234.828 359.28l22.627-22.627c9.373-9.373 9.373-24.569 0-33.941L63.598 7.029c-9.373-9.373-24.569-9.373-33.941 0L7.029 29.657c-9.373 9.373-9.373 24.569 0 33.941l441.373 441.373c9.373 9.372 24.569 9.372 33.941 0z"}}]})(e)}function Ie(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4zm323-128.4l-27.8-28.1c-4.6-4.7-12.1-4.7-16.8-.1l-104.8 104-45.5-45.8c-4.6-4.7-12.1-4.7-16.8-.1l-28.1 27.9c-4.7 4.6-4.7 12.1-.1 16.8l81.7 82.3c4.6 4.7 12.1 4.7 16.8.1l141.3-140.2c4.6-4.7 4.7-12.2.1-16.8z"}}]})(e)}function Le(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zM104 424c0 13.3 10.7 24 24 24s24-10.7 24-24-10.7-24-24-24-24 10.7-24 24zm216-135.4v49c36.5 7.4 64 39.8 64 78.4v41.7c0 7.6-5.4 14.2-12.9 15.7l-32.2 6.4c-4.3.9-8.5-1.9-9.4-6.3l-3.1-15.7c-.9-4.3 1.9-8.6 6.3-9.4l19.3-3.9V416c0-62.8-96-65.1-96 1.9v26.7l19.3 3.9c4.3.9 7.1 5.1 6.3 9.4l-3.1 15.7c-.9 4.3-5.1 7.1-9.4 6.3l-31.2-4.2c-7.9-1.1-13.8-7.8-13.8-15.9V416c0-38.6 27.5-70.9 64-78.4v-45.2c-2.2.7-4.4 1.1-6.6 1.9-18 6.3-37.3 9.8-57.4 9.8s-39.4-3.5-57.4-9.8c-7.4-2.6-14.9-4.2-22.6-5.2v81.6c23.1 6.9 40 28.1 40 53.4 0 30.9-25.1 56-56 56s-56-25.1-56-56c0-25.3 16.9-46.5 40-53.4v-80.4C48.5 301 0 355.8 0 422.4v44.8C0 491.9 20.1 512 44.8 512h358.4c24.7 0 44.8-20.1 44.8-44.8v-44.8c0-72-56.8-130.3-128-133.8z"}}]})(e)}function Me(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M624 208h-64v-64c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v64h-64c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h64v64c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-64h64c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-400 48c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function Be(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M589.6 240l45.6-45.6c6.3-6.3 6.3-16.5 0-22.8l-22.8-22.8c-6.3-6.3-16.5-6.3-22.8 0L544 194.4l-45.6-45.6c-6.3-6.3-16.5-6.3-22.8 0l-22.8 22.8c-6.3 6.3-6.3 16.5 0 22.8l45.6 45.6-45.6 45.6c-6.3 6.3-6.3 16.5 0 22.8l22.8 22.8c6.3 6.3 16.5 6.3 22.8 0l45.6-45.6 45.6 45.6c6.3 6.3 16.5 6.3 22.8 0l22.8-22.8c6.3-6.3 6.3-16.5 0-22.8L589.6 240zM224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function Ue(e){return t({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"}}]})(e)}function He(e){return t({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"}}]})(e)}function De(e){return t({attr:{viewBox:"0 0 288 512"},child:[{tag:"path",attr:{d:"M288 176c0-79.5-64.5-144-144-144S0 96.5 0 176c0 68.5 47.9 125.9 112 140.4V368H76c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h36v36c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12v-36h36c6.6 0 12-5.4 12-12v-40c0-6.6-5.4-12-12-12h-36v-51.6c64.1-14.5 112-71.9 112-140.4zm-224 0c0-44.1 35.9-80 80-80s80 35.9 80 80-35.9 80-80 80-80-35.9-80-80z"}}]})(e)}class Ne extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class Ve extends Ne{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class qe extends Ne{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Fe extends Ne{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var We,Ke;(Ke=We||(We={})).Any="any",Ke.ApNortheast1="ap-northeast-1",Ke.ApNortheast2="ap-northeast-2",Ke.ApSouth1="ap-south-1",Ke.ApSoutheast1="ap-southeast-1",Ke.ApSoutheast2="ap-southeast-2",Ke.CaCentral1="ca-central-1",Ke.EuCentral1="eu-central-1",Ke.EuWest1="eu-west-1",Ke.EuWest2="eu-west-2",Ke.EuWest3="eu-west-3",Ke.SaEast1="sa-east-1",Ke.UsEast1="us-east-1",Ke.UsWest1="us-west-1",Ke.UsWest2="us-west-2";var Je=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class Ge{constructor(t,{headers:r={},customFetch:s,region:n=We.Any}={}){this.url=t,this.headers=r,this.region=n,this.fetch=(t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>g(()=>e(null,null,function*(){const{default:e}=yield Promise.resolve().then(()=>ct);return{default:e}}),void 0).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)})(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r;return Je(this,void 0,void 0,function*(){try{const{headers:s,method:n,body:i}=t;let o={},{region:a}=t;a||(a=this.region);const c=new URL(`${this.url}/${e}`);let l;a&&"any"!==a&&(o["x-region"]=a,c.searchParams.set("forceFunctionRegion",a)),i&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",l=i):"string"==typeof i?(o["Content-Type"]="text/plain",l=i):"undefined"!=typeof FormData&&i instanceof FormData?l=i:(o["Content-Type"]="application/json",l=JSON.stringify(i)));const u=yield this.fetch(c.toString(),{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),s),body:l}).catch(e=>{throw new Ve(e)}),h=u.headers.get("x-relay-error");if(h&&"true"===h)throw new qe(u);if(!u.ok)throw new Fe(u);let d,f=(null!==(r=u.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return d="application/json"===f?yield u.json():"application/octet-stream"===f?yield u.blob():"text/event-stream"===f?u:"multipart/form-data"===f?yield u.formData():yield u.text(),{data:d,error:null,response:u}}catch(s){return{data:null,error:s,response:s instanceof Fe||s instanceof qe?s.context:void 0}}})}}var Ye={},Xe={},Ze={},Qe={},et={},tt={},rt=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const st=rt.fetch,nt=rt.fetch.bind(rt),it=rt.Headers,ot=rt.Request,at=rt.Response,ct=Object.freeze(Object.defineProperty({__proto__:null,Headers:it,Request:ot,Response:at,default:nt,fetch:st},Symbol.toStringTag,{value:"Module"})),lt=n(ct);var ut,ht,dt,ft,pt,vt={};function gt(){if(ut)return vt;ut=1,Object.defineProperty(vt,"__esModule",{value:!0});class e extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}return vt.default=e,vt}function yt(){if(ht)return tt;ht=1;var t=tt&&tt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(tt,"__esModule",{value:!0});const r=t(lt),s=t(gt());return tt.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=r.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(t,r){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let n=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(t=>e(this,null,function*(){var e,r,n;let i=null,o=null,a=null,c=t.status,l=t.statusText;if(t.ok){if("HEAD"!==this.method){const e=yield t.text();""===e||(o="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?e:JSON.parse(e))}const s=null===(e=this.headers.Prefer)||void 0===e?void 0:e.match(/count=(exact|planned|estimated)/),n=null===(r=t.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&n&&n.length>1&&(a=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(i={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,c=406,l="Not Acceptable"):o=1===o.length?o[0]:null)}else{const e=yield t.text();try{i=JSON.parse(e),Array.isArray(i)&&404===t.status&&(o=[],i=null,c=200,l="OK")}catch(u){404===t.status&&""===e?(c=204,l="No Content"):i={message:e}}if(i&&this.isMaybeSingle&&(null===(n=null==i?void 0:i.details)||void 0===n?void 0:n.includes("0 rows"))&&(i=null,c=200,l="OK"),i&&this.shouldThrowOnError)throw new s.default(i)}return{error:i,data:o,count:a,status:c,statusText:l}}));return this.shouldThrowOnError||(n=n.catch(e=>{var t,r,s;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(s=null==e?void 0:e.code)&&void 0!==s?s:""}`},data:null,count:null,status:0,statusText:""}})),n.then(t,r)}returns(){return this}overrideTypes(){return this}},tt}function mt(){if(dt)return et;dt=1;var e=et&&et.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(et,"__esModule",{value:!0});const t=e(yt());class r extends t.default{select(e){let t=!1;const r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:s,referencedTable:n=s}={}){const i=n?`${n}.order`:"order",o=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){const s=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(s,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:s=r}={}){const n=void 0===s?"offset":`${s}.offset`,i=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(i,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:s=!1,wal:n=!1,format:i="text"}={}){var o;const a=[e?"analyze":null,t?"verbose":null,r?"settings":null,s?"buffers":null,n?"wal":null].filter(Boolean).join("|"),c=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${c}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return et.default=r,et}function bt(){if(ft)return Qe;ft=1;var e=Qe&&Qe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Qe,"__esModule",{value:!0});const t=e(mt());class r extends t.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const r=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:s}={}){let n="";"plain"===s?n="pl":"phrase"===s?n="ph":"websearch"===s&&(n="w");const i=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${i}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){const s=r?`${r}.or`:"or";return this.url.searchParams.append(s,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}return Qe.default=r,Qe}function _t(){if(pt)return Ze;pt=1;var e=Ze&&Ze.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ze,"__esModule",{value:!0});const t=e(bt());return Ze.default=class{constructor(e,{headers:t={},schema:r,fetch:s}){this.url=e,this.headers=t,this.schema=r,this.fetch=s}select(e,{head:r=!1,count:s}={}){const n=r?"HEAD":"GET";let i=!1;const o=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",o),s&&(this.headers.Prefer=`count=${s}`),new t.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:r,defaultToNull:s=!0}={}){const n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),r&&n.push(`count=${r}`),s||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:r,ignoreDuplicates:s=!1,count:n,defaultToNull:i=!0}={}){const o=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==r&&this.url.searchParams.set("on_conflict",r),this.headers.Prefer&&o.push(this.headers.Prefer),n&&o.push(`count=${n}`),i||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:r}={}){const s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),r&&s.push(`count=${r}`),this.headers.Prefer=s.join(","),new t.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const r=[];return e&&r.push(`count=${e}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new t.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}},Ze}var wt,kt,St,Tt,jt={},Et={};function Ct(){if(kt)return jt;kt=1,Object.defineProperty(jt,"__esModule",{value:!0}),jt.DEFAULT_HEADERS=void 0;const e=(wt||(wt=1,Object.defineProperty(Et,"__esModule",{value:!0}),Et.version=void 0,Et.version="0.0.0-automated"),Et);return jt.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${e.version}`},jt}const Pt=i(function(){if(Tt)return Ye;Tt=1;var e=Ye&&Ye.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ye,"__esModule",{value:!0}),Ye.PostgrestError=Ye.PostgrestBuilder=Ye.PostgrestTransformBuilder=Ye.PostgrestFilterBuilder=Ye.PostgrestQueryBuilder=Ye.PostgrestClient=void 0;const t=e(function(){if(St)return Xe;St=1;var e=Xe&&Xe.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Xe,"__esModule",{value:!0});const t=e(_t()),r=e(bt()),s=Ct();class n{constructor(e,{headers:t={},schema:r,fetch:n}={}){this.url=e,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=n}from(e){const r=new URL(`${this.url}/${e}`);return new t.default(r,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new n(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:n=!1,count:i}={}){let o;const a=new URL(`${this.url}/rpc/${e}`);let c;s||n?(o=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{a.searchParams.append(e,t)})):(o="POST",c=t);const l=Object.assign({},this.headers);return i&&(l.Prefer=`count=${i}`),new r.default({method:o,url:a,headers:l,schema:this.schemaName,body:c,fetch:this.fetch,allowEmpty:!1})}}return Xe.default=n,Xe}());Ye.PostgrestClient=t.default;const r=e(_t());Ye.PostgrestQueryBuilder=r.default;const s=e(bt());Ye.PostgrestFilterBuilder=s.default;const n=e(mt());Ye.PostgrestTransformBuilder=n.default;const i=e(yt());Ye.PostgrestBuilder=i.default;const o=e(gt());return Ye.PostgrestError=o.default,Ye.default={PostgrestClient:t.default,PostgrestQueryBuilder:r.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:n.default,PostgrestBuilder:i.default,PostgrestError:o.default},Ye}()),{PostgrestClient:Ot,PostgrestQueryBuilder:xt,PostgrestFilterBuilder:zt,PostgrestTransformBuilder:$t,PostgrestBuilder:At,PostgrestError:Rt}=Pt;class It{static detectEnvironment(){var e;if("undefined"!=typeof WebSocket)return{type:"native",constructor:WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocket)return{type:"native",constructor:globalThis.WebSocket};if("undefined"!=typeof global&&void 0!==global.WebSocket)return{type:"native",constructor:global.WebSocket};if("undefined"!=typeof globalThis&&void 0!==globalThis.WebSocketPair&&void 0===globalThis.WebSocket)return{type:"cloudflare",error:"Cloudflare Workers detected. WebSocket clients are not supported in Cloudflare Workers.",workaround:"Use Cloudflare Workers WebSocket API for server-side WebSocket handling, or deploy to a different runtime."};if("undefined"!=typeof globalThis&&globalThis.EdgeRuntime||"undefined"!=typeof navigator&&(null===(e=navigator.userAgent)||void 0===e?void 0:e.includes("Vercel-Edge")))return{type:"unsupported",error:"Edge runtime detected (Vercel Edge/Netlify Edge). WebSockets are not supported in edge functions.",workaround:"Use serverless functions or a different deployment target for WebSocket functionality."};if("undefined"!=typeof process&&process.versions&&process.versions.node){const e=parseInt(process.versions.node.split(".")[0]);return e>=22?void 0!==globalThis.WebSocket?{type:"native",constructor:globalThis.WebSocket}:{type:"unsupported",error:`Node.js ${e} detected but native WebSocket not found.`,workaround:"Provide a WebSocket implementation via the transport option."}:{type:"unsupported",error:`Node.js ${e} detected without native WebSocket support.`,workaround:'For Node.js < 22, install "ws" package and provide it via the transport option:\nimport ws from "ws"\nnew RealtimeClient(url, { transport: ws })'}}return{type:"unsupported",error:"Unknown JavaScript runtime without WebSocket support.",workaround:"Ensure you're running in a supported environment (browser, Node.js, Deno) or provide a custom WebSocket implementation."}}static getWebSocketConstructor(){const e=this.detectEnvironment();if(e.constructor)return e.constructor;let t=e.error||"WebSocket not supported in this environment.";throw e.workaround&&(t+=`\n\nSuggested solution: ${e.workaround}`),new Error(t)}static createWebSocket(e,t){return new(this.getWebSocketConstructor())(e,t)}static isWebSocketSupported(){try{const e=this.detectEnvironment();return"native"===e.type||"ws"===e.type}catch(e){return!1}}}const Lt=1e4;var Mt,Bt,Ut,Ht,Dt,Nt,Vt,qt,Ft,Wt,Kt;(Bt=Mt||(Mt={}))[Bt.connecting=0]="connecting",Bt[Bt.open=1]="open",Bt[Bt.closing=2]="closing",Bt[Bt.closed=3]="closed",(Ht=Ut||(Ut={})).closed="closed",Ht.errored="errored",Ht.joined="joined",Ht.joining="joining",Ht.leaving="leaving",(Nt=Dt||(Dt={})).close="phx_close",Nt.error="phx_error",Nt.join="phx_join",Nt.reply="phx_reply",Nt.leave="phx_leave",Nt.access_token="access_token",(Vt||(Vt={})).websocket="websocket",(Ft=qt||(qt={})).Connecting="connecting",Ft.Open="open",Ft.Closing="closing",Ft.Closed="closed";class Jt{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){const s=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const o=r.decode(e.slice(i,i+s));i+=s;const a=r.decode(e.slice(i,i+n));i+=n;return{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(i,e.byteLength)))}}}class Gt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer),this.timer=void 0}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(Kt=Wt||(Wt={})).abstime="abstime",Kt.bool="bool",Kt.date="date",Kt.daterange="daterange",Kt.float4="float4",Kt.float8="float8",Kt.int2="int2",Kt.int4="int4",Kt.int4range="int4range",Kt.int8="int8",Kt.int8range="int8range",Kt.json="json",Kt.jsonb="jsonb",Kt.money="money",Kt.numeric="numeric",Kt.oid="oid",Kt.reltime="reltime",Kt.text="text",Kt.time="time",Kt.timestamp="timestamp",Kt.timestamptz="timestamptz",Kt.timetz="timetz",Kt.tsrange="tsrange",Kt.tstzrange="tstzrange";const Yt=(e,t,r={})=>{var s;const n=null!==(s=r.skipTypes)&&void 0!==s?s:[];return Object.keys(t).reduce((r,s)=>(r[s]=Xt(s,e,t,n),r),{})},Xt=(e,t,r,s)=>{const n=t.find(t=>t.name===e),i=null==n?void 0:n.type,o=r[e];return i&&!s.includes(i)?Zt(i,o):Qt(o)},Zt=(e,t)=>{if("_"===e.charAt(0)){const r=e.slice(1,e.length);return sr(t,r)}switch(e){case Wt.bool:return er(t);case Wt.float4:case Wt.float8:case Wt.int2:case Wt.int4:case Wt.int8:case Wt.numeric:case Wt.oid:return tr(t);case Wt.json:case Wt.jsonb:return rr(t);case Wt.timestamp:return nr(t);case Wt.abstime:case Wt.date:case Wt.daterange:case Wt.int4range:case Wt.int8range:case Wt.money:case Wt.reltime:case Wt.text:case Wt.time:case Wt.timestamptz:case Wt.timetz:case Wt.tsrange:case Wt.tstzrange:default:return Qt(t)}},Qt=e=>e,er=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},tr=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},rr=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return e}return e},sr=(e,t)=>{if("string"!=typeof e)return e;const r=e.length-1,s=e[r];if("{"===e[0]&&"}"===s){let s;const i=e.slice(1,r);try{s=JSON.parse("["+i+"]")}catch(n){s=i?i.split(","):[]}return s.map(e=>Zt(t,e))}return e},nr=e=>"string"==typeof e?e.replace(" ","T"):e,ir=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")+"/api/broadcast"};class or{constructor(e,t,r={},s=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=s,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var ar,cr,lr,ur,hr,dr,fr,pr;(cr=ar||(ar={})).SYNC="sync",cr.JOIN="join",cr.LEAVE="leave";class vr{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.enabled=!1,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.joinRef=this.channel._joinRef(),this.state=vr.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=vr.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],s()}),this.channel._on(r.diff,{},e=>{const{onJoin:t,onLeave:r,onSync:s}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=vr.syncDiff(this.state,e,t,r),s())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,s){const n=this.cloneDeep(e),i=this.transformState(t),o={},a={};return this.map(n,(e,t)=>{i[e]||(a[e]=t)}),this.map(i,(e,t)=>{const r=n[e];if(r){const s=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),i=t.filter(e=>n.indexOf(e.presence_ref)<0),c=r.filter(e=>s.indexOf(e.presence_ref)<0);i.length>0&&(o[e]=i),c.length>0&&(a[e]=c)}else o[e]=t}),this.syncDiff(n,{joins:o,leaves:a},r,s)}static syncDiff(e,t,r,s){const{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),s||(s=()=>{}),this.map(n,(t,s)=>{var n;const i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(s),i.length>0){const r=e[t].map(e=>e.presence_ref),s=i.filter(e=>r.indexOf(e.presence_ref)<0);e[t].unshift(...s)}r(t,i,s)}),this.map(i,(t,r)=>{let n=e[t];if(!n)return;const i=r.map(e=>e.presence_ref);n=n.filter(e=>i.indexOf(e.presence_ref)<0),e[t]=n,s(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,r)=>{const s=e[r];return t[r]="metas"in s?s.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):s,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(ur=lr||(lr={})).ALL="*",ur.INSERT="INSERT",ur.UPDATE="UPDATE",ur.DELETE="DELETE",(dr=hr||(hr={})).BROADCAST="broadcast",dr.PRESENCE="presence",dr.POSTGRES_CHANGES="postgres_changes",dr.SYSTEM="system",(pr=fr||(fr={})).SUBSCRIBED="SUBSCRIBED",pr.TIMED_OUT="TIMED_OUT",pr.CLOSED="CLOSED",pr.CHANNEL_ERROR="CHANNEL_ERROR";class gr{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=Ut.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:"",enabled:!1},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new or(this,Dt.join,this.params,this.timeout),this.rejoinTimer=new Gt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Ut.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Ut.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Ut.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Ut.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("error",e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=Ut.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Dt.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new vr(this),this.broadcastEndpointURL=ir(this.socket.endPoint),this.private=this.params.config.private||!1}subscribe(t,r=this.timeout){var s,n;if(this.socket.isConnected()||this.socket.connect(),this.state==Ut.closed){const{config:{broadcast:i,presence:o,private:a}}=this.params,c=null!==(n=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map(e=>e.filter))&&void 0!==n?n:[],l=!!this.bindings[hr.PRESENCE]&&this.bindings[hr.PRESENCE].length>0,u={},h={broadcast:i,presence:Object.assign(Object.assign({},o),{enabled:l}),postgres_changes:c,private:a};this.socket.accessTokenValue&&(u.access_token=this.socket.accessTokenValue),this._onError(e=>null==t?void 0:t(fr.CHANNEL_ERROR,e)),this._onClose(()=>null==t?void 0:t(fr.CLOSED)),this.updateJoinPayload(Object.assign({config:h},u)),this.joinedOnce=!0,this._rejoin(r),this.joinPush.receive("ok",r=>e(this,[r],function*({postgres_changes:e}){var r;if(this.socket.setAuth(),void 0!==e){const s=this.bindings.postgres_changes,n=null!==(r=null==s?void 0:s.length)&&void 0!==r?r:0,i=[];for(let r=0;r<n;r++){const n=s[r],{filter:{event:o,schema:a,table:c,filter:l}}=n,u=e&&e[r];if(!u||u.event!==o||u.schema!==a||u.table!==c||u.filter!==l)return this.unsubscribe(),this.state=Ut.errored,void(null==t||t(fr.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},n),{id:u.id}))}return this.bindings.postgres_changes=i,void(t&&t(fr.SUBSCRIBED))}null==t||t(fr.SUBSCRIBED)})).receive("error",e=>{this.state=Ut.errored,null==t||t(fr.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(e).join(", ")||"error")))}).receive("timeout",()=>{null==t||t(fr.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(t){return e(this,arguments,function*(e,t={}){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(){return e(this,arguments,function*(e={}){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,r){return this.state===Ut.joined&&e===hr.PRESENCE&&(this.socket.log("channel",`resubscribe to ${this.topic} due to change in presence callbacks on joined channel`),this.unsubscribe().then(()=>this.subscribe())),this._on(e,t,r)}send(t){return e(this,arguments,function*(e,t={}){var r,s;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var s,n,i;const o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(s=this.params)||void 0===s?void 0:s.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{const{event:i,payload:o}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const e=yield this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return yield null===(s=e.body)||void 0===s?void 0:s.cancel(),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=Ut.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Dt.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(s=>{r=new or(this,Dt.leave,{},e),r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.pushBuffer=[],this.rejoinTimer.reset(),this.joinPush.destroy(),this.state=Ut.closed,this.bindings={}}_fetchWithTimeout(t,r,s){return e(this,null,function*(){const e=new AbortController,n=setTimeout(()=>e.abort(),s),i=yield this.socket.fetch(t,Object.assign(Object.assign({},r),{signal:e.signal}));return clearTimeout(n),i})}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let s=new or(this,e,t,r);return this._canPush()?s.send():this._addToPushBuffer(s),s}_addToPushBuffer(e){if(e.startTimeout(),this.pushBuffer.push(e),this.pushBuffer.length>100){const e=this.pushBuffer.shift();e&&(e.destroy(),this.socket.log("channel",`discarded push due to buffer overflow: ${e.event}`,e.payload))}}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var s,n;const i=e.toLocaleLowerCase(),{close:o,error:a,leave:c,join:l}=Dt;if(r&&[o,a,c,l].indexOf(i)>=0&&r!==this._joinRef())return;let u=this._onMessage(i,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(s=this.bindings.postgres_changes)||void 0===s||s.filter(e=>{var t,r,s;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(s=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===s?void 0:s.toLocaleLowerCase())===i}).map(e=>e.callback(u,r)):null===(n=this.bindings[i])||void 0===n||n.filter(e=>{var r,s,n,o,a,c;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in e){const i=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return i&&(null===(s=t.ids)||void 0===s?void 0:s.includes(i))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(c=null==t?void 0:t.event)||void 0===c?void 0:c.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===i}).map(e=>{if("object"==typeof u&&"ids"in u){const e=u.data,{schema:t,table:r,commit_timestamp:s,type:n,errors:i}=e,o={schema:t,table:r,commit_timestamp:s,eventType:n,new:{},old:{},errors:i};u=Object.assign(Object.assign({},o),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===Ut.closed}_isJoined(){return this.state===Ut.joined}_isJoining(){return this.state===Ut.joining}_isLeaving(){return this.state===Ut.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){const s=e.toLocaleLowerCase(),n={type:s,filter:t,callback:r};return this.bindings[s]?this.bindings[s].push(n):this.bindings[s]=[n],this}_off(e,t){const r=e.toLocaleLowerCase();return this.bindings[r]&&(this.bindings[r]=this.bindings[r].filter(e=>{var s;return!((null===(s=e.type)||void 0===s?void 0:s.toLocaleLowerCase())===r&&gr.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Dt.close,{},e)}_onError(e){this._on(Dt.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Ut.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Yt(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Yt(e.columns,e.old_record)),t}}const yr=()=>{},mr=25e3,br=10,_r=100,wr=[1e3,2e3,5e3,1e4];class kr{constructor(t,r){var s;if(this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Lt,this.transport=null,this.heartbeatIntervalMs=mr,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=yr,this.ref=0,this.reconnectTimer=null,this.logger=yr,this.conn=null,this.sendBuffer=[],this.serializer=new Jt,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._connectionState="disconnected",this._wasManualDisconnect=!1,this._authPromise=null,this._resolveFetch=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>g(()=>e(this,null,function*(){const{default:e}=yield Promise.resolve().then(()=>ct);return{default:e}}),void 0).then(({default:e})=>e(...t)).catch(e=>{throw new Error(`Failed to load @supabase/node-fetch: ${e.message}. This is required for HTTP requests in Node.js environments without native fetch.`)}):fetch),(...e)=>r(...e)},!(null===(s=null==r?void 0:r.params)||void 0===s?void 0:s.apikey))throw new Error("API key is required to connect to Realtime");this.apiKey=r.params.apikey,this.endPoint=`${t}/${Vt.websocket}`,this.httpEndpoint=ir(t),this._initializeOptions(r),this._setupReconnectionTimer(),this.fetch=this._resolveFetch(null==r?void 0:r.fetch)}connect(){if(!(this.isConnecting()||this.isDisconnecting()||null!==this.conn&&this.isConnected())){if(this._setConnectionState("connecting"),this._setAuthSafely("connect"),this.transport)this.conn=new this.transport(this.endpointURL());else try{this.conn=It.createWebSocket(this.endpointURL())}catch(e){this._setConnectionState("disconnected");const t=e.message;if(t.includes("Node.js"))throw new Error(`${t}\n\nTo use Realtime in Node.js, you need to provide a WebSocket implementation:\n\nOption 1: Use Node.js 22+ which has native WebSocket support\nOption 2: Install and provide the "ws" package:\n\n  npm install ws\n\n  import ws from "ws"\n  const client = new RealtimeClient(url, {\n    ...options,\n    transport: ws\n  })`);throw new Error(`WebSocket not available: ${t}`)}this._setupConnectionHandlers()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){if(!this.isDisconnecting())if(this._setConnectionState("disconnecting",!0),this.conn){const r=setTimeout(()=>{this._setConnectionState("disconnected")},100);this.conn.onclose=()=>{clearTimeout(r),this._setConnectionState("disconnected")},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this._teardownConnection()}else this._setConnectionState("disconnected")}getChannels(){return this.channels}removeChannel(t){return e(this,null,function*(){const e=yield t.unsubscribe();return 0===this.channels.length&&this.disconnect(),e})}removeAllChannels(){return e(this,null,function*(){const e=yield Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e})}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Mt.connecting:return qt.Connecting;case Mt.open:return qt.Open;case Mt.closing:return qt.Closing;default:return qt.Closed}}isConnected(){return this.connectionState()===qt.Open}isConnecting(){return"connecting"===this._connectionState}isDisconnecting(){return"disconnecting"===this._connectionState}channel(e,t={config:{}}){const r=`realtime:${e}`,s=this.getChannels().find(e=>e.topic===r);if(s)return s;{const r=new gr(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){const{topic:t,event:r,payload:s,ref:n}=e,i=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${n})`,s),this.isConnected()?i():this.sendBuffer.push(i)}setAuth(t=null){return e(this,null,function*(){this._authPromise=this._performAuth(t);try{yield this._authPromise}finally{this._authPromise=null}})}sendHeartbeat(){return e(this,null,function*(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),this._wasManualDisconnect=!1,null===(e=this.conn)||void 0===e||e.close(1e3,"heartbeat timeout"),void setTimeout(()=>{var e;this.isConnected()||null===(e=this.reconnectTimer)||void 0===e||e.scheduleTimeout()},_r);this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),this._setAuthSafely("heartbeat")}else this.heartbeatCallback("disconnected")})}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}_onConnMessage(e){this.decode(e.data,e=>{"phoenix"===e.topic&&"phx_reply"===e.event&&this.heartbeatCallback("ok"===e.payload.status?"ok":"error"),e.ref&&e.ref===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null);const{topic:t,event:r,payload:s,ref:n}=e,i=n?`(${n})`:"",o=s.status||"";this.log("receive",`${o} ${t} ${r} ${i}`.trim(),s),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,s,n)),this._triggerStateCallbacks("message",e)})}_clearTimer(e){var t;"heartbeat"===e&&this.heartbeatTimer?(clearInterval(this.heartbeatTimer),this.heartbeatTimer=void 0):"reconnect"===e&&(null===(t=this.reconnectTimer)||void 0===t||t.reset())}_clearAllTimers(){this._clearTimer("heartbeat"),this._clearTimer("reconnect")}_setupConnectionHandlers(){this.conn&&("binaryType"in this.conn&&(this.conn.binaryType="arraybuffer"),this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_teardownConnection(){this.conn&&(this.conn.onopen=null,this.conn.onerror=null,this.conn.onmessage=null,this.conn.onclose=null,this.conn=null),this._clearAllTimers(),this.channels.forEach(e=>e.teardown())}_onConnOpen(){this._setConnectionState("connected"),this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this._clearTimer("reconnect"),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this._triggerStateCallbacks("open")}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){var t;this._setConnectionState("disconnected"),this.log("transport","close",e),this._triggerChanError(),this._clearTimer("heartbeat"),this._wasManualDisconnect||null===(t=this.reconnectTimer)||void 0===t||t.scheduleTimeout(),this._triggerStateCallbacks("close",e)}_onConnError(e){this._setConnectionState("disconnected"),this.log("transport",`${e}`),this._triggerChanError(),this._triggerStateCallbacks("error",e)}_triggerChanError(){this.channels.forEach(e=>e._trigger(Dt.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const r=e.match(/\?/)?"&":"?";return`${e}${r}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}_setConnectionState(e,t=!1){this._connectionState=e,"connecting"===e?this._wasManualDisconnect=!1:"disconnecting"===e&&(this._wasManualDisconnect=t)}_performAuth(t=null){return e(this,null,function*(){let e;e=t||(this.accessToken?yield this.accessToken():this.accessTokenValue),this.accessTokenValue!=e&&(this.accessTokenValue=e,this.channels.forEach(t=>{const r={access_token:e,version:"realtime-js/2.15.1"};e&&t.updateJoinPayload(r),t.joinedOnce&&t._isJoined()&&t._push(Dt.access_token,{access_token:e})}))})}_waitForAuthIfNeeded(){return e(this,null,function*(){this._authPromise&&(yield this._authPromise)})}_setAuthSafely(e="general"){this.setAuth().catch(t=>{this.log("error",`error setting auth in ${e}`,t)})}_triggerStateCallbacks(e,t){try{this.stateChangeCallbacks[e].forEach(r=>{try{r(t)}catch(s){this.log("error",`error in ${e} callback`,s)}})}catch(r){this.log("error",`error triggering ${e} callbacks`,r)}}_setupReconnectionTimer(){this.reconnectTimer=new Gt(()=>e(this,null,function*(){setTimeout(()=>e(this,null,function*(){yield this._waitForAuthIfNeeded(),this.isConnected()||this.connect()}),br)}),this.reconnectAfterMs)}_initializeOptions(e){var t,r,s,n,i,o,a,c;if(this.transport=null!==(t=null==e?void 0:e.transport)&&void 0!==t?t:null,this.timeout=null!==(r=null==e?void 0:e.timeout)&&void 0!==r?r:Lt,this.heartbeatIntervalMs=null!==(s=null==e?void 0:e.heartbeatIntervalMs)&&void 0!==s?s:mr,this.worker=null!==(n=null==e?void 0:e.worker)&&void 0!==n&&n,this.accessToken=null!==(i=null==e?void 0:e.accessToken)&&void 0!==i?i:null,(null==e?void 0:e.params)&&(this.params=e.params),(null==e?void 0:e.logger)&&(this.logger=e.logger),((null==e?void 0:e.logLevel)||(null==e?void 0:e.log_level))&&(this.logLevel=e.logLevel||e.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),this.reconnectAfterMs=null!==(o=null==e?void 0:e.reconnectAfterMs)&&void 0!==o?o:e=>wr[e-1]||1e4,this.encode=null!==(a=null==e?void 0:e.encode)&&void 0!==a?a:(e,t)=>t(JSON.stringify(e)),this.decode=null!==(c=null==e?void 0:e.decode)&&void 0!==c?c:this.serializer.decode.bind(this.serializer),this.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.workerUrl=null==e?void 0:e.workerUrl}}}class Sr extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function Tr(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class jr extends Sr{constructor(e,t,r){super(e),this.name="StorageApiError",this.status=t,this.statusCode=r}toJSON(){return{name:this.name,message:this.message,status:this.status,statusCode:this.statusCode}}}class Er extends Sr{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Cr=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Pr=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>g(()=>e(null,null,function*(){const{default:e}=yield Promise.resolve().then(()=>ct);return{default:e}}),void 0).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)},Or=e=>{if(Array.isArray(e))return e.map(e=>Or(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,r])=>{const s=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[s]=Or(r)}),t};var xr=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const zr=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),$r=(e,t,r)=>xr(void 0,void 0,void 0,function*(){const s=yield Cr(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield g(()=>Promise.resolve().then(()=>ct),void 0)).Response:Response});e instanceof s&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{const s=e.status||500,n=(null==r?void 0:r.statusCode)||s+"";t(new jr(zr(r),s,n))}).catch(e=>{t(new Er(zr(e),e))}):t(new Er(zr(e),e))}),Ar=(e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"!==e&&s?((e=>{if("object"!=typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)})(s)?(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n.body=JSON.stringify(s)):n.body=s,(null==t?void 0:t.duplex)&&(n.duplex=t.duplex),Object.assign(Object.assign({},n),r)):n};function Rr(e,t,r,s,n,i){return xr(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,Ar(t,s,n,i)).then(e=>{if(!e.ok)throw e;return(null==s?void 0:s.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>$r(e,a,s))})})}function Ir(e,t,r,s){return xr(this,void 0,void 0,function*(){return Rr(e,"GET",t,r,s)})}function Lr(e,t,r,s,n){return xr(this,void 0,void 0,function*(){return Rr(e,"POST",t,s,n,r)})}function Mr(e,t,r,s,n){return xr(this,void 0,void 0,function*(){return Rr(e,"PUT",t,s,n,r)})}function Br(e,t,r,s,n){return xr(this,void 0,void 0,function*(){return Rr(e,"DELETE",t,s,n,r)})}var Ur=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Hr={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Dr={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Nr{constructor(e,t={},r,s){this.url=e,this.headers=t,this.bucketId=r,this.fetch=Pr(s)}uploadOrUpdate(e,t,r,s){return Ur(this,void 0,void 0,function*(){try{let n;const i=Object.assign(Object.assign({},Dr),s);let o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});const a=i.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(n=r,n.append("cacheControl",i.cacheControl),a&&n.append("metadata",this.encodeMetadata(a))):(n=r,o["cache-control"]=`max-age=${i.cacheControl}`,o["content-type"]=i.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==s?void 0:s.headers)&&(o=Object.assign(Object.assign({},o),s.headers));const c=this._removeEmptyFolders(t),l=this._getFinalPath(c),u=yield("PUT"==e?Mr:Lr)(this.fetch,`${this.url}/object/${l}`,n,Object.assign({headers:o},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{}));return{data:{path:c,id:u.Id,fullPath:u.Key},error:null}}catch(n){if(Tr(n))return{data:null,error:n};throw n}})}upload(e,t,r){return Ur(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,s){return Ur(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),i=this._getFinalPath(n),o=new URL(this.url+`/object/upload/sign/${i}`);o.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:Dr.upsert},s),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);return{data:{path:n,fullPath:(yield Mr(this.fetch,o.toString(),e,{headers:i})).Key},error:null}}catch(a){if(Tr(a))return{data:null,error:a};throw a}})}createSignedUploadUrl(e,t){return Ur(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e);const s=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(s["x-upsert"]="true");const n=yield Lr(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:s}),i=new URL(this.url+n.url),o=i.searchParams.get("token");if(!o)throw new Sr("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:o},error:null}}catch(r){if(Tr(r))return{data:null,error:r};throw r}})}update(e,t,r){return Ur(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return Ur(this,void 0,void 0,function*(){try{return{data:yield Lr(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(s){if(Tr(s))return{data:null,error:s};throw s}})}copy(e,t,r){return Ur(this,void 0,void 0,function*(){try{return{data:{path:(yield Lr(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(s){if(Tr(s))return{data:null,error:s};throw s}})}createSignedUrl(e,t,r){return Ur(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),n=yield Lr(this.fetch,`${this.url}/object/sign/${s}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${i}`)},{data:n,error:null}}catch(s){if(Tr(s))return{data:null,error:s};throw s}})}createSignedUrls(e,t,r){return Ur(this,void 0,void 0,function*(){try{const s=yield Lr(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(s){if(Tr(s))return{data:null,error:s};throw s}})}download(e,t){return Ur(this,void 0,void 0,function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=s?`?${s}`:"";try{const t=this._getFinalPath(e),s=yield Ir(this.fetch,`${this.url}/${r}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield s.blob(),error:null}}catch(i){if(Tr(i))return{data:null,error:i};throw i}})}info(e){return Ur(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield Ir(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Or(e),error:null}}catch(r){if(Tr(r))return{data:null,error:r};throw r}})}exists(e){return Ur(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,r,s){return xr(this,void 0,void 0,function*(){return Rr(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(Tr(r)&&r instanceof Er){const e=r.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:r}}throw r}})}getPublicUrl(e,t){const r=this._getFinalPath(e),s=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&s.push(n);const i=void 0!==(null==t?void 0:t.transform)?"render/image":"object",o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&s.push(o);let a=s.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${r}${a}`)}}}remove(e){return Ur(this,void 0,void 0,function*(){try{return{data:yield Br(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(Tr(t))return{data:null,error:t};throw t}})}list(e,t,r){return Ur(this,void 0,void 0,function*(){try{const s=Object.assign(Object.assign(Object.assign({},Hr),t),{prefix:e||""});return{data:yield Lr(this.fetch,`${this.url}/object/list/${this.bucketId}`,s,{headers:this.headers},r),error:null}}catch(s){if(Tr(s))return{data:null,error:s};throw s}})}listV2(e,t){return Ur(this,void 0,void 0,function*(){try{const r=Object.assign({},e);return{data:yield Lr(this.fetch,`${this.url}/object/list-v2/${this.bucketId}`,r,{headers:this.headers},t),error:null}}catch(r){if(Tr(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e.replace(/^\/+/,"")}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Vr={"X-Client-Info":"storage-js/2.11.0"};var qr=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class Fr{constructor(e,t={},r,s){const n=new URL(e);if(null==s?void 0:s.useNewHostname){/supabase\.(co|in|red)$/.test(n.hostname)&&!n.hostname.includes("storage.supabase.")&&(n.hostname=n.hostname.replace("supabase.","storage.supabase."))}this.url=n.href,this.headers=Object.assign(Object.assign({},Vr),t),this.fetch=Pr(r)}listBuckets(){return qr(this,void 0,void 0,function*(){try{return{data:yield Ir(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(Tr(e))return{data:null,error:e};throw e}})}getBucket(e){return qr(this,void 0,void 0,function*(){try{return{data:yield Ir(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(Tr(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return qr(this,void 0,void 0,function*(){try{return{data:yield Lr(this.fetch,`${this.url}/bucket`,{id:e,name:e,type:t.type,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(Tr(r))return{data:null,error:r};throw r}})}updateBucket(e,t){return qr(this,void 0,void 0,function*(){try{return{data:yield Mr(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(Tr(r))return{data:null,error:r};throw r}})}emptyBucket(e){return qr(this,void 0,void 0,function*(){try{return{data:yield Lr(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(Tr(t))return{data:null,error:t};throw t}})}deleteBucket(e){return qr(this,void 0,void 0,function*(){try{return{data:yield Br(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(Tr(t))return{data:null,error:t};throw t}})}}class Wr extends Fr{constructor(e,t={},r,s){super(e,t,r,s)}from(e){return new Nr(this.url,this.headers,e,this.fetch)}}let Kr="";Kr="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const Jr={headers:{"X-Client-Info":`supabase-js-${Kr}/2.55.0`}},Gr={schema:"public"},Yr={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Xr={};var Zr=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const Qr=(e,t,r)=>{const s=(e=>{let t;return t=e||("undefined"==typeof fetch?nt:fetch),(...e)=>t(...e)})(r),n="undefined"==typeof Headers?it:Headers;return(r,i)=>Zr(void 0,void 0,void 0,function*(){var o;const a=null!==(o=yield t())&&void 0!==o?o:e;let c=new n(null==i?void 0:i.headers);return c.has("apikey")||c.set("apikey",e),c.has("Authorization")||c.set("Authorization",`Bearer ${a}`),s(r,Object.assign(Object.assign({},i),{headers:c}))})};var es=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};const ts="2.71.1",rs=3e4,ss=9e4,ns={"X-Client-Info":`gotrue-js/${ts}`},is="X-Supabase-Api-Version",os={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},as=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class cs extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function ls(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class us extends cs{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class hs extends cs{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class ds extends cs{constructor(e,t,r,s){super(e,r,s),this.name=t,this.status=r}}class fs extends ds{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class ps extends ds{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class vs extends ds{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class gs extends ds{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ys extends ds{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ms extends ds{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function bs(e){return ls(e)&&"AuthRetryableFetchError"===e.name}class _s extends ds{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class ws extends ds{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const ks="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Ss=" \t\n\r=".split(""),Ts=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Ss.length;t+=1)e[Ss[t].charCodeAt(0)]=-2;for(let t=0;t<ks.length;t+=1)e[ks[t].charCodeAt(0)]=t;return e})();function js(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(ks[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;r(ks[e]),t.queuedBits-=6}}function Es(e,t,r){const s=Ts[e];if(!(s>-1)){if(-2===s)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function Cs(e){const t=[],r=e=>{t.push(String.fromCodePoint(e))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return void r(e);for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,s,r)};for(let o=0;o<e.length;o+=1)Es(e.charCodeAt(o),n,i);return t.join("")}function Ps(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function Os(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)Es(e.charCodeAt(n),r,s);return new Uint8Array(t)}function xs(e){const t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const t=1024*(s-55296)&65535;s=65536+(e.charCodeAt(r+1)-56320&65535|t),r+=1}Ps(s,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function zs(e){const t=[],r={queue:0,queuedBits:0},s=e=>{t.push(e)};return e.forEach(e=>js(e,r,s)),js(null,r,s),t.join("")}const $s=()=>"undefined"!=typeof window&&"undefined"!=typeof document,As={tested:!1,writable:!1},Rs=()=>{if(!$s())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(As.tested)return As.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),As.tested=!0,As.writable=!0}catch(t){As.tested=!0,As.writable=!1}return As.writable};const Is=t=>{let r;return r=t||("undefined"==typeof fetch?(...t)=>g(()=>e(null,null,function*(){const{default:e}=yield Promise.resolve().then(()=>ct);return{default:e}}),void 0).then(({default:e})=>e(...t)):fetch),(...e)=>r(...e)},Ls=(t,r,s)=>e(null,null,function*(){yield t.setItem(r,JSON.stringify(s))}),Ms=(t,r)=>e(null,null,function*(){const e=yield t.getItem(r);if(!e)return null;try{return JSON.parse(e)}catch(s){return e}}),Bs=(t,r)=>e(null,null,function*(){yield t.removeItem(r)});class Us{constructor(){this.promise=new Us.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function Hs(e){const t=e.split(".");if(3!==t.length)throw new ws("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!as.test(t[r]))throw new ws("JWT not in base64url format");return{header:JSON.parse(Cs(t[0])),payload:JSON.parse(Cs(t[1])),signature:Os(t[2]),raw:{header:t[0],payload:t[1]}}}function Ds(e){return("0"+e.toString(16)).substr(-2)}function Ns(t){return e(this,null,function*(){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return t;const r=yield function(t){return e(this,null,function*(){const e=(new TextEncoder).encode(t),r=yield crypto.subtle.digest("SHA-256",e),s=new Uint8Array(r);return Array.from(s).map(e=>String.fromCharCode(e)).join("")})}(t);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function Vs(t,r,s=!1){return e(this,null,function*(){const e=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let r="";for(let s=0;s<56;s++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,Ds).join("")}();let n=e;s&&(n+="/PASSWORD_RECOVERY"),yield Ls(t,`${r}-code-verifier`,n);const i=yield Ns(e);return[i,e===i?"plain":"s256"]})}Us.promiseConstructor=Promise;const qs=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const Fs=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function Ws(e){if(!Fs.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}function Ks(){return new Proxy({},{get:(e,t)=>{if("__isUserNotAvailableProxy"===t)return!0;if("symbol"==typeof t){const e=t.toString();if("Symbol(Symbol.toPrimitive)"===e||"Symbol(Symbol.toStringTag)"===e||"Symbol(util.inspect.custom)"===e)return}throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Accessing the "${t}" property of the session object is not supported. Please use getUser() instead.`)},set:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Setting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)},deleteProperty:(e,t)=>{throw new Error(`@supabase/auth-js: client was created with userStorage option and there was no user stored in the user storage. Deleting the "${t}" property of the session object is not supported. Please use getUser() to fetch a user object you can manipulate.`)}})}function Js(e){return JSON.parse(JSON.stringify(e))}const Gs=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ys=[502,503,504];function Xs(t){return e(this,null,function*(){var e,r;if(!("object"==typeof(r=t)&&null!==r&&"status"in r&&"ok"in r&&"json"in r&&"function"==typeof r.json))throw new ms(Gs(t),0);if(Ys.includes(t.status))throw new ms(Gs(t),t.status);let s,n;try{s=yield t.json()}catch(o){throw new hs(Gs(o),o)}const i=function(e){const t=e.headers.get(is);if(!t)return null;if(!t.match(qs))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(o){return null}}(t);if(i&&i.getTime()>=os.timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?n=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(n=s.error_code),n){if("weak_password"===n)throw new _s(Gs(s),t.status,(null===(e=s.weak_password)||void 0===e?void 0:e.reasons)||[]);if("session_not_found"===n)throw new fs}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new _s(Gs(s),t.status,s.weak_password.reasons);throw new us(Gs(s),t.status||500,n)})}function Zs(t,r,s,n){return e(this,null,function*(){var i;const o=Object.assign({},null==n?void 0:n.headers);o[is]||(o[is]=os.name),(null==n?void 0:n.jwt)&&(o.Authorization=`Bearer ${n.jwt}`);const a=null!==(i=null==n?void 0:n.query)&&void 0!==i?i:{};(null==n?void 0:n.redirectTo)&&(a.redirect_to=n.redirectTo);const c=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=yield function(t,r,s,n,i,o){return e(this,null,function*(){const e=((e,t,r,s)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))})(r,n,i,o);let a;try{a=yield t(s,Object.assign({},e))}catch(c){throw new ms(Gs(c),0)}if(a.ok||(yield Xs(a)),null==n?void 0:n.noResolveJson)return a;try{return yield a.json()}catch(c){yield Xs(c)}})}(t,r,s+c,{headers:o,noResolveJson:null==n?void 0:n.noResolveJson},{},null==n?void 0:n.body);return(null==n?void 0:n.xform)?null==n?void 0:n.xform(l):{data:Object.assign({},l),error:null}})}function Qs(e){var t;let r=null;var s;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:r,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function en(e){const t=Qs(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function tn(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function rn(e){return{data:e,error:null}}function sn(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i}=e,o=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i},user:Object.assign({},o)},error:null}}function nn(e){return e}const on=["global","local","others"];class an{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=Is(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(t){return e(this,arguments,function*(e,t=on[0]){if(on.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${on.join(", ")}`);try{return yield Zs(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(r){if(ls(r))return{data:null,error:r};throw r}})}inviteUserByEmail(t){return e(this,arguments,function*(e,t={}){try{return yield Zs(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:tn})}catch(r){if(ls(r))return{data:{user:null},error:r};throw r}})}generateLink(t){return e(this,null,function*(){try{const{options:e}=t,r=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]])}return r}(t,["options"]),s=Object.assign(Object.assign({},r),e);return"newEmail"in r&&(s.new_email=null==r?void 0:r.newEmail,delete s.newEmail),yield Zs(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:s,headers:this.headers,xform:sn,redirectTo:null==e?void 0:e.redirectTo})}catch(e){if(ls(e))return{data:{properties:null,user:null},error:e};throw e}})}createUser(t){return e(this,null,function*(){try{return yield Zs(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:tn})}catch(e){if(ls(e))return{data:{user:null},error:e};throw e}})}listUsers(t){return e(this,null,function*(){var e,r,s,n,i,o,a;try{const c={nextPage:null,lastPage:0,total:0},l=yield Zs(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(e=null==t?void 0:t.page)||void 0===e?void 0:e.toString())&&void 0!==r?r:"",per_page:null!==(n=null===(s=null==t?void 0:t.perPage)||void 0===s?void 0:s.toString())&&void 0!==n?n:""},xform:nn});if(l.error)throw l.error;const u=yield l.json(),h=null!==(i=l.headers.get("x-total-count"))&&void 0!==i?i:0,d=null!==(a=null===(o=l.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);c[`${r}Page`]=t}),c.total=parseInt(h)),{data:Object.assign(Object.assign({},u),c),error:null}}catch(c){if(ls(c))return{data:{users:[]},error:c};throw c}})}getUserById(t){return e(this,null,function*(){Ws(t);try{return yield Zs(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:tn})}catch(e){if(ls(e))return{data:{user:null},error:e};throw e}})}updateUserById(t,r){return e(this,null,function*(){Ws(t);try{return yield Zs(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:r,headers:this.headers,xform:tn})}catch(e){if(ls(e))return{data:{user:null},error:e};throw e}})}deleteUser(t,r=!1){return e(this,null,function*(){Ws(t);try{return yield Zs(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:r},xform:tn})}catch(e){if(ls(e))return{data:{user:null},error:e};throw e}})}_listFactors(t){return e(this,null,function*(){Ws(t.userId);try{const{data:e,error:r}=yield Zs(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:e,error:r}}catch(e){if(ls(e))return{data:null,error:e};throw e}})}_deleteFactor(t){return e(this,null,function*(){Ws(t.userId),Ws(t.id);try{return{data:yield Zs(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(e){if(ls(e))return{data:null,error:e};throw e}})}}function cn(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}const ln=!!(globalThis&&Rs()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class un extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class hn extends un{}function dn(t,r,s){return e(this,null,function*(){const n=new globalThis.AbortController;return r>0&&setTimeout(()=>{n.abort()},r),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(t,0===r?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},n=>e(null,null,function*(){if(!n){if(0===r)throw new hn(`Acquiring an exclusive Navigator LockManager lock "${t}" immediately failed`);if(ln)try{yield globalThis.navigator.locks.query()}catch(e){}return yield s()}try{return yield s()}finally{}})))})}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const fn={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ns,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function pn(t,r,s){return e(this,null,function*(){return yield s()})}const vn={};class gn{constructor(t){var r,s;this.userStorage=null,this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=gn.nextInstanceID,gn.nextInstanceID+=1,this.instanceID>0&&$s();const n=Object.assign(Object.assign({},fn),t);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new an({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=Is(n.fetch),this.lock=n.lock||pn,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:$s()&&(null===(r=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===r?void 0:r.locks)?this.lock=dn:this.lock=pn,this.jwks||(this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER),this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?(n.storage?this.storage=n.storage:Rs()?this.storage=globalThis.localStorage:(this.memoryStorage={},this.storage=cn(this.memoryStorage)),n.userStorage&&(this.userStorage=n.userStorage)):(this.memoryStorage={},this.storage=cn(this.memoryStorage)),$s()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",t=>e(this,null,function*(){this._debug("received broadcast notification from other tab or client",t),yield this._notifyAllSubscribers(t.data.event,t.data.session,!1)}))}this.initialize()}get jwks(){var e,t;return null!==(t=null===(e=vn[this.storageKey])||void 0===e?void 0:e.jwks)&&void 0!==t?t:{keys:[]}}set jwks(e){vn[this.storageKey]=Object.assign(Object.assign({},vn[this.storageKey]),{jwks:e})}get jwks_cached_at(){var e,t;return null!==(t=null===(e=vn[this.storageKey])||void 0===e?void 0:e.cachedAt)&&void 0!==t?t:Number.MIN_SAFE_INTEGER}set jwks_cached_at(e){vn[this.storageKey]=Object.assign(Object.assign({},vn[this.storageKey]),{cachedAt:e})}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ts}) ${(new Date).toISOString()}`,...e),this}initialize(){return e(this,null,function*(){return this.initializePromise||(this.initializePromise=(()=>e(this,null,function*(){return yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._initialize()}))}))()),yield this.initializePromise})}_initialize(){return e(this,null,function*(){var t;try{const r=function(e){const t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(s){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(r)?s="implicit":(yield this._isPKCECallback(r))&&(s="pkce"),$s()&&this.detectSessionInUrl&&"none"!==s){const{data:n,error:i}=yield this._getSessionFromURL(r,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),function(e){return ls(e)&&"AuthImplicitGrantRedirectError"===e.name}(i)){const e=null===(t=i.details)||void 0===t?void 0:t.code;if("identity_already_exists"===e||"identity_not_found"===e||"single_identity_not_deletable"===e)return{error:i}}return yield this._removeSession(),{error:i}}const{session:o,redirectType:a}=n;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),yield this._saveSession(o),setTimeout(()=>e(this,null,function*(){"recovery"===a?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",o):yield this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(r){return ls(r)?{error:r}:{error:new hs("Unexpected error during initialization",r)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(t){return e(this,null,function*(){var e,r,s;try{const n=yield Zs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(s=null==t?void 0:t.options)||void 0===s?void 0:s.captchaToken}},xform:Qs}),{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(yield this._saveSession(i.session),yield this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(ls(n))return{data:{user:null,session:null},error:n};throw n}})}signUp(t){return e(this,null,function*(){var e,r,s;try{let n;if("email"in t){const{email:r,password:s,options:i}=t;let o=null,a=null;"pkce"===this.flowType&&([o,a]=yield Vs(this.storage,this.storageKey)),n=yield Zs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:r,password:s,data:null!==(e=null==i?void 0:i.data)&&void 0!==e?e:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:a},xform:Qs})}else{if(!("phone"in t))throw new vs("You must provide either an email or phone number and a password");{const{phone:e,password:i,options:o}=t;n=yield Zs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:e,password:i,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(s=null==o?void 0:o.channel)&&void 0!==s?s:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:Qs})}}const{data:i,error:o}=n;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,c=i.user;return i.session&&(yield this._saveSession(i.session),yield this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(n){if(ls(n))return{data:{user:null,session:null},error:n};throw n}})}signInWithPassword(t){return e(this,null,function*(){try{let e;if("email"in t){const{email:r,password:s,options:n}=t;e=yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:en})}else{if(!("phone"in t))throw new vs("You must provide either an email or phone number and a password");{const{phone:r,password:s,options:n}=t;e=yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:en})}}const{data:r,error:s}=e;return s?{data:{user:null,session:null},error:s}:r&&r.session&&r.user?(r.session&&(yield this._saveSession(r.session),yield this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:s}):{data:{user:null,session:null},error:new ps}}catch(e){if(ls(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOAuth(t){return e(this,null,function*(){var e,r,s,n;return yield this._handleProviderSignIn(t.provider,{redirectTo:null===(e=t.options)||void 0===e?void 0:e.redirectTo,scopes:null===(r=t.options)||void 0===r?void 0:r.scopes,queryParams:null===(s=t.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:null===(n=t.options)||void 0===n?void 0:n.skipBrowserRedirect})})}exchangeCodeForSession(t){return e(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>e(this,null,function*(){return this._exchangeCodeForSession(t)}))})}signInWithWeb3(t){return e(this,null,function*(){const{chain:e}=t;if("solana"===e)return yield this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${e}"`)})}signInWithSolana(t){return e(this,null,function*(){var e,r,s,n,i,o,a,c,l,u,h,d;let f,p;if("message"in t)f=t.message,p=t.signature;else{const{chain:h,wallet:d,statement:v,options:g}=t;let y;if($s())if("object"==typeof d)y=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");y=e.solana}else{if("object"!=typeof d||!(null==g?void 0:g.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=d}const m=new URL(null!==(e=null==g?void 0:g.url)&&void 0!==e?e:window.location.href);if("signIn"in y&&y.signIn){const e=yield y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==g?void 0:g.signInWithSolana),{version:"1",domain:m.host,uri:m.href}),v?{statement:v}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in y&&"function"==typeof y.signMessage&&"publicKey"in y&&"object"==typeof y&&y.publicKey&&"toBase58"in y.publicKey&&"function"==typeof y.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${m.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...v?["",v,""]:[""],"Version: 1",`URI: ${m.href}`,`Issued At: ${null!==(s=null===(r=null==g?void 0:g.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==s?s:(new Date).toISOString()}`,...(null===(n=null==g?void 0:g.signInWithSolana)||void 0===n?void 0:n.notBefore)?[`Not Before: ${g.signInWithSolana.notBefore}`]:[],...(null===(i=null==g?void 0:g.signInWithSolana)||void 0===i?void 0:i.expirationTime)?[`Expiration Time: ${g.signInWithSolana.expirationTime}`]:[],...(null===(o=null==g?void 0:g.signInWithSolana)||void 0===o?void 0:o.chainId)?[`Chain ID: ${g.signInWithSolana.chainId}`]:[],...(null===(a=null==g?void 0:g.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${g.signInWithSolana.nonce}`]:[],...(null===(c=null==g?void 0:g.signInWithSolana)||void 0===c?void 0:c.requestId)?[`Request ID: ${g.signInWithSolana.requestId}`]:[],...(null===(u=null===(l=null==g?void 0:g.signInWithSolana)||void 0===l?void 0:l.resources)||void 0===u?void 0:u.length)?["Resources",...g.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=yield y.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:e,error:r}=yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:zs(p)},(null===(h=t.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=t.options)||void 0===d?void 0:d.captchaToken}}:null),xform:Qs});if(r)throw r;return e&&e.session&&e.user?(e.session&&(yield this._saveSession(e.session),yield this._notifyAllSubscribers("SIGNED_IN",e.session)),{data:Object.assign({},e),error:r}):{data:{user:null,session:null},error:new ps}}catch(v){if(ls(v))return{data:{user:null,session:null},error:v};throw v}})}_exchangeCodeForSession(t){return e(this,null,function*(){const e=yield Ms(this.storage,`${this.storageKey}-code-verifier`),[r,s]=(null!=e?e:"").split("/");try{const{data:e,error:n}=yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:Qs});if(yield Bs(this.storage,`${this.storageKey}-code-verifier`),n)throw n;return e&&e.session&&e.user?(e.session&&(yield this._saveSession(e.session),yield this._notifyAllSubscribers("SIGNED_IN",e.session)),{data:Object.assign(Object.assign({},e),{redirectType:null!=s?s:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new ps}}catch(n){if(ls(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}})}signInWithIdToken(t){return e(this,null,function*(){try{const{options:e,provider:r,token:s,access_token:n,nonce:i}=t,o=yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:s,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:Qs}),{data:a,error:c}=o;return c?{data:{user:null,session:null},error:c}:a&&a.session&&a.user?(a.session&&(yield this._saveSession(a.session),yield this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:c}):{data:{user:null,session:null},error:new ps}}catch(e){if(ls(e))return{data:{user:null,session:null},error:e};throw e}})}signInWithOtp(t){return e(this,null,function*(){var e,r,s,n,i;try{if("email"in t){const{email:s,options:n}=t;let i=null,o=null;"pkce"===this.flowType&&([i,o]=yield Vs(this.storage,this.storageKey));const{error:a}=yield Zs(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:s,data:null!==(e=null==n?void 0:n.data)&&void 0!==e?e:{},create_user:null===(r=null==n?void 0:n.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:o},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in t){const{phone:e,options:r}=t,{data:o,error:a}=yield Zs(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:e,data:null!==(s=null==r?void 0:r.data)&&void 0!==s?s:{},create_user:null===(n=null==r?void 0:r.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(i=null==r?void 0:r.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new vs("You must provide either an email or phone number.")}catch(o){if(ls(o))return{data:{user:null,session:null},error:o};throw o}})}verifyOtp(t){return e(this,null,function*(){var e,r;try{let s,n;"options"in t&&(s=null===(e=t.options)||void 0===e?void 0:e.redirectTo,n=null===(r=t.options)||void 0===r?void 0:r.captchaToken);const{data:i,error:o}=yield Zs(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:n}}),redirectTo:s,xform:Qs});if(o)throw o;if(!i)throw new Error("An error occurred on token verification.");const a=i.session,c=i.user;return(null==a?void 0:a.access_token)&&(yield this._saveSession(a),yield this._notifyAllSubscribers("recovery"==t.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:c,session:a},error:null}}catch(s){if(ls(s))return{data:{user:null,session:null},error:s};throw s}})}signInWithSSO(t){return e(this,null,function*(){var e,r,s;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=yield Vs(this.storage,this.storageKey)),yield Zs(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:null!==(r=null===(e=t.options)||void 0===e?void 0:e.redirectTo)&&void 0!==r?r:void 0}),(null===(s=null==t?void 0:t.options)||void 0===s?void 0:s.captchaToken)?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:rn})}catch(n){if(ls(n))return{data:null,error:n};throw n}})}reauthenticate(){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return e(this,null,function*(){try{return yield this._useSession(t=>e(this,null,function*(){const{data:{session:e},error:r}=t;if(r)throw r;if(!e)throw new fs;const{error:s}=yield Zs(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:e.access_token});return{data:{user:null,session:null},error:s}}))}catch(t){if(ls(t))return{data:{user:null,session:null},error:t};throw t}})}resend(t){return e(this,null,function*(){try{const e=`${this.url}/resend`;if("email"in t){const{email:r,type:s,options:n}=t,{error:i}=yield Zs(this.fetch,"POST",e,{headers:this.headers,body:{email:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in t){const{phone:r,type:s,options:n}=t,{data:i,error:o}=yield Zs(this.fetch,"POST",e,{headers:this.headers,body:{phone:r,type:s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:o}}throw new vs("You must provide either an email or phone number and a type")}catch(e){if(ls(e))return{data:{user:null,session:null},error:e};throw e}})}getSession(){return e(this,null,function*(){yield this.initializePromise;return yield this._acquireLock(-1,()=>e(this,null,function*(){return this._useSession(t=>e(this,null,function*(){return t}))}))})}_acquireLock(t,r){return e(this,null,function*(){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const t=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(()=>e(this,null,function*(){return yield t,yield r()}))();return this.pendingInLock.push((()=>e(this,null,function*(){try{yield s}catch(e){}}))()),s}return yield this.lock(`lock:${this.storageKey}`,t,()=>e(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const t=r();for(this.pendingInLock.push((()=>e(this,null,function*(){try{yield t}catch(e){}}))()),yield t;this.pendingInLock.length;){const e=[...this.pendingInLock];yield Promise.all(e),this.pendingInLock.splice(0,e.length)}return yield t}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(t){return e(this,null,function*(){this._debug("#_useSession","begin");try{const e=yield this.__loadSession();return yield t(e)}finally{this._debug("#_useSession","end")}})}__loadSession(){return e(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=yield Ms(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};const r=!!e.expires_at&&1e3*e.expires_at-Date.now()<ss;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.userStorage){const t=yield Ms(this.userStorage,this.storageKey+"-user");(null==t?void 0:t.user)?e.user=t.user:e.user=Ks()}if(this.storage.isServer&&e.user){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,s)=>(t||"user"!==r||(t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,s))})}return{data:{session:e},error:null}}const{session:s,error:n}=yield this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(t){return e(this,null,function*(){if(t)return yield this._getUser(t);yield this.initializePromise;return yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._getUser()}))})}_getUser(t){return e(this,null,function*(){try{return t?yield Zs(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:tn}):yield this._useSession(t=>e(this,null,function*(){var e,r,s;const{data:n,error:i}=t;if(i)throw i;return(null===(e=n.session)||void 0===e?void 0:e.access_token)||this.hasCustomAuthorizationHeader?yield Zs(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(s=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==s?s:void 0,xform:tn}):{data:{user:null},error:new fs}}))}catch(r){if(ls(r))return function(e){return ls(e)&&"AuthSessionMissingError"===e.name}(r)&&(yield this._removeSession(),yield Bs(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:r};throw r}})}updateUser(t){return e(this,arguments,function*(t,r={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._updateUser(t,r)}))})}_updateUser(t){return e(this,arguments,function*(t,r={}){try{return yield this._useSession(s=>e(this,null,function*(){const{data:e,error:n}=s;if(n)throw n;if(!e.session)throw new fs;const i=e.session;let o=null,a=null;"pkce"===this.flowType&&null!=t.email&&([o,a]=yield Vs(this.storage,this.storageKey));const{data:c,error:l}=yield Zs(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==r?void 0:r.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:o,code_challenge_method:a}),jwt:i.access_token,xform:tn});if(l)throw l;return i.user=c.user,yield this._saveSession(i),yield this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}}))}catch(s){if(ls(s))return{data:{user:null},error:s};throw s}})}setSession(t){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._setSession(t)}))})}_setSession(t){return e(this,null,function*(){try{if(!t.access_token||!t.refresh_token)throw new fs;const e=Date.now()/1e3;let r=e,s=!0,n=null;const{payload:i}=Hs(t.access_token);if(i.exp&&(r=i.exp,s=r<=e),s){const{session:e,error:r}=yield this._callRefreshToken(t.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!e)return{data:{user:null,session:null},error:null};n=e}else{const{data:s,error:i}=yield this._getUser(t.access_token);if(i)throw i;n={access_token:t.access_token,refresh_token:t.refresh_token,user:s.user,token_type:"bearer",expires_in:r-e,expires_at:r},yield this._saveSession(n),yield this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(e){if(ls(e))return{data:{session:null,user:null},error:e};throw e}})}refreshSession(t){return e(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._refreshSession(t)}))})}_refreshSession(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;if(!t){const{data:s,error:n}=r;if(n)throw n;t=null!==(e=s.session)&&void 0!==e?e:void 0}if(!(null==t?void 0:t.refresh_token))throw new fs;const{session:s,error:n}=yield this._callRefreshToken(t.refresh_token);return n?{data:{user:null,session:null},error:n}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}}))}catch(r){if(ls(r))return{data:{user:null,session:null},error:r};throw r}})}_getSessionFromURL(t,r){return e(this,null,function*(){try{if(!$s())throw new gs("No browser detected.");if(t.error||t.error_description||t.error_code)throw new gs(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(r){case"implicit":if("pkce"===this.flowType)throw new ys("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new gs("Not a valid implicit grant flow url.")}if("pkce"===r){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new ys("No code detected.");const{data:e,error:r}=yield this._exchangeCodeForSession(t.code);if(r)throw r;const s=new URL(window.location.href);return s.searchParams.delete("code"),window.history.replaceState(window.history.state,"",s.toString()),{data:{session:e.session,redirectType:null},error:null}}const{provider_token:e,provider_refresh_token:s,access_token:n,refresh_token:i,expires_in:o,expires_at:a,token_type:c}=t;if(!(n&&o&&i&&c))throw new gs("No session defined in URL");const l=Math.round(Date.now()/1e3),u=parseInt(o);let h=l+u;a&&(h=parseInt(a));const{data:d,error:f}=yield this._getUser(n);if(f)throw f;const p={provider_token:e,provider_refresh_token:s,access_token:n,expires_in:u,expires_at:h,refresh_token:i,token_type:c,user:d.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:p,redirectType:t.type},error:null}}catch(e){if(ls(e))return{data:{session:null,redirectType:null},error:e};throw e}})}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}_isPKCECallback(t){return e(this,null,function*(){const e=yield Ms(this.storage,`${this.storageKey}-code-verifier`);return!(!t.code||!e)})}signOut(){return e(this,arguments,function*(t={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){return yield this._signOut(t)}))})}_signOut(){return e(this,arguments,function*({scope:t}={scope:"global"}){return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:n}=r;if(n)return{error:n};const i=null===(e=s.session)||void 0===e?void 0:e.access_token;if(i){const{error:e}=yield this.admin.signOut(i,t);if(e&&(!function(e){return ls(e)&&"AuthApiError"===e.name}(e)||404!==e.status&&401!==e.status&&403!==e.status))return{error:e}}return"others"!==t&&(yield this._removeSession(),yield Bs(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(t){const r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:r,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",r),this.stateChangeEmitters.delete(r)}};return this._debug("#onAuthStateChange()","registered callback with id",r),this.stateChangeEmitters.set(r,s),(()=>{e(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){this._emitInitialSession(r)}))})})(),{data:{subscription:s}}}_emitInitialSession(t){return e(this,null,function*(){return yield this._useSession(r=>e(this,null,function*(){var e,s;try{const{data:{session:s},error:n}=r;if(n)throw n;yield null===(e=this.stateChangeEmitters.get(t))||void 0===e?void 0:e.callback("INITIAL_SESSION",s),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(n){yield null===(s=this.stateChangeEmitters.get(t))||void 0===s?void 0:s.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",t,"error",n)}}))})}resetPasswordForEmail(t){return e(this,arguments,function*(e,t={}){let r=null,s=null;"pkce"===this.flowType&&([r,s]=yield Vs(this.storage,this.storageKey,!0));try{return yield Zs(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:s,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(ls(n))return{data:null,error:n};throw n}})}getUserIdentities(){return e(this,null,function*(){var e;try{const{data:t,error:r}=yield this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(ls(t))return{data:null,error:t};throw t}})}linkIdentity(t){return e(this,null,function*(){var r;try{const{data:s,error:n}=yield this._useSession(r=>e(this,null,function*(){var e,s,n,i,o;const{data:a,error:c}=r;if(c)throw c;const l=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:null===(e=t.options)||void 0===e?void 0:e.redirectTo,scopes:null===(s=t.options)||void 0===s?void 0:s.scopes,queryParams:null===(n=t.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return yield Zs(this.fetch,"GET",l,{headers:this.headers,jwt:null!==(o=null===(i=a.session)||void 0===i?void 0:i.access_token)&&void 0!==o?o:void 0})}));if(n)throw n;return $s()&&!(null===(r=t.options)||void 0===r?void 0:r.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:t.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(ls(s))return{data:{provider:t.provider,url:null},error:s};throw s}})}unlinkIdentity(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e,s;const{data:n,error:i}=r;if(i)throw i;return yield Zs(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:null!==(s=null===(e=n.session)||void 0===e?void 0:e.access_token)&&void 0!==s?s:void 0})}))}catch(r){if(ls(r))return{data:null,error:r};throw r}})}_refreshAccessToken(t){return e(this,null,function*(){const r=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(r,"begin");try{const i=Date.now();return yield(s=s=>e(this,null,function*(){return s>0&&(yield function(t){return e(this,null,function*(){return yield new Promise(e=>{setTimeout(()=>e(null),t)})})}(200*Math.pow(2,s-1))),this._debug(r,"refreshing attempt",s),yield Zs(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:Qs})}),n=(e,t)=>{const r=200*Math.pow(2,e);return t&&bs(t)&&Date.now()+r-i<rs},new Promise((t,r)=>{e(null,null,function*(){for(let i=0;i<1/0;i++)try{const e=yield s(i);if(!n(i,null,e))return void t(e)}catch(e){if(!n(i,e))return void r(e)}})}))}catch(i){if(this._debug(r,"error",i),ls(i))return{data:{session:null,user:null},error:i};throw i}finally{this._debug(r,"end")}var s,n})}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(t,r){return e(this,null,function*(){const e=yield this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:r.redirectTo,scopes:r.scopes,queryParams:r.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",r,"url",e),$s()&&!r.skipBrowserRedirect&&window.location.assign(e),{data:{provider:t,url:e},error:null}})}_recoverAndRefresh(){return e(this,null,function*(){var e,t;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const n=yield Ms(this.storage,this.storageKey);if(n&&this.userStorage){let t=yield Ms(this.userStorage,this.storageKey+"-user");this.storage.isServer||!Object.is(this.storage,this.userStorage)||t||(t={user:n.user},yield Ls(this.userStorage,this.storageKey+"-user",t)),n.user=null!==(e=null==t?void 0:t.user)&&void 0!==e?e:Ks()}else if(n&&!n.user&&!n.user){const e=yield Ms(this.storage,this.storageKey+"-user");e&&(null==e?void 0:e.user)?(n.user=e.user,yield Bs(this.storage,this.storageKey+"-user"),yield Ls(this.storage,this.storageKey,n)):n.user=Ks()}if(this._debug(r,"session from storage",n),!this._isValidSession(n))return this._debug(r,"session is not valid"),void(null!==n&&(yield this._removeSession()));const i=1e3*(null!==(t=n.expires_at)&&void 0!==t?t:1/0)-Date.now()<ss;if(this._debug(r,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&n.refresh_token){const{error:e}=yield this._callRefreshToken(n.refresh_token);e&&(bs(e)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",e),yield this._removeSession()))}}else if(n.user&&!0===n.user.__isUserNotAvailableProxy)try{const{data:e,error:t}=yield this._getUser(n.access_token);!t&&(null==e?void 0:e.user)?(n.user=e.user,yield this._saveSession(n),yield this._notifyAllSubscribers("SIGNED_IN",n)):this._debug(r,"could not get user data, skipping SIGNED_IN notification")}catch(s){this._debug(r,"error getting user data, skipping SIGNED_IN notification",s)}else yield this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){return void this._debug(r,"error",n)}finally{this._debug(r,"end")}})}_callRefreshToken(t){return e(this,null,function*(){var e,r;if(!t)throw new fs;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const s=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Us;const{data:e,error:r}=yield this._refreshAccessToken(t);if(r)throw r;if(!e.session)throw new fs;yield this._saveSession(e.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",e.session);const s={session:e.session,error:null};return this.refreshingDeferred.resolve(s),s}catch(n){if(this._debug(s,"error",n),ls(n)){const t={session:null,error:n};return bs(n)||(yield this._removeSession()),null===(e=this.refreshingDeferred)||void 0===e||e.resolve(t),t}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(n),n}finally{this.refreshingDeferred=null,this._debug(s,"end")}})}_notifyAllSubscribers(t,r,s=!0){return e(this,null,function*(){const n=`#_notifyAllSubscribers(${t})`;this._debug(n,"begin",r,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:t,session:r});const n=[],i=Array.from(this.stateChangeEmitters.values()).map(s=>e(this,null,function*(){try{yield s.callback(t,r)}catch(e){n.push(e)}}));if(yield Promise.all(i),n.length>0){for(let e=0;e<n.length;e+=1);throw n[0]}}finally{this._debug(n,"end")}})}_saveSession(t){return e(this,null,function*(){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0;const e=Object.assign({},t),r=e.user&&!0===e.user.__isUserNotAvailableProxy;if(this.userStorage){!r&&e.user&&(yield Ls(this.userStorage,this.storageKey+"-user",{user:e.user}));const t=Object.assign({},e);delete t.user;const s=Js(t);yield Ls(this.storage,this.storageKey,s)}else{const t=Js(e);yield Ls(this.storage,this.storageKey,t)}})}_removeSession(){return e(this,null,function*(){this._debug("#_removeSession()"),yield Bs(this.storage,this.storageKey),yield Bs(this.storage,this.storageKey+"-code-verifier"),yield Bs(this.storage,this.storageKey+"-user"),this.userStorage&&(yield Bs(this.userStorage,this.storageKey+"-user")),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&$s()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}_startAutoRefresh(){return e(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),rs);this.autoRefreshTicker=t,t&&"object"==typeof t&&"function"==typeof t.unref?t.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(t),setTimeout(()=>e(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return e(this,null,function*(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return e(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return e(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return e(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>e(this,null,function*(){try{const r=Date.now();try{return yield this._useSession(t=>e(this,null,function*(){const{data:{session:e}}=t;if(!e||!e.refresh_token||!e.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const s=Math.floor((1e3*e.expires_at-r)/rs);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),s<=3&&(yield this._callRefreshToken(e.refresh_token))}))}catch(t){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(t){if(!(t.isAcquireTimeout||t instanceof un))throw t;this._debug("auto refresh token tick lock not available")}})}_handleVisibilityChange(){return e(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!$s()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>e(this,null,function*(){return yield this._onVisibilityChanged(!1)}),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(t){}})}_onVisibilityChanged(t){return e(this,null,function*(){const r=`#_onVisibilityChanged(${t})`;this._debug(r,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),t||(yield this.initializePromise,yield this._acquireLock(-1,()=>e(this,null,function*(){"visible"===document.visibilityState?yield this._recoverAndRefresh():this._debug(r,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(t,r,s){return e(this,null,function*(){const e=[`provider=${encodeURIComponent(r)}`];if((null==s?void 0:s.redirectTo)&&e.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&e.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[t,r]=yield Vs(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(t)}`,code_challenge_method:`${encodeURIComponent(r)}`});e.push(s.toString())}if(null==s?void 0:s.queryParams){const t=new URLSearchParams(s.queryParams);e.push(t.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&e.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${t}?${e.join("&")}`})}_unenroll(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:n}=r;return n?{data:null,error:n}:yield Zs(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token})}))}catch(r){if(ls(r))return{data:null,error:r};throw r}})}_enroll(t){return e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e,s;const{data:n,error:i}=r;if(i)return{data:null,error:i};const o=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},"phone"===t.factorType?{phone:t.phone}:{issuer:t.issuer}),{data:a,error:c}=yield Zs(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(e=null==n?void 0:n.session)||void 0===e?void 0:e.access_token});return c?{data:null,error:c}:("totp"===t.factorType&&(null===(s=null==a?void 0:a.totp)||void 0===s?void 0:s.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})}))}catch(r){if(ls(r))return{data:null,error:r};throw r}})}_verify(t){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:n}=r;if(n)return{data:null,error:n};const{data:i,error:o}=yield Zs(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token});return o?{data:null,error:o}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:o})}))}catch(r){if(ls(r))return{data:null,error:r};throw r}}))})}_challenge(t){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){try{return yield this._useSession(r=>e(this,null,function*(){var e;const{data:s,error:n}=r;return n?{data:null,error:n}:yield Zs(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:null===(e=null==s?void 0:s.session)||void 0===e?void 0:e.access_token})}))}catch(r){if(ls(r))return{data:null,error:r};throw r}}))})}_challengeAndVerify(t){return e(this,null,function*(){const{data:e,error:r}=yield this._challenge({factorId:t.factorId});return r?{data:null,error:r}:yield this._verify({factorId:t.factorId,challengeId:e.id,code:t.code})})}_listFactors(){return e(this,null,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const r=(null==e?void 0:e.factors)||[],s=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:s,phone:n},error:null}})}_getAuthenticatorAssuranceLevel(){return e(this,null,function*(){return this._acquireLock(-1,()=>e(this,null,function*(){return yield this._useSession(t=>e(this,null,function*(){var e,r;const{data:{session:s},error:n}=t;if(n)return{data:null,error:n};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=Hs(s.access_token);let o=null;i.aal&&(o=i.aal);let a=o;(null!==(r=null===(e=s.user.factors)||void 0===e?void 0:e.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2");return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:i.amr||[]},error:null}}))}))})}fetchJwk(t){return e(this,arguments,function*(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r)return r;const s=Date.now();if(r=this.jwks.keys.find(t=>t.kid===e),r&&this.jwks_cached_at+6e5>s)return r;const{data:n,error:i}=yield Zs(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;return n.keys&&0!==n.keys.length?(this.jwks=n,this.jwks_cached_at=s,r=n.keys.find(t=>t.kid===e),r||null):null})}getClaims(t){return e(this,arguments,function*(e,t={}){try{let r=e;if(!r){const{data:e,error:t}=yield this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}const{header:s,payload:n,signature:i,raw:{header:o,payload:a}}=Hs(r);(null==t?void 0:t.allowExpired)||function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp);const c=s.alg&&!s.alg.startsWith("HS")&&s.kid&&"crypto"in globalThis&&"subtle"in globalThis.crypto?yield this.fetchJwk(s.kid,(null==t?void 0:t.keys)?{keys:t.keys}:null==t?void 0:t.jwks):null;if(!c){const{error:e}=yield this.getUser(r);if(e)throw e;return{data:{claims:n,header:s,signature:i},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(s.alg),u=yield crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(yield crypto.subtle.verify(l,u,i,xs(`${o}.${a}`))))throw new ws("Invalid JWT signature");return{data:{claims:n,header:s,signature:i},error:null}}catch(r){if(ls(r))return{data:null,error:r};throw r}})}}gn.nextInstanceID=0;const yn=gn;class mn extends yn{constructor(e){super(e)}}var bn=function(e,t,r,s){return new(r||(r=Promise))(function(n,i){function o(e){try{c(s.next(e))}catch(t){i(t)}}function a(e){try{c(s.throw(e))}catch(t){i(t)}}function c(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r(function(e){e(t)})).then(o,a)}c((s=s.apply(e,t||[])).next())})};class _n{constructor(e,t,r){var s,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=(a=e).endsWith("/")?a:a+"/";var a;const c=new URL(o);this.realtimeUrl=new URL("realtime/v1",c),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",c),this.storageUrl=new URL("storage/v1",c),this.functionsUrl=new URL("functions/v1",c);const l=`sb-${c.hostname.split(".")[0]}-auth-token`,u=function(e,t){var r,s;const{db:n,auth:i,realtime:o,global:a}=e,{db:c,auth:l,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},c),n),auth:Object.assign(Object.assign({},l),i),realtime:Object.assign(Object.assign({},u),o),storage:{},global:Object.assign(Object.assign(Object.assign({},h),a),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(s=null==a?void 0:a.headers)&&void 0!==s?s:{})}),accessToken:()=>es(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:Gr,realtime:Xr,auth:Object.assign(Object.assign({},Yr),{storageKey:l}),global:Jr});this.storageKey=null!==(s=u.auth.storageKey)&&void 0!==s?s:"",this.headers=null!==(n=u.global.headers)&&void 0!==n?n:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=u.auth)&&void 0!==i?i:{},this.headers,u.global.fetch),this.fetch=Qr(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new Ot(new URL("rest/v1",c).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),this.storage=new Wr(this.storageUrl.href,this.headers,this.fetch,null==r?void 0:r.storage),u.accessToken||this._listenForAuthEvents()}get functions(){return new Ge(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return bn(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:this.supabaseKey})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,storageKey:n,flowType:i,lock:o,debug:a},c,l){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new mn({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),c),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:s,flowType:i,lock:o,debug:a,fetch:l,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new kr(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===r?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=r}}!function(){if("undefined"!=typeof window)return!1;if("undefined"==typeof process)return!1;const e=process.version;if(null==e)return!1;const t=e.match(/^v(\d+)\./);!!t&&parseInt(t[1],10)}();const wn=new _n("https://ydglduxhgwajqdseqzpy.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HEFdJm5qnXU1PQFbF-HkZ-bLez9LuPi3LepirU0nz4c",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});const kn=s.createContext(),Sn=()=>{const e=s.useContext(kn);if(!e)throw new Error("useAuth debe ser usado dentro de un AuthProvider");return e};export{I as $,he as A,q as B,M as C,F as D,me as E,je as F,X as G,H,fe as I,C as J,Ue as K,oe as L,te as M,ze as N,p as O,Q as P,G as Q,ke as R,Se as S,Pe as T,Oe as U,$ as V,A as W,y as X,P as Y,L as Z,Me as _,pe as a,Ie as a0,Be as a1,ee as a2,ne as a3,Re as a4,x as a5,T as a6,N as a7,W as a8,we as a9,ve as aa,Z as ab,j as ac,_ as ad,U as ae,Y as af,ge as ag,k as ah,$e as ai,se as aj,S as ak,Te as al,xe as am,O as an,Ee as ao,ie as ap,g as aq,le as ar,De as as,ce as at,f as au,w as b,V as c,Ae as d,K as e,ae as f,B as g,Le as h,He as i,h as j,D as k,Ce as l,ue as m,re as n,m as o,be as p,ye as q,_e as r,wn as s,de as t,Sn as u,J as v,E as w,b as x,R as y,z};
