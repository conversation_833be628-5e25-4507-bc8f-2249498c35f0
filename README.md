# Sistema de Gestión Psicológica - BAT-7

Sistema web para la gestión de evaluaciones psicológicas utilizando la batería de aptitudes BAT-7.

## 🚀 Despliegue en GitHub Pages

### Configuración Automática (Recomendado)

El proyecto está configurado para despliegue automático en GitHub Pages usando GitHub Actions:

1. **Push al repositorio**: Cada vez que hagas push a la rama `main` o `master`, se ejecutará automáticamente el workflow de despliegue.

2. **Verificar el despliegue**: Ve a la pestaña "Actions" en tu repositorio de GitHub para ver el progreso del despliegue.

3. **Acceder a la aplicación**: Una vez completado, tu aplicación estará disponible en:
   ```
   https://activa-tumente.github.io/Bat-7/
   ```

### Despliegue Manual

Si prefieres desplegar manualmente:

```bash
# Instalar dependencias
npm install

# Construir para producción
npm run build

# Desplegar a GitHub Pages
npm run deploy
```

### Configuración de GitHub Pages

1. Ve a tu repositorio en GitHub
2. Navega a **Settings** > **Pages**
3. En **Source**, selecciona **Deploy from a branch**
4. Selecciona la rama **gh-pages** y la carpeta **/ (root)**
5. Haz clic en **Save**

## 🛠️ Desarrollo Local

```bash
# Instalar dependencias
npm install

# Ejecutar en modo desarrollo
npm run dev

# Construir para producción
npm run build

# Previsualizar build de producción
npm run preview
```

## 📁 Estructura del Proyecto

```
src/
├── components/     # Componentes React reutilizables
├── pages/         # Páginas de la aplicación
├── context/       # Contextos de React (Auth, etc.)
├── hooks/         # Custom hooks
├── services/      # Servicios y APIs
├── utils/         # Utilidades y helpers
└── styles/        # Estilos CSS y configuración
```

## 🔧 Tecnologías

- **React 18** - Framework de UI
- **Vite** - Build tool y dev server
- **React Router** - Enrutamiento
- **Tailwind CSS** - Framework de CSS
- **Supabase** - Backend y base de datos
- **GitHub Pages** - Hosting

## 📝 Scripts Disponibles

- `npm run dev` - Servidor de desarrollo
- `npm run build` - Construir para producción
- `npm run preview` - Previsualizar build
- `npm run deploy` - Desplegar a GitHub Pages
- `npm run test` - Ejecutar tests
- `npm run test:coverage` - Tests con cobertura

## 🌐 Variables de Entorno

Para el funcionamiento completo, configura las siguientes variables de entorno:

```env
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_anon_key
```

## 📄 Licencia

Este proyecto está bajo la licencia MIT.
