{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/extends.js", "../../@babel/runtime/helpers/esm/assertThisInitialized.js", "../../@babel/runtime/helpers/esm/setPrototypeOf.js", "../../@babel/runtime/helpers/esm/inheritsLoose.js", "../../memoize-one/dist/memoize-one.esm.js", "../../@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../react-window/src/timer.js", "../../react-window/src/domHelpers.js", "../../react-window/src/createGridComponent.js", "../../react-window/src/VariableSizeGrid.js", "../../react-window/src/createListComponent.js", "../../react-window/src/VariableSizeList.js", "../../react-window/src/FixedSizeGrid.js", "../../react-window/src/FixedSizeList.js", "../../react-window/src/shallowDiffers.js", "../../react-window/src/areEqual.js", "../../react-window/src/shouldComponentUpdate.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount, layout } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      // This is an edge case for lists; normally they only scroll in the dominant direction.\n      let scrollbarSize = 0;\n      if (this._outerRef) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (layout === 'vertical') {\n          scrollbarSize =\n            outerRef.scrollWidth > outerRef.clientWidth\n              ? getScrollbarSize()\n              : 0;\n        } else {\n          scrollbarSize =\n            outerRef.scrollHeight > outerRef.clientHeight\n              ? getScrollbarSize()\n              : 0;\n        }\n      }\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps,\n          scrollbarSize\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size + scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\ntype InstanceProps = any;\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) -\n        size +\n        ((itemSize: any): number) +\n        scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "mappings": ";;;;;;;;AAAA,SAAS,WAAW;AAClB,SAAO,WAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,GAAG;AACpE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,IAAI,UAAU,CAAC;AACnB,eAAS,KAAK,EAAG,EAAC,CAAC,GAAG,eAAe,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAChE;AACA,WAAO;AAAA,EACT,GAAG,SAAS,MAAM,MAAM,SAAS;AACnC;;;ACRA,SAAS,uBAAuB,GAAG;AACjC,MAAI,WAAW,EAAG,OAAM,IAAI,eAAe,2DAA2D;AACtG,SAAO;AACT;;;ACHA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,eAAe,GAAG,GAAG;AAC5B,IAAE,YAAY,OAAO,OAAO,EAAE,SAAS,GAAG,EAAE,UAAU,cAAc,GAAG,gBAAe,GAAG,CAAC;AAC5F;;;ACHA,IAAI,YAAY,OAAO,SACnB,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACJ,SAAS,QAAQ,OAAO,QAAQ;AAC5B,MAAI,UAAU,QAAQ;AAClB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,KAAK,KAAK,UAAU,MAAM,GAAG;AACvC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,eAAe,WAAW,YAAY;AAC3C,MAAI,UAAU,WAAW,WAAW,QAAQ;AACxC,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,QAAI,CAAC,QAAQ,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG;AACvC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,UAAUE,UAAS;AACnC,MAAIA,aAAY,QAAQ;AAAE,IAAAA,WAAU;AAAA,EAAgB;AACpD,MAAI;AACJ,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,aAAa;AACjB,WAAS,WAAW;AAChB,QAAI,UAAU,CAAC;AACf,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,cAAQ,EAAE,IAAI,UAAU,EAAE;AAAA,IAC9B;AACA,QAAI,cAAc,aAAa,QAAQA,SAAQ,SAAS,QAAQ,GAAG;AAC/D,aAAO;AAAA,IACX;AACA,iBAAa,SAAS,MAAM,MAAM,OAAO;AACzC,iBAAa;AACb,eAAW;AACX,eAAW;AACX,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,IAAO,0BAAQ;A;;;;;AChDf,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,QAAI,OAAO,EAAE,QAAQ,CAAC,EAAG;AACzB,MAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACZ;AACA,SAAO;AACT;;;ACHA,IAAMC,0BACJ,OAAOC,gBAAgB,YAAY,OAAOA,YAAYC,QAAQ;AAEhE,IAAMA,MAAMF,0BACR,WAAA;AAAA,SAAMC,YAAYC,IAAZ;AAAN,IACA,WAAA;AAAA,SAAMC,KAAKD,IAAL;AAAN;AAMG,SAASE,cAAcC,WAAsB;AAClDC,uBAAqBD,UAAUE,EAAX;AACrB;AAEM,SAASC,eAAeC,UAAoBC,OAA0B;AAC3E,MAAMC,QAAQT,IAAG;AAEjB,WAASU,OAAO;AACd,QAAIV,IAAG,IAAKS,SAASD,OAAO;AAC1BD,eAASI,KAAK,IAAd;IACD,OAAM;AACLR,gBAAUE,KAAKO,sBAAsBF,IAAD;IACrC;EACF;AAED,MAAMP,YAAuB;IAC3BE,IAAIO,sBAAsBF,IAAD;EADE;AAI7B,SAAOP;AACR;AClCD,IAAIU,OAAe;AAGZ,SAASC,iBAAiBC,aAAuC;AAAA,MAAvCA,gBAAuC,QAAA;AAAvCA,kBAAwB;EAAe;AACtE,MAAIF,SAAS,MAAME,aAAa;AAC9B,QAAMC,MAAMC,SAASC,cAAc,KAAvB;AACZ,QAAMC,QAAQH,IAAIG;AAClBA,UAAMC,QAAQ;AACdD,UAAME,SAAS;AACfF,UAAMG,WAAW;AAEfL,aAASM,KAA6BC,YAAYR,GAApD;AAEAH,WAAOG,IAAIS,cAAcT,IAAIU;AAE3BT,aAASM,KAA6BI,YAAYX,GAApD;EACD;AAED,SAAOH;AACR;AAOD,IAAIe,kBAAwC;AAQrC,SAASC,iBAAiBd,aAA8C;AAAA,MAA9CA,gBAA8C,QAAA;AAA9CA,kBAAwB;EAAsB;AAC7E,MAAIa,oBAAoB,QAAQb,aAAa;AAC3C,QAAMe,WAAWb,SAASC,cAAc,KAAvB;AACjB,QAAMa,aAAaD,SAASX;AAC5BY,eAAWX,QAAQ;AACnBW,eAAWV,SAAS;AACpBU,eAAWT,WAAW;AACtBS,eAAWC,YAAY;AAEvB,QAAMC,WAAWhB,SAASC,cAAc,KAAvB;AACjB,QAAMgB,aAAaD,SAASd;AAC5Be,eAAWd,QAAQ;AACnBc,eAAWb,SAAS;AAEpBS,aAASN,YAAYS,QAArB;AAEEhB,aAASM,KAA6BC,YAAYM,QAApD;AAEA,QAAIA,SAASK,aAAa,GAAG;AAC3BP,wBAAkB;IACnB,OAAM;AACLE,eAASK,aAAa;AACtB,UAAIL,SAASK,eAAe,GAAG;AAC7BP,0BAAkB;MACnB,OAAM;AACLA,0BAAkB;MACnB;IACF;AAECX,aAASM,KAA6BI,YAAYG,QAApD;AAEA,WAAOF;EACR;AAED,SAAOA;AACR;ACuED,IAAMQ,iCAAiC;AAEvC,IAAMC,iBAAiB,SAAjBA,gBAAiB,MAAA;AAAA,MAAGC,cAAH,KAAGA,aAAaC,OAAhB,KAAgBA,MAAMC,WAAtB,KAAsBA;AAAtB,SAClBA,WADkB,MACNF;AADM;AAKvB,IAAIG,2BAA2B;AAC/B,IAAIC,sCAAsC;AAC1C,IAAIC,qBAAqB;AACzB,IAAIC,MAAuC;AACzC,MAAI,OAAOC,WAAW,eAAe,OAAOA,OAAOC,YAAY,aAAa;AAC1EL,+BAA2B,oBAAIK,QAAJ;AAC3BJ,0CAAsC,oBAAII,QAAJ;AACtCH,yBAAqB,oBAAIG,QAAJ;EACtB;AACF;AAEc,SAASC,oBAAT,OAgCX;AAAA,MAAA;AAAA,MA/BFC,mBA+BE,MA/BFA,iBACAC,gCA8BE,MA9BFA,8BACAC,mCA6BE,MA7BFA,iCACAC,kBA4BE,MA5BFA,gBACAC,2BA2BE,MA3BFA,yBACAC,0BA0BE,MA1BFA,wBACAC,kCAyBE,MAzBFA,gCACAC,+BAwBE,MAxBFA,6BACAC,gBAuBE,MAvBFA,cACAC,gBAsBE,MAtBFA,cACAC,6BAqBE,MArBFA,2BACAC,gCAoBE,MApBFA,8BACAC,qBAmBE,MAnBFA,mBACAC,wCAkBE,MAlBFA,uCACAC,iBAiBE,MAjBFA;AAkBA,SAAA,UAAA,SAAA,gBAAA;AAAA,mBAAA,MAAA,cAAA;AA8BE,aAAA,KAAYC,OAAiB;AAAA,UAAA;AAC3B,cAAA,eAAA,KAAA,MAAMA,KAAN,KAAA;AAD2B,YA7B7BC,iBAAsBJ,mBAAkB,MAAKG,OAAN,uBAAA,KAAA,CAAA;AA6BV,YA5B7BE,6BAA+C;AA4BlB,YA3B7BC,YA2B6B;AAAA,YAnB7BC,QAAe;QACbC,UAAQ,uBAAA,KAAA;QACRC,aAAa;QACbC,2BAA2B;QAC3BnC,YACE,OAAO,MAAK4B,MAAMQ,sBAAsB,WACpC,MAAKR,MAAMQ,oBACX;QACNC,WACE,OAAO,MAAKT,MAAMU,qBAAqB,WACnC,MAAKV,MAAMU,mBACX;QACNC,0BAA0B;QAC1BC,yBAAyB;MAbZ;AAmBc,YAwQ7BC,uBAxQ6B;AAAA,YAkR7BA,uBAAuBC,wBACrB,SACEC,0BACAC,yBACAC,uBACAC,sBACAC,yBACAC,wBACAC,sBACAC,qBARF;AAAA,eAUI,MAAKtB,MAAMuB,gBAAgD;UAC3DR;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QAR2D,CAA7D;MAVF,CAD+B;AAlRJ,YAyS7BE,gBAzS6B;AAAA,YAgT7BA,gBAAgBV,wBACd,SACE1C,YACAqC,WACAF,2BACAK,yBACAD,0BALF;AAAA,eAOI,MAAKX,MAAMyB,SAAkC;UAC7ClB;UACAnC;UACAqC;UACAG;UACAD;QAL6C,CAA/C;MAPF,CADwB;AAhTG,YAqX7Be,gBArX6B;AAAA,YAsX7BA,gBAAgB,SAACjD,UAAkBF,aAAgC;AACjE,YAAA,cAA8C,MAAKyB,OAA3C2B,cAAR,YAAQA,aAAa1D,YAArB,YAAqBA,WAAW2D,YAAhC,YAAgCA;AAEhC,YAAMC,iBAAiB,MAAKC,mBAC1BhC,yCAAyC6B,aACzC7B,yCAAyC7B,WACzC6B,yCAAyC8B,SAHpB;AAMvB,YAAMG,MAAStD,WAAN,MAAkBF;AAE3B,YAAInB;AACJ,YAAIyE,eAAeG,eAAeD,GAA9B,GAAoC;AACtC3E,kBAAQyE,eAAeE,GAAD;QACvB,OAAM;AACL,cAAME,UAAShD,iBACb,MAAKe,OACLzB,aACA,MAAK0B,cAHuB;AAK9B,cAAMiC,QAAQjE,cAAc;AAC5B4D,yBAAeE,GAAD,IAAQ3E,QAAQ;YAC5B+E,UAAU;YACVC,MAAMF,QAAQG,SAAYJ;YAC1BK,OAAOJ,QAAQD,UAASI;YACxBE,KAAK7C,cAAa,MAAKM,OAAOvB,UAAU,MAAKwB,cAA5B;YACjB3C,QAAQmC,cAAa,MAAKO,OAAOvB,UAAU,MAAKwB,cAA5B;YACpB5C,OAAO+B,gBAAe,MAAKY,OAAOzB,aAAa,MAAK0B,cAA/B;UANO;QAQ/B;AAED,eAAO7C;MACR;AAtZ4B,YAwZ7B0E,qBAxZ6B;AAAA,YAyZ7BA,qBAAqBhB,wBAAW,SAAC0B,GAAQC,IAASC,KAAlB;AAAA,eAAgC,CAAA;MAAhC,CAAD;AAzZF,YA2f7BC,YAAY,SAACC,OAA6B;AACxC,YAAA,uBAOIA,MAAMC,eANRC,eADF,qBACEA,cACAnF,cAFF,qBAEEA,aACAS,aAHF,qBAGEA,YACAqC,YAJF,qBAIEA,WACAsC,eALF,qBAKEA,cACAC,cANF,qBAMEA;AAEF,cAAKC,SAAS,SAAAC,WAAa;AACzB,cACEA,UAAU9E,eAAeA,cACzB8E,UAAUzC,cAAcA,WACxB;AAIA,mBAAO;UACR;AAED,cAAQxC,YAAc,MAAK+B,MAAnB/B;AAMR,cAAIkF,uBAAuB/E;AAC3B,cAAIH,cAAc,OAAO;AACvB,oBAAQH,iBAAgB,GAAxB;cACE,KAAK;AACHqF,uCAAuB,CAAC/E;AACxB;cACF,KAAK;AACH+E,uCAAuBH,cAAcrF,cAAcS;AACnD;YANJ;UAQD;AAGD+E,iCAAuBC,KAAKC,IAC1B,GACAD,KAAKE,IAAIH,sBAAsBH,cAAcrF,WAA7C,CAFqB;AAIvB,cAAM4F,sBAAsBH,KAAKC,IAC/B,GACAD,KAAKE,IAAI7C,WAAWsC,eAAeD,YAAnC,CAF0B;AAK5B,iBAAO;YACLxC,aAAa;YACbC,2BACE2C,UAAU9E,aAAaA,aAAa,YAAY;YAClDA,YAAY+E;YACZ1C,WAAW8C;YACX3C,yBACEsC,UAAUzC,YAAYA,YAAY,YAAY;YAChDE,0BAA0B;UARrB;QAUR,GAAE,MAAK6C,0BAjDR;MAkDD;AAtjB4B,YAwjB7BC,kBAAkB,SAACC,KAAmB;AACpC,YAAQC,WAAa,MAAK3D,MAAlB2D;AAER,cAAKxD,YAAcuD;AAEnB,YAAI,OAAOC,aAAa,YAAY;AAClCA,mBAASD,GAAD;QACT,WACCC,YAAY,QACZ,OAAOA,aAAa,YACpBA,SAAS3B,eAAe,SAAxB,GACA;AACA2B,mBAASC,UAAUF;QACpB;MACF;AAtkB4B,YAwkB7BF,6BAA6B,WAAM;AACjC,YAAI,MAAKtD,+BAA+B,MAAM;AAC5C/D,wBAAc,MAAK+D,0BAAN;QACd;AAED,cAAKA,6BAA6B3D,eAChC,MAAKsH,mBACLxF,8BAF8C;MAIjD;AAjlB4B,YAmlB7BwF,oBAAoB,WAAM;AACxB,cAAK3D,6BAA6B;AAElC,cAAK+C,SAAS;UAAE3C,aAAa;QAAf,GAAwB,WAAM;AAG1C,gBAAKwB,mBAAmB,EAAxB;QACD,CAJD;MAKD;AA3lB4B,aAAA;IAE5B;AAhCH,SAkCSgC,2BAAP,SAAA,yBACEC,WACAb,WACsB;AACtBc,0BAAoBD,WAAWb,SAAZ;AACnBnD,MAAAA,eAAcgE,SAAD;AACb,aAAO;IACR;AAzCH,QAAA,SAAA,KAAA;AAAA,WA2CEE,WAAA,SAAA,SAAA,OAMS;AAAA,UALP7F,aAKO,MALPA,YACAqC,YAIO,MAJPA;AAKA,UAAIrC,eAAeiE,QAAW;AAC5BjE,qBAAagF,KAAKC,IAAI,GAAGjF,UAAZ;MACd;AACD,UAAIqC,cAAc4B,QAAW;AAC3B5B,oBAAY2C,KAAKC,IAAI,GAAG5C,SAAZ;MACb;AAED,WAAKwC,SAAS,SAAAC,WAAa;AACzB,YAAI9E,eAAeiE,QAAW;AAC5BjE,uBAAa8E,UAAU9E;QACxB;AACD,YAAIqC,cAAc4B,QAAW;AAC3B5B,sBAAYyC,UAAUzC;QACvB;AAED,YACEyC,UAAU9E,eAAeA,cACzB8E,UAAUzC,cAAcA,WACxB;AACA,iBAAO;QACR;AAED,eAAO;UACLF,2BACE2C,UAAU9E,aAAaA,aAAa,YAAY;UAClDA;UACAqC;UACAE,0BAA0B;UAC1BC,yBACEsC,UAAUzC,YAAYA,YAAY,YAAY;QAP3C;MASR,GAAE,KAAK+C,0BAxBR;IAyBD;AAlFH,WAoFEU,eAAA,SAAA,aAAA,OAQS;AAAA,UAAA,cAAA,MAPPC,OAAAA,QAOO,gBAAA,SAPC,SAOD,aANP5F,cAMO,MANPA,aACAE,WAKO,MALPA;AAMA,UAAA,eAAiD,KAAKuB,OAA9CoE,cAAR,aAAQA,aAAa9G,SAArB,aAAqBA,QAAQ+G,WAA7B,aAA6BA,UAAUhH,QAAvC,aAAuCA;AACvC,UAAA,cAAkC,KAAK+C,OAA/BhC,aAAR,YAAQA,YAAYqC,YAApB,YAAoBA;AACpB,UAAM6D,gBAAgBvH,iBAAgB;AAEtC,UAAIwB,gBAAgB8D,QAAW;AAC7B9D,sBAAc6E,KAAKC,IAAI,GAAGD,KAAKE,IAAI/E,aAAa6F,cAAc,CAApC,CAAZ;MACf;AACD,UAAI3F,aAAa4D,QAAW;AAC1B5D,mBAAW2E,KAAKC,IAAI,GAAGD,KAAKE,IAAI7E,UAAU4F,WAAW,CAA9B,CAAZ;MACZ;AAED,UAAME,uBAAuBlF,yBAC3B,KAAKW,OACL,KAAKC,cAF6C;AAIpD,UAAMuE,sBAAsBlF,wBAC1B,KAAKU,OACL,KAAKC,cAF2C;AAQlD,UAAMwE,0BACJD,sBAAsBnH,QAAQiH,gBAAgB;AAChD,UAAMI,wBACJH,uBAAuBjH,SAASgH,gBAAgB;AAElD,WAAKL,SAAS;QACZ7F,YACEG,gBAAgB8D,SACZ9C,gCACE,KAAKS,OACLzB,aACA4F,OACA/F,YACA,KAAK6B,gBACLyE,qBAN4B,IAQ9BtG;QACNqC,WACEhC,aAAa4D,SACT7C,6BACE,KAAKQ,OACLvB,UACA0F,OACA1D,WACA,KAAKR,gBACLwE,uBANyB,IAQ3BhE;MAtBM,CAAd;IAwBD;AAjJH,WAmJEkE,oBAAA,SAAA,oBAAoB;AAClB,UAAA,eAAgD,KAAK3E,OAA7CQ,oBAAR,aAAQA,mBAAmBE,mBAA3B,aAA2BA;AAE3B,UAAI,KAAKP,aAAa,MAAM;AAC1B,YAAMwD,WAAa,KAAKxD;AACxB,YAAI,OAAOK,sBAAsB,UAAU;AACzCmD,mBAASvF,aAAaoC;QACvB;AACD,YAAI,OAAOE,qBAAqB,UAAU;AACxCiD,mBAASlD,YAAYC;QACtB;MACF;AAED,WAAKkE,oBAAL;IACD;AAjKH,WAmKEC,qBAAA,SAAA,qBAAqB;AACnB,UAAQ5G,YAAc,KAAK+B,MAAnB/B;AACR,UAAA,eAA4D,KAAKmC,OAAzDhC,aAAR,aAAQA,YAAYqC,YAApB,aAAoBA,WAAWE,2BAA/B,aAA+BA;AAE/B,UAAIA,4BAA4B,KAAKR,aAAa,MAAM;AAItD,YAAMwD,WAAa,KAAKxD;AACxB,YAAIlC,cAAc,OAAO;AACvB,kBAAQH,iBAAgB,GAAxB;YACE,KAAK;AACH6F,uBAASvF,aAAa,CAACA;AACvB;YACF,KAAK;AACHuF,uBAASvF,aAAaA;AACtB;YACF;AACE,kBAAQT,cAA6BgG,SAA7BhG,aAAaqF,cAAgBW,SAAhBX;AACrBW,uBAASvF,aAAa4E,cAAcrF,cAAcS;AAClD;UAVJ;QAYD,OAAM;AACLuF,mBAASvF,aAAagF,KAAKC,IAAI,GAAGjF,UAAZ;QACvB;AAEDuF,iBAASlD,YAAY2C,KAAKC,IAAI,GAAG5C,SAAZ;MACtB;AAED,WAAKmE,oBAAL;IACD;AAjMH,WAmMEE,uBAAA,SAAA,uBAAuB;AACrB,UAAI,KAAK5E,+BAA+B,MAAM;AAC5C/D,sBAAc,KAAK+D,0BAAN;MACd;IACF;AAvMH,WAyME6E,SAAA,SAAA,SAAS;AACP,UAAA,eAiBI,KAAK/E,OAhBPgF,WADF,aACEA,UACAC,YAFF,aAEEA,WACAb,cAHF,aAGEA,aACAnG,YAJF,aAIEA,WACAX,SALF,aAKEA,QACA4H,WANF,aAMEA,UACAC,mBAPF,aAOEA,kBACAC,eARF,aAQEA,cACAC,WATF,aASEA,UATF,uBAAA,aAUEC,SAAAA,UAVF,yBAAA,SAUYhH,iBAVZ,sBAWEiH,mBAXF,aAWEA,kBACAC,eAZF,aAYEA,cACAnB,WAbF,aAaEA,UACAjH,QAdF,aAcEA,OACAqI,iBAfF,aAeEA,gBACApI,QAhBF,aAgBEA;AAEF,UAAQiD,cAAgB,KAAKF,MAArBE;AAER,UAAA,wBAGI,KAAKoF,4BAAL,GAFFC,mBADF,sBAAA,CAAA,GAEEC,kBAFF,sBAAA,CAAA;AAIA,UAAA,wBAAsC,KAAKC,0BAAL,GAA/BC,gBAAP,sBAAA,CAAA,GAAsBC,eAAtB,sBAAA,CAAA;AAEA,UAAMC,QAAQ,CAAA;AACd,UAAI5B,cAAc,KAAKC,UAAU;AAC/B,iBACM5F,YAAWqH,eACfrH,aAAYsH,cACZtH,aACA;AACA,mBACMF,eAAcoH,kBAClBpH,gBAAeqH,iBACfrH,gBACA;AACAyH,kBAAMC,SACJ9I,4BAAc6H,UAAU;cACtBzG,aAAAA;cACAC,MAAM6G;cACN/E,aAAamF,iBAAiBnF,cAAc+B;cAC5CN,KAAKuD,QAAQ;gBAAE/G,aAAAA;gBAAaC,MAAM6G;gBAAU5G,UAAAA;cAA/B,CAAD;cACZA,UAAAA;cACArB,OAAO,KAAKsE,cAAcjD,WAAUF,YAA7B;YANe,CAAX,CADf;UAUD;QACF;MACF;AAID,UAAMgG,uBAAuBlF,yBAC3B,KAAKW,OACL,KAAKC,cAF6C;AAIpD,UAAMuE,sBAAsBlF,wBAC1B,KAAKU,OACL,KAAKC,cAF2C;AAKlD,iBAAO9C,4BACLoI,oBAAoBC,gBAAgB,OACpC;QACEP;QACAxD,UAAU,KAAKkB;QACfe,KAAK,KAAKD;QACVrG,OAAK,SAAA;UACH+E,UAAU;UACV7E;UACAD;UACAE,UAAU;UACV2I,yBAAyB;UACzBC,YAAY;UACZlI;QAPG,GAQAb,KARA;MAJP,OAeAD,4BAAcgI,oBAAoBC,gBAAgB,OAAO;QACvDJ,UAAUgB;QACVtC,KAAKwB;QACL9H,OAAO;UACLE,QAAQiH;UACR6B,eAAe9F,cAAc,SAAS+B;UACtChF,OAAOmH;QAHF;MAHgD,CAA5C,CAjBK;IA2BrB;AApSH,WA+VEI,sBAAA,SAAA,sBAAsB;AACpB,UAAA,eAA6D,KAAK5E,OAA1DoE,cAAR,aAAQA,aAAa7C,kBAArB,aAAqBA,iBAAiBE,WAAtC,aAAsCA,UAAU4C,WAAhD,aAAgDA;AAEhD,UAAI,OAAO9C,oBAAoB,YAAY;AACzC,YAAI6C,cAAc,KAAKC,WAAW,GAAG;AACnC,cAAA,yBAKI,KAAKqB,4BAAL,GAJF3E,4BADF,uBAAA,CAAA,GAEEC,2BAFF,uBAAA,CAAA,GAGEG,2BAHF,uBAAA,CAAA,GAIEC,0BAJF,uBAAA,CAAA;AAMA,cAAA,yBAKI,KAAKyE,0BAAL,GAJF5E,yBADF,uBAAA,CAAA,GAEEC,wBAFF,uBAAA,CAAA,GAGEG,wBAHF,uBAAA,CAAA,GAIEC,uBAJF,uBAAA,CAAA;AAMA,eAAKT,qBACHE,2BACAC,0BACAC,wBACAC,uBACAC,0BACAC,yBACAC,uBACAC,oBARF;QAUD;MACF;AAED,UAAI,OAAOG,aAAa,YAAY;AAClC,YAAA,eAMI,KAAKrB,OALPG,6BADF,aACEA,2BACAnC,cAFF,aAEEA,YACAqC,aAHF,aAGEA,WACAE,4BAJF,aAIEA,0BACAC,2BALF,aAKEA;AAEF,aAAKY,cACHpD,aACAqC,YACAF,4BACAK,0BACAD,yBALF;MAOD;IACF;AA7YH,WAybE+E,8BAAA,SAAA,8BAAgE;AAC9D,UAAA,eAMI,KAAK1F,OALPoE,cADF,aACEA,aACAiC,sBAFF,aAEEA,qBACAC,uBAHF,aAGEA,sBACAC,gBAJF,aAIEA,eACAlC,WALF,aAKEA;AAEF,UAAA,eAA+D,KAAKjE,OAA5DG,4BAAR,aAAQA,2BAA2BD,cAAnC,aAAmCA,aAAalC,aAAhD,aAAgDA;AAEhD,UAAMoI,wBACJH,uBAAuBC,wBAAwBC,iBAAiB;AAElE,UAAInC,gBAAgB,KAAKC,aAAa,GAAG;AACvC,eAAO,CAAC,GAAG,GAAG,GAAG,CAAV;MACR;AAED,UAAMoC,aAAavH,8BACjB,KAAKc,OACL5B,YACA,KAAK6B,cAHwC;AAK/C,UAAMyG,YAAYvH,iCAChB,KAAKa,OACLyG,YACArI,YACA,KAAK6B,cAJ0C;AASjD,UAAM0G,mBACJ,CAACrG,eAAeC,8BAA8B,aAC1C6C,KAAKC,IAAI,GAAGmD,qBAAZ,IACA;AACN,UAAMI,kBACJ,CAACtG,eAAeC,8BAA8B,YAC1C6C,KAAKC,IAAI,GAAGmD,qBAAZ,IACA;AAEN,aAAO,CACLpD,KAAKC,IAAI,GAAGoD,aAAaE,gBAAzB,GACAvD,KAAKC,IAAI,GAAGD,KAAKE,IAAIc,cAAc,GAAGsC,YAAYE,eAAtC,CAAZ,GACAH,YACAC,SAJK;IAMR;AAveH,WAyeEb,4BAAA,SAAA,4BAA8D;AAC5D,UAAA,eAMI,KAAK7F,OALPoE,cADF,aACEA,aACAmC,gBAFF,aAEEA,eACAM,mBAHF,aAGEA,kBACAC,oBAJF,aAIEA,mBACAzC,WALF,aAKEA;AAEF,UAAA,eAA4D,KAAKjE,OAAzDE,cAAR,aAAQA,aAAaM,0BAArB,aAAqBA,yBAAyBH,YAA9C,aAA8CA;AAE9C,UAAM+F,wBACJK,oBAAoBC,qBAAqBP,iBAAiB;AAE5D,UAAInC,gBAAgB,KAAKC,aAAa,GAAG;AACvC,eAAO,CAAC,GAAG,GAAG,GAAG,CAAV;MACR;AAED,UAAMoC,aAAa9G,2BACjB,KAAKK,OACLS,WACA,KAAKR,cAHqC;AAK5C,UAAMyG,YAAY9G,8BAChB,KAAKI,OACLyG,YACAhG,WACA,KAAKR,cAJuC;AAS9C,UAAM0G,mBACJ,CAACrG,eAAeM,4BAA4B,aACxCwC,KAAKC,IAAI,GAAGmD,qBAAZ,IACA;AACN,UAAMI,kBACJ,CAACtG,eAAeM,4BAA4B,YACxCwC,KAAKC,IAAI,GAAGmD,qBAAZ,IACA;AAEN,aAAO,CACLpD,KAAKC,IAAI,GAAGoD,aAAaE,gBAAzB,GACAvD,KAAKC,IAAI,GAAGD,KAAKE,IAAIe,WAAW,GAAGqC,YAAYE,eAAnC,CAAZ,GACAH,YACAC,SAJK;IAMR;AAvhBH,WAAA;EAAA,GAA6BK,0BAA7B,GAAA,OAKSC,eAAe;IACpB/I,WAAW;IACXoH,UAAUhD;IACVoD,gBAAgB;EAHI,GALxB;AA2nBD;AAED,IAAMzB,sBAAsB,SAAtBA,qBAAsB,OAAA,OAajB;AAAA,MAXPgB,WAWO,MAXPA,UACA/G,YAUO,MAVPA,WACAX,SASO,MATPA,QACA8H,eAQO,MARPA,cACAI,eAOO,MAPPA,cACAc,uBAMO,MANPA,sBACAC,gBAKO,MALPA,eACAO,oBAIO,MAJPA,mBACAzJ,QAGO,MAHPA;AAGO,MADPgD,WACO,MADPA;AAEF,MAAIxB,MAAuC;AACzC,QAAI,OAAO0H,kBAAkB,UAAU;AACrC,UAAI7H,4BAA4B,CAACA,yBAAyBuI,IAAI5G,QAA7B,GAAwC;AACvE3B,iCAAyBwI,IAAI7G,QAA7B;AACA8G,gBAAQC,KACN,oHADF;MAID;IACF;AAED,QACE,OAAOd,yBAAyB,YAChC,OAAOQ,sBAAsB,UAC7B;AACA,UACEnI,uCACA,CAACA,oCAAoCsI,IAAI5G,QAAxC,GACD;AACA1B,4CAAoCuI,IAAI7G,QAAxC;AACA8G,gBAAQC,KACN,mJADF;MAID;IACF;AAED,QAAIhC,gBAAgB,QAAQI,gBAAgB,MAAM;AAChD,UAAI5G,sBAAsB,CAACA,mBAAmBqI,IAAI5G,QAAvB,GAAkC;AAC3DzB,2BAAmBsI,IAAI7G,QAAvB;AACA8G,gBAAQC,KACN,mIADF;MAID;IACF;AAED,QAAIpC,YAAY,MAAM;AACpB,YAAMqC,MACJ,wFAAA,OAEMrC,aAAa,OAAO,SAAS,OAAOA,YAF1C,mBADS;IAKZ;AAED,YAAQ/G,WAAR;MACE,KAAK;MACL,KAAK;AAEH;MACF;AACE,cAAMoJ,MACJ,6FAAA,MAEMpJ,YAFN,mBADS;IANf;AAaA,QAAI,OAAOZ,UAAU,UAAU;AAC7B,YAAMgK,MACJ,yFAAA,OAEMhK,UAAU,OAAO,SAAS,OAAOA,SAFvC,mBADS;IAKZ;AAED,QAAI,OAAOC,WAAW,UAAU;AAC9B,YAAM+J,MACJ,2FAAA,OAEM/J,WAAW,OAAO,SAAS,OAAOA,UAFxC,mBADS;IAKZ;EACF;AACF;ACh5BD,IAAMgK,8BAA8B;AAyBpC,IAAMjI,0BAA0B,SAA1BA,yBAA0B,MAAA,OAG3B;AAAA,MAFDgF,WAEC,KAFDA;AAEC,MADDkD,iBACC,MADDA,gBAAgBC,qBACf,MADeA,oBAAoBC,uBACnC,MADmCA;AAEtC,MAAIC,0BAA0B;AAI9B,MAAID,wBAAwBpD,UAAU;AACpCoD,2BAAuBpD,WAAW;EACnC;AAED,MAAIoD,wBAAwB,GAAG;AAC7B,QAAME,eAAeJ,eAAeE,oBAAD;AACnCC,8BAA0BC,aAAa1F,SAAS0F,aAAa7K;EAC9D;AAED,MAAM8K,qBAAqBvD,WAAWoD,uBAAuB;AAC7D,MAAMI,6BAA6BD,qBAAqBJ;AAExD,SAAOE,0BAA0BG;AAClC;AAED,IAAMvI,yBAAyB,SAAzBA,wBAAyB,OAAA,OAO1B;AAAA,MAND8E,cAMC,MANDA;AAMC,MAJD0D,oBAIC,MAJDA,mBACAC,uBAGC,MAHDA,sBACAC,0BAEC,MAFDA;AAGF,MAAIN,0BAA0B;AAI9B,MAAIM,2BAA2B5D,aAAa;AAC1C4D,8BAA0B5D,cAAc;EACzC;AAED,MAAI4D,2BAA2B,GAAG;AAChC,QAAML,eAAeG,kBAAkBE,uBAAD;AACtCN,8BAA0BC,aAAa1F,SAAS0F,aAAa7K;EAC9D;AAED,MAAM8K,qBAAqBxD,cAAc4D,0BAA0B;AACnE,MAAMH,6BAA6BD,qBAAqBG;AAExD,SAAOL,0BAA0BG;AAClC;AAED,IAAMI,kBAAkB,SAAlBA,iBACJC,UACAlI,OACAmI,OACAC,eACiB;AACjB,MAAIC,iBAAiBC,UAAUC;AAC/B,MAAIL,aAAa,UAAU;AACzBG,sBAAkBD,cAAcN;AAChCQ,eAAatI,MAAM2B;AACnB4G,wBAAoBH,cAAcJ;EACnC,OAAM;AACLK,sBAAkBD,cAAcb;AAChCe,eAAatI,MAAM4B;AACnB2G,wBAAoBH,cAAcX;EACnC;AAED,MAAIU,QAAQI,mBAAmB;AAC7B,QAAItG,SAAS;AACb,QAAIsG,qBAAqB,GAAG;AAC1B,UAAMZ,eAAeU,gBAAgBE,iBAAD;AACpCtG,eAAS0F,aAAa1F,SAAS0F,aAAa7K;IAC7C;AAED,aAAS0L,IAAID,oBAAoB,GAAGC,KAAKL,OAAOK,KAAK;AACnD,UAAI1L,QAAOwL,SAASE,CAAD;AAEnBH,sBAAgBG,CAAD,IAAM;QACnBvG;QACAnF,MAAAA;MAFmB;AAKrBmF,gBAAUnF;IACX;AAED,QAAIoL,aAAa,UAAU;AACzBE,oBAAcJ,0BAA0BG;IACzC,OAAM;AACLC,oBAAcX,uBAAuBU;IACtC;EACF;AAED,SAAOE,gBAAgBF,KAAD;AACvB;AAED,IAAMM,kBAAkB,SAAlBA,iBACJP,UACAlI,OACAoI,eACAnG,QACG;AACH,MAAIoG,iBAAiBE;AACrB,MAAIL,aAAa,UAAU;AACzBG,sBAAkBD,cAAcN;AAChCS,wBAAoBH,cAAcJ;EACnC,OAAM;AACLK,sBAAkBD,cAAcb;AAChCgB,wBAAoBH,cAAcX;EACnC;AAED,MAAMiB,yBACJH,oBAAoB,IAAIF,gBAAgBE,iBAAD,EAAoBtG,SAAS;AAEtE,MAAIyG,0BAA0BzG,QAAQ;AAEpC,WAAO0G,4BACLT,UACAlI,OACAoI,eACAG,mBACA,GACAtG,MANgC;EAQnC,OAAM;AAIL,WAAO2G,iCACLV,UACAlI,OACAoI,eACAhF,KAAKC,IAAI,GAAGkF,iBAAZ,GACAtG,MALqC;EAOxC;AACF;AAED,IAAM0G,8BAA8B,SAA9BA,6BACJT,UACAlI,OACAoI,eACAS,MACAC,KACA7G,QACW;AACX,SAAO6G,OAAOD,MAAM;AAClB,QAAME,SAASD,MAAM1F,KAAK4F,OAAOH,OAAOC,OAAO,CAA1B;AACrB,QAAMG,gBAAgBhB,gBACpBC,UACAlI,OACA+I,QACAX,aAJmC,EAKnCnG;AAEF,QAAIgH,kBAAkBhH,QAAQ;AAC5B,aAAO8G;IACR,WAAUE,gBAAgBhH,QAAQ;AACjC6G,YAAMC,SAAS;IAChB,WAAUE,gBAAgBhH,QAAQ;AACjC4G,aAAOE,SAAS;IACjB;EACF;AAED,MAAID,MAAM,GAAG;AACX,WAAOA,MAAM;EACd,OAAM;AACL,WAAO;EACR;AACF;AAED,IAAMF,mCAAmC,SAAnCA,kCACJV,UACAlI,OACAoI,eACAD,OACAlG,QACW;AACX,MAAMiH,YAAYhB,aAAa,WAAWlI,MAAMoE,cAAcpE,MAAMqE;AACpE,MAAI8E,WAAW;AAEf,SACEhB,QAAQe,aACRjB,gBAAgBC,UAAUlI,OAAOmI,OAAOC,aAAzB,EAAwCnG,SAASA,QAChE;AACAkG,aAASgB;AACTA,gBAAY;EACb;AAED,SAAOR,4BACLT,UACAlI,OACAoI,eACAhF,KAAKE,IAAI6E,OAAOe,YAAY,CAA5B,GACA9F,KAAK4F,MAAMb,QAAQ,CAAnB,GACAlG,MANgC;AAQnC;AAED,IAAMmH,gCAAgC,SAAhCA,+BACJlB,UACAlI,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,eACW;AACX,MAAMxH,QAAOoL,aAAa,WAAWlI,MAAM3C,QAAQ2C,MAAM1C;AACzD,MAAMqK,eAAeM,gBAAgBC,UAAUlI,OAAOmI,OAAOC,aAAzB;AAIpC,MAAMkB,qBACJpB,aAAa,WACT5I,uBAAuBU,OAAOoI,aAAR,IACtB/I,wBAAwBW,OAAOoI,aAAR;AAE7B,MAAMmB,YAAYnG,KAAKC,IACrB,GACAD,KAAKE,IAAIgG,qBAAqBxM,OAAM6K,aAAa1F,MAAjD,CAFgB;AAIlB,MAAMuH,YAAYpG,KAAKC,IACrB,GACAsE,aAAa1F,SAASnF,QAAOwH,gBAAgBqD,aAAa7K,IAF1C;AAKlB,MAAIqH,UAAU,SAAS;AACrB,QAAIkF,gBAAgBG,YAAY1M,SAAQuM,gBAAgBE,YAAYzM,OAAM;AACxEqH,cAAQ;IACT,OAAM;AACLA,cAAQ;IACT;EACF;AAED,UAAQA,OAAR;IACE,KAAK;AACH,aAAOoF;IACT,KAAK;AACH,aAAOC;IACT,KAAK;AACH,aAAOpG,KAAKqG,MAAMD,aAAaD,YAAYC,aAAa,CAAjD;IACT,KAAK;IACL;AACE,UAAIH,gBAAgBG,aAAaH,gBAAgBE,WAAW;AAC1D,eAAOF;MACR,WAAUG,YAAYD,WAAW;AAGhC,eAAOC;MACR,WAAUH,eAAeG,WAAW;AACnC,eAAOA;MACR,OAAM;AACL,eAAOD;MACR;EAnBL;AAqBD;AAED,IAAMG,mBAAmB1K,oBAAoB;EAC3CC,iBAAiB,SAAA,gBACfe,OACAmI,OACAC,eAHe;AAAA,WAIJH,gBAAgB,UAAUjI,OAAOmI,OAAOC,aAAzB,EAAwCnG;EAJnD;EAMjB/C,8BAA8B,SAAA,6BAC5Bc,OACA5B,YACAgK,eAH4B;AAAA,WAIjBK,gBAAgB,UAAUzI,OAAOoI,eAAehK,UAAjC;EAJE;EAM9Be,iCAAiC,SAAA,gCAC/Ba,OACAyG,YACArI,YACAgK,eACW;AACX,QAAQhE,cAAuBpE,MAAvBoE,aAAa/G,QAAU2C,MAAV3C;AAErB,QAAMsK,eAAeM,gBACnB,UACAjI,OACAyG,YACA2B,aAJkC;AAMpC,QAAMmB,YAAYnL,aAAaf;AAE/B,QAAI4E,SAAS0F,aAAa1F,SAAS0F,aAAa7K;AAChD,QAAI4J,YAAYD;AAEhB,WAAOC,YAAYtC,cAAc,KAAKnC,SAASsH,WAAW;AACxD7C;AACAzE,gBAAUgG,gBAAgB,UAAUjI,OAAO0G,WAAW0B,aAA7B,EAA4CtL;IACtE;AAED,WAAO4J;EACR;EAEDtH,gBAAgB,SAAA,eACdY,OACAmI,OACAC,eAHc;AAAA,WAIHA,cAAcN,kBAAkBK,KAAhC,EAAuCrL;EAJpC;EAMhBuC;EACAC;EAEAC,gCAAgC,SAAA,+BAC9BS,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,eAN8B;AAAA,WAQ9B8E,8BACE,UACApJ,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,aAP2B;EARC;EAkBhC9E,6BAA6B,SAAA,4BAC3BQ,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,eAN2B;AAAA,WAQ3B8E,8BACE,OACApJ,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,aAP2B;EARF;EAkB7B5E,cAAc,SAAA,aACZM,OACAmI,OACAC,eAHY;AAAA,WAIDH,gBAAgB,OAAOjI,OAAOmI,OAAOC,aAAtB,EAAqCnG;EAJnD;EAMdxC,cAAc,SAAA,aACZO,OACAmI,OACAC,eAHY;AAAA,WAIDA,cAAcb,eAAeY,KAA7B,EAAoCrL;EAJnC;EAMd6C,2BAA2B,SAAA,0BACzBK,OACAS,WACA2H,eAHyB;AAAA,WAIdK,gBAAgB,OAAOzI,OAAOoI,eAAe3H,SAA9B;EAJD;EAM3Bb,8BAA8B,SAAA,6BAC5BI,OACAyG,YACAhG,WACA2H,eACW;AACX,QAAQ/D,WAAqBrE,MAArBqE,UAAU/G,SAAW0C,MAAX1C;AAElB,QAAMqK,eAAeM,gBACnB,OACAjI,OACAyG,YACA2B,aAJkC;AAMpC,QAAMmB,YAAY9I,YAAYnD;AAE9B,QAAI2E,SAAS0F,aAAa1F,SAAS0F,aAAa7K;AAChD,QAAI4J,YAAYD;AAEhB,WAAOC,YAAYrC,WAAW,KAAKpC,SAASsH,WAAW;AACrD7C;AACAzE,gBAAUgG,gBAAgB,OAAOjI,OAAO0G,WAAW0B,aAA1B,EAAyCtL;IACnE;AAED,WAAO4J;EACR;EAED7G,mBAlI2C,SAAA,kBAkIzBG,OAAmBK,UAA8B;AACjE,QAAA,QAGML,OAFJ+H,uBADF,MACEA,sBACAP,qBAFF,MAEEA;AAGF,QAAMY,gBAAgB;MACpBN,mBAAmB,CAAA;MACnBC,sBAAsBA,wBAAwBT;MAC9CE,oBAAoBA,sBAAsBF;MAC1CU,yBAAyB;MACzBP,sBAAsB;MACtBF,gBAAgB,CAAA;IANI;AAStBlH,aAASsJ,wBAAwB,SAC/BpL,aACAqL,mBACG;AAAA,UADHA,sBACG,QAAA;AADHA,4BAA8B;MAC3B;AACHvJ,eAASwJ,kBAAkB;QAAEtL;QAAaqL;MAAf,CAA3B;IACD;AAEDvJ,aAASyJ,qBAAqB,SAC5BrL,UACAmL,mBACG;AAAA,UADHA,sBACG,QAAA;AADHA,4BAA8B;MAC3B;AACHvJ,eAASwJ,kBAAkB;QAAEpL;QAAUmL;MAAZ,CAA3B;IACD;AAEDvJ,aAASwJ,oBAAoB,SAAA,OAQvB;AAAA,UAPJtL,cAOI,MAPJA,aACAE,WAMI,MANJA,UAMI,wBAAA,MALJmL,mBAAAA,oBAKI,0BAAA,SALgB,OAKhB;AACJ,UAAI,OAAOrL,gBAAgB,UAAU;AACnC6J,sBAAcJ,0BAA0B5E,KAAKE,IAC3C8E,cAAcJ,yBACdzJ,cAAc,CAFwB;MAIzC;AACD,UAAI,OAAOE,aAAa,UAAU;AAChC2J,sBAAcX,uBAAuBrE,KAAKE,IACxC8E,cAAcX,sBACdhJ,WAAW,CAFwB;MAItC;AAMD4B,eAASyB,mBAAmB,EAA5B;AAEA,UAAI8H,mBAAmB;AACrBvJ,iBAAS0J,YAAT;MACD;IACF;AAED,WAAO3B;EACR;EAEDtI,uCAAuC;EAEvCC,eAAe,SAAA,cAAA,OAAkD;AAAA,QAA/C4B,cAA+C,MAA/CA,aAAaC,YAAkC,MAAlCA;AAC7B,QAAI/C,MAAuC;AACzC,UAAI,OAAO8C,gBAAgB,YAAY;AACrC,cAAM0F,MACJ,oFAAA,OAGI1F,gBAAgB,OAAO,SAAS,OAAOA,eAH3C,mBADS;MAOZ,WAAU,OAAOC,cAAc,YAAY;AAC1C,cAAMyF,MACJ,kFAAA,OAEMzF,cAAc,OAAO,SAAS,OAAOA,aAF3C,mBADS;MAKZ;IACF;EACF;AAvN0C,CAAD;ACtK5C,IAAMvD,mCAAiC;AAEvC,IAAMC,mBAAiB,SAAjBA,gBAAkB6J,OAAe3J,MAAhB;AAAA,SAA8B2J;AAA9B;AAIvB,IAAI6B,uBAAuB;AAC3B,IAAIpL,uBAAqB;AACzB,IAAIC,MAAuC;AACzC,MAAI,OAAOC,WAAW,eAAe,OAAOA,OAAOC,YAAY,aAAa;AAC1EiL,2BAAuB,oBAAIjL,QAAJ;AACvBH,2BAAqB,oBAAIG,QAAJ;EACtB;AACF;AAEc,SAASkL,oBAAT,MAoBX;AAAA,MAAA;AAAA,MAnBFC,iBAmBE,KAnBFA,eACAC,yBAkBE,KAlBFA,uBACAC,eAiBE,KAjBFA,aACAhB,iCAgBE,KAhBFA,+BACAiB,0BAeE,KAfFA,wBACAC,6BAcE,KAdFA,2BACAzK,qBAaE,KAbFA,mBACAC,wCAYE,KAZFA,uCACAC,iBAWE,KAXFA;AAYA,SAAA,UAAA,SAAA,gBAAA;AAAA,mBAAA,MAAA,cAAA;AA2BE,aAAA,KAAYC,OAAiB;AAAA,UAAA;AAC3B,cAAA,eAAA,KAAA,MAAMA,KAAN,KAAA;AAD2B,YA1B7BC,iBAAsBJ,mBAAkB,MAAKG,OAAN,uBAAA,KAAA,CAAA;AA0BV,YAzB7BG,YAyB6B;AAAA,YAxB7BD,6BAA+C;AAwBlB,YAd7BE,QAAe;QACbC,UAAQ,uBAAA,KAAA;QACRC,aAAa;QACbiK,iBAAiB;QACjBlB,cACE,OAAO,MAAKrJ,MAAMwK,wBAAwB,WACtC,MAAKxK,MAAMwK,sBACX;QACN7J,0BAA0B;MARb;AAcc,YA8M7BE,uBA9M6B;AAAA,YAoN7BA,uBAAuBC,wBACrB,SACE2J,oBACAC,mBACAC,mBACAC,kBAJF;AAAA,eAMI,MAAK5K,MAAMuB,gBAAgD;UAC3DkJ;UACAC;UACAC;UACAC;QAJ2D,CAA7D;MANF,CAD+B;AApNJ,YAmO7BpJ,gBAnO6B;AAAA,YAwO7BA,gBAAgBV,wBACd,SACEyJ,iBACAlB,cACA1I,0BAHF;AAAA,eAKI,MAAKX,MAAMyB,SAAkC;UAC7C8I;UACAlB;UACA1I;QAH6C,CAA/C;MALF,CADwB;AAxOG,YA0R7Be,gBA1R6B;AAAA,YA2R7BA,gBAAgB,SAACyG,OAA0B;AACzC,YAAA,cAAwC,MAAKnI,OAArC/B,YAAR,YAAQA,WAAWqK,WAAnB,YAAmBA,UAAUuC,SAA7B,YAA6BA;AAE7B,YAAMhJ,iBAAiB,MAAKC,mBAC1BhC,yCAAyCwI,UACzCxI,yCAAyC+K,QACzC/K,yCAAyC7B,SAHpB;AAMvB,YAAIb;AACJ,YAAIyE,eAAeG,eAAemG,KAA9B,GAAsC;AACxC/K,kBAAQyE,eAAesG,KAAD;QACvB,OAAM;AACL,cAAMlG,UAASiI,eAAc,MAAKlK,OAAOmI,OAAO,MAAKlI,cAAzB;AAC5B,cAAMnD,QAAOsN,aAAY,MAAKpK,OAAOmI,OAAO,MAAKlI,cAAzB;AAGxB,cAAM6K,eACJ7M,cAAc,gBAAgB4M,WAAW;AAE3C,cAAM3I,QAAQjE,cAAc;AAC5B,cAAM8M,mBAAmBD,eAAe7I,UAAS;AACjDJ,yBAAesG,KAAD,IAAU/K,QAAQ;YAC9B+E,UAAU;YACVC,MAAMF,QAAQG,SAAY0I;YAC1BzI,OAAOJ,QAAQ6I,mBAAmB1I;YAClCE,KAAK,CAACuI,eAAe7I,UAAS;YAC9B3E,QAAQ,CAACwN,eAAehO,QAAO;YAC/BO,OAAOyN,eAAehO,QAAO;UANC;QAQjC;AAED,eAAOM;MACR;AA5T4B,YA8T7B0E,qBA9T6B;AAAA,YA+T7BA,qBAAqBhB,wBAAW,SAAC0B,GAAQC,IAASC,KAAlB;AAAA,eAAgC,CAAA;MAAhC,CAAD;AA/TF,YAwW7BsI,sBAAsB,SAACpI,OAA6B;AAClD,YAAA,uBAAiDA,MAAMC,eAA/ClF,cAAR,qBAAQA,aAAaS,aAArB,qBAAqBA,YAAY4E,cAAjC,qBAAiCA;AACjC,cAAKC,SAAS,SAAAC,WAAa;AACzB,cAAIA,UAAUmG,iBAAiBjL,YAAY;AAIzC,mBAAO;UACR;AAED,cAAQH,YAAc,MAAK+B,MAAnB/B;AAER,cAAIoL,eAAejL;AACnB,cAAIH,cAAc,OAAO;AAKvB,oBAAQH,iBAAgB,GAAxB;cACE,KAAK;AACHuL,+BAAe,CAACjL;AAChB;cACF,KAAK;AACHiL,+BAAerG,cAAcrF,cAAcS;AAC3C;YANJ;UAQD;AAGDiL,yBAAejG,KAAKC,IAClB,GACAD,KAAKE,IAAI+F,cAAcrG,cAAcrF,WAArC,CAFa;AAKf,iBAAO;YACL2C,aAAa;YACbiK,iBACErH,UAAUmG,eAAeA,eAAe,YAAY;YACtDA;YACA1I,0BAA0B;UALrB;QAOR,GAAE,MAAK6C,0BAvCR;MAwCD;AAlZ4B,YAoZ7ByH,oBAAoB,SAACrI,OAA6B;AAChD,YAAA,wBAAkDA,MAAMC,eAAhDC,eAAR,sBAAQA,cAAcC,eAAtB,sBAAsBA,cAActC,YAApC,sBAAoCA;AACpC,cAAKwC,SAAS,SAAAC,WAAa;AACzB,cAAIA,UAAUmG,iBAAiB5I,WAAW;AAIxC,mBAAO;UACR;AAGD,cAAM4I,eAAejG,KAAKC,IACxB,GACAD,KAAKE,IAAI7C,WAAWsC,eAAeD,YAAnC,CAFmB;AAKrB,iBAAO;YACLxC,aAAa;YACbiK,iBACErH,UAAUmG,eAAeA,eAAe,YAAY;YACtDA;YACA1I,0BAA0B;UALrB;QAOR,GAAE,MAAK6C,0BArBR;MAsBD;AA5a4B,YA8a7BC,kBAAkB,SAACC,KAAmB;AACpC,YAAQC,WAAa,MAAK3D,MAAlB2D;AAER,cAAKxD,YAAcuD;AAEnB,YAAI,OAAOC,aAAa,YAAY;AAClCA,mBAASD,GAAD;QACT,WACCC,YAAY,QACZ,OAAOA,aAAa,YACpBA,SAAS3B,eAAe,SAAxB,GACA;AACA2B,mBAASC,UAAUF;QACpB;MACF;AA5b4B,YA8b7BF,6BAA6B,WAAM;AACjC,YAAI,MAAKtD,+BAA+B,MAAM;AAC5C/D,wBAAc,MAAK+D,0BAAN;QACd;AAED,cAAKA,6BAA6B3D,eAChC,MAAKsH,mBACLxF,gCAF8C;MAIjD;AAvc4B,YAyc7BwF,oBAAoB,WAAM;AACxB,cAAK3D,6BAA6B;AAElC,cAAK+C,SAAS;UAAE3C,aAAa;QAAf,GAAwB,WAAM;AAG1C,gBAAKwB,mBAAmB,IAAI,IAA5B;QACD,CAJD;MAKD;AAjd4B,aAAA;IAE5B;AA7BH,SA+BSgC,2BAAP,SAAA,yBACEC,WACAb,WACsB;AACtBc,4BAAoBD,WAAWb,SAAZ;AACnBnD,MAAAA,eAAcgE,SAAD;AACb,aAAO;IACR;AAtCH,QAAA,SAAA,KAAA;AAAA,WAwCEE,WAAA,SAAA,SAASoF,cAA4B;AACnCA,qBAAejG,KAAKC,IAAI,GAAGgG,YAAZ;AAEf,WAAKpG,SAAS,SAAAC,WAAa;AACzB,YAAIA,UAAUmG,iBAAiBA,cAAc;AAC3C,iBAAO;QACR;AACD,eAAO;UACLkB,iBACErH,UAAUmG,eAAeA,eAAe,YAAY;UACtDA;UACA1I,0BAA0B;QAJrB;MAMR,GAAE,KAAK6C,0BAVR;IAWD;AAtDH,WAwDEU,eAAA,SAAA,aAAaiE,OAAehE,OAAqC;AAAA,UAArCA,UAAqC,QAAA;AAArCA,gBAAuB;MAAc;AAC/D,UAAA,eAA8B,KAAKnE,OAA3BkJ,YAAR,aAAQA,WAAW2B,SAAnB,aAAmBA;AACnB,UAAQxB,eAAiB,KAAKjJ,MAAtBiJ;AAERlB,cAAQ/E,KAAKC,IAAI,GAAGD,KAAKE,IAAI6E,OAAOe,YAAY,CAA5B,CAAZ;AAKR,UAAI5E,gBAAgB;AACpB,UAAI,KAAKnE,WAAW;AAClB,YAAMwD,WAAa,KAAKxD;AACxB,YAAI0K,WAAW,YAAY;AACzBvG,0BACEX,SAASX,cAAcW,SAAShG,cAC5BZ,iBAAgB,IAChB;QACP,OAAM;AACLuH,0BACEX,SAASZ,eAAeY,SAASb,eAC7B/F,iBAAgB,IAChB;QACP;MACF;AAED,WAAKkH,SACHmF,+BACE,KAAKpJ,OACLmI,OACAhE,OACAkF,cACA,KAAKpJ,gBACLqE,aAN2B,CAD/B;IAUD;AA3FH,WA6FEK,oBAAA,SAAA,oBAAoB;AAClB,UAAA,eAAmD,KAAK3E,OAAhD/B,YAAR,aAAQA,WAAWuM,sBAAnB,aAAmBA,qBAAqBK,SAAxC,aAAwCA;AAExC,UAAI,OAAOL,wBAAwB,YAAY,KAAKrK,aAAa,MAAM;AACrE,YAAMwD,WAAa,KAAKxD;AAExB,YAAIlC,cAAc,gBAAgB4M,WAAW,cAAc;AACzDlH,mBAASvF,aAAaoM;QACvB,OAAM;AACL7G,mBAASlD,YAAY+J;QACtB;MACF;AAED,WAAK5F,oBAAL;IACD;AA3GH,WA6GEC,qBAAA,SAAA,qBAAqB;AACnB,UAAA,eAA8B,KAAK7E,OAA3B/B,YAAR,aAAQA,WAAW4M,SAAnB,aAAmBA;AACnB,UAAA,cAAmD,KAAKzK,OAAhDiJ,eAAR,YAAQA,cAAc1I,2BAAtB,YAAsBA;AAEtB,UAAIA,4BAA4B,KAAKR,aAAa,MAAM;AACtD,YAAMwD,WAAa,KAAKxD;AAGxB,YAAIlC,cAAc,gBAAgB4M,WAAW,cAAc;AACzD,cAAI5M,cAAc,OAAO;AAIvB,oBAAQH,iBAAgB,GAAxB;cACE,KAAK;AACH6F,yBAASvF,aAAa,CAACiL;AACvB;cACF,KAAK;AACH1F,yBAASvF,aAAaiL;AACtB;cACF;AACE,oBAAQ1L,cAA6BgG,SAA7BhG,aAAaqF,cAAgBW,SAAhBX;AACrBW,yBAASvF,aAAa4E,cAAcrF,cAAc0L;AAClD;YAVJ;UAYD,OAAM;AACL1F,qBAASvF,aAAaiL;UACvB;QACF,OAAM;AACL1F,mBAASlD,YAAY4I;QACtB;MACF;AAED,WAAKzE,oBAAL;IACD;AA/IH,WAiJEE,uBAAA,SAAA,uBAAuB;AACrB,UAAI,KAAK5E,+BAA+B,MAAM;AAC5C/D,sBAAc,KAAK+D,0BAAN;MACd;IACF;AArJH,WAuJE6E,SAAA,SAAA,SAAS;AACP,UAAA,eAiBI,KAAK/E,OAhBPgF,WADF,aACEA,UACAC,YAFF,aAEEA,WACAhH,YAHF,aAGEA,WACAX,SAJF,aAIEA,QACA4H,WALF,aAKEA,UACAC,mBANF,aAMEA,kBACAC,eAPF,aAOEA,cACA8D,YARF,aAQEA,WACA7D,WATF,aASEA,UATF,uBAAA,aAUEC,SAAAA,UAVF,yBAAA,SAUYhH,mBAVZ,sBAWEuM,SAXF,aAWEA,QACAtF,mBAZF,aAYEA,kBACAC,eAbF,aAaEA,cACApI,QAdF,aAcEA,OACAqI,iBAfF,aAeEA,gBACApI,QAhBF,aAgBEA;AAEF,UAAQiD,cAAgB,KAAKF,MAArBE;AAGR,UAAMwK,eACJ7M,cAAc,gBAAgB4M,WAAW;AAE3C,UAAMpJ,WAAWqJ,eACb,KAAKE,sBACL,KAAKC;AAET,UAAA,wBAAgC,KAAKC,kBAAL,GAAzBzE,aAAP,sBAAA,CAAA,GAAmBC,YAAnB,sBAAA,CAAA;AAEA,UAAMV,QAAQ,CAAA;AACd,UAAIkD,YAAY,GAAG;AACjB,iBAASf,SAAQ1B,YAAY0B,UAASzB,WAAWyB,UAAS;AACxDnC,gBAAMC,SACJ9I,4BAAc6H,UAAU;YACtBxG,MAAM6G;YACNtD,KAAKuD,QAAQ6C,QAAO9C,QAAR;YACZ8C,OAAAA;YACA7H,aAAamF,iBAAiBnF,cAAc+B;YAC5CjF,OAAO,KAAKsE,cAAcyG,MAAnB;UALe,CAAX,CADf;QASD;MACF;AAID,UAAMmB,qBAAqBa,uBACzB,KAAKnK,OACL,KAAKC,cAFyC;AAKhD,iBAAO9C,4BACLoI,oBAAoBC,gBAAgB,OACpC;QACEP;QACAxD;QACAiC,KAAK,KAAKD;QACVrG,OAAK,SAAA;UACH+E,UAAU;UACV7E;UACAD;UACAE,UAAU;UACV2I,yBAAyB;UACzBC,YAAY;UACZlI;QAPG,GAQAb,KARA;MAJP,OAeAD,4BAAcgI,oBAAoBC,gBAAgB,OAAO;QACvDJ,UAAUgB;QACVtC,KAAKwB;QACL9H,OAAO;UACLE,QAAQwN,eAAe,SAASxB;UAChClD,eAAe9F,cAAc,SAAS+B;UACtChF,OAAOyN,eAAexB,qBAAqB;QAHtC;MAHgD,CAA5C,CAjBK;IA2BrB;AAvOH,WAgRE1E,sBAAA,SAAA,sBAAsB;AACpB,UAAI,OAAO,KAAK5E,MAAMuB,oBAAoB,YAAY;AACpD,YAAQ2H,YAAc,KAAKlJ,MAAnBkJ;AACR,YAAIA,YAAY,GAAG;AACjB,cAAA,yBAKI,KAAKgC,kBAAL,GAJFT,sBADF,uBAAA,CAAA,GAEEC,qBAFF,uBAAA,CAAA,GAGEC,qBAHF,uBAAA,CAAA,GAIEC,oBAJF,uBAAA,CAAA;AAMA,eAAK/J,qBACH4J,qBACAC,oBACAC,oBACAC,iBAJF;QAMD;MACF;AAED,UAAI,OAAO,KAAK5K,MAAMyB,aAAa,YAAY;AAC7C,YAAA,eAII,KAAKrB,OAHPmK,mBADF,aACEA,iBACAlB,gBAFF,aAEEA,cACA1I,4BAHF,aAGEA;AAEF,aAAKa,cACH+I,kBACAlB,eACA1I,yBAHF;MAKD;IACF;AA/SH,WA4VEuK,oBAAA,SAAA,oBAAsD;AACpD,UAAA,eAAqC,KAAKlL,OAAlCkJ,YAAR,aAAQA,WAAW3C,gBAAnB,aAAmBA;AACnB,UAAA,eAAuD,KAAKnG,OAApDE,cAAR,aAAQA,aAAaiK,kBAArB,aAAqBA,iBAAiBlB,eAAtC,aAAsCA;AAEtC,UAAIH,cAAc,GAAG;AACnB,eAAO,CAAC,GAAG,GAAG,GAAG,CAAV;MACR;AAED,UAAMzC,aAAa4D,wBACjB,KAAKrK,OACLqJ,cACA,KAAKpJ,cAHkC;AAKzC,UAAMyG,YAAY4D,2BAChB,KAAKtK,OACLyG,YACA4C,cACA,KAAKpJ,cAJoC;AAS3C,UAAM0G,mBACJ,CAACrG,eAAeiK,oBAAoB,aAChCnH,KAAKC,IAAI,GAAGkD,aAAZ,IACA;AACN,UAAMK,kBACJ,CAACtG,eAAeiK,oBAAoB,YAChCnH,KAAKC,IAAI,GAAGkD,aAAZ,IACA;AAEN,aAAO,CACLnD,KAAKC,IAAI,GAAGoD,aAAaE,gBAAzB,GACAvD,KAAKC,IAAI,GAAGD,KAAKE,IAAI4F,YAAY,GAAGxC,YAAYE,eAApC,CAAZ,GACAH,YACAC,SAJK;IAMR;AAjYH,WAAA;EAAA,GAA6BK,0BAA7B,GAAA,OAKSC,eAAe;IACpB/I,WAAW;IACXoH,UAAUhD;IACVwI,QAAQ;IACRtE,eAAe;IACfd,gBAAgB;EALI,GALxB;AA8eD;AAQD,IAAMzB,wBAAsB,SAAtBA,qBAAsB,OAAA,OAWjB;AAAA,MATPgB,WASO,MATPA,UACA/G,YAQO,MARPA,WACAX,SAOO,MAPPA,QACAuN,SAMO,MANPA,QACAzF,eAKO,MALPA,cACAI,eAIO,MAJPA,cACAnI,QAGO,MAHPA;AAGO,MADPgD,WACO,MADPA;AAEF,MAAIxB,MAAuC;AACzC,QAAIuG,gBAAgB,QAAQI,gBAAgB,MAAM;AAChD,UAAI5G,wBAAsB,CAACA,qBAAmBqI,IAAI5G,QAAvB,GAAkC;AAC3DzB,6BAAmBsI,IAAI7G,QAAvB;AACA8G,gBAAQC,KACN,mIADF;MAID;IACF;AAGD,QAAM0D,eAAe7M,cAAc,gBAAgB4M,WAAW;AAE9D,YAAQ5M,WAAR;MACE,KAAK;MACL,KAAK;AACH,YAAI+L,wBAAwB,CAACA,qBAAqB/C,IAAI5G,QAAzB,GAAoC;AAC/D2J,+BAAqB9C,IAAI7G,QAAzB;AACA8G,kBAAQC,KACN,uJADF;QAID;AACD;MACF,KAAK;MACL,KAAK;AAEH;MACF;AACE,cAAMC,MACJ,6FAAA,MAEMpJ,YAFN,mBADS;IAhBf;AAuBA,YAAQ4M,QAAR;MACE,KAAK;MACL,KAAK;AAEH;MACF;AACE,cAAMxD,MACJ,sGAAA,MAEMwD,SAFN,mBADS;IANf;AAaA,QAAI7F,YAAY,MAAM;AACpB,YAAMqC,MACJ,wFAAA,OAEMrC,aAAa,OAAO,SAAS,OAAOA,YAF1C,mBADS;IAKZ;AAED,QAAI8F,gBAAgB,OAAOzN,UAAU,UAAU;AAC7C,YAAMgK,MACJ,oGAAA,OAEMhK,UAAU,OAAO,SAAS,OAAOA,SAFvC,mBADS;IAKZ,WAAU,CAACyN,gBAAgB,OAAOxN,WAAW,UAAU;AACtD,YAAM+J,MACJ,oGAAA,OAEM/J,WAAW,OAAO,SAAS,OAAOA,UAFxC,mBADS;IAKZ;EACF;AACF;ACluBD,IAAMgK,gCAA8B;AAmBpC,IAAMW,oBAAkB,SAAlBA,iBACJjI,OACAmI,OACAC,eACiB;AACjB,MAAA,OAAuBpI,OAAfsI,WAAR,KAAQA;AACR,MAAQD,kBAAuCD,cAAvCC,iBAAiBE,oBAAsBH,cAAtBG;AAEzB,MAAIJ,QAAQI,mBAAmB;AAC7B,QAAItG,SAAS;AACb,QAAIsG,qBAAqB,GAAG;AAC1B,UAAMZ,eAAeU,gBAAgBE,iBAAD;AACpCtG,eAAS0F,aAAa1F,SAAS0F,aAAa7K;IAC7C;AAED,aAAS0L,IAAID,oBAAoB,GAAGC,KAAKL,OAAOK,KAAK;AACnD,UAAI1L,QAASwL,SAAgCE,CAAlC;AAEXH,sBAAgBG,CAAD,IAAM;QACnBvG;QACAnF,MAAAA;MAFmB;AAKrBmF,gBAAUnF;IACX;AAEDsL,kBAAcG,oBAAoBJ;EACnC;AAED,SAAOE,gBAAgBF,KAAD;AACvB;AAED,IAAMM,oBAAkB,SAAlBA,iBACJzI,OACAoI,eACAnG,QACG;AACH,MAAQoG,kBAAuCD,cAAvCC,iBAAiBE,oBAAsBH,cAAtBG;AAEzB,MAAMG,yBACJH,oBAAoB,IAAIF,gBAAgBE,iBAAD,EAAoBtG,SAAS;AAEtE,MAAIyG,0BAA0BzG,QAAQ;AAEpC,WAAO0G,8BACL3I,OACAoI,eACAG,mBACA,GACAtG,MALgC;EAOnC,OAAM;AAIL,WAAO2G,mCACL5I,OACAoI,eACAhF,KAAKC,IAAI,GAAGkF,iBAAZ,GACAtG,MAJqC;EAMxC;AACF;AAED,IAAM0G,gCAA8B,SAA9BA,6BACJ3I,OACAoI,eACAS,MACAC,KACA7G,QACW;AACX,SAAO6G,OAAOD,MAAM;AAClB,QAAME,SAASD,MAAM1F,KAAK4F,OAAOH,OAAOC,OAAO,CAA1B;AACrB,QAAMG,gBAAgBhB,kBAAgBjI,OAAO+I,QAAQX,aAAhB,EAA+BnG;AAEpE,QAAIgH,kBAAkBhH,QAAQ;AAC5B,aAAO8G;IACR,WAAUE,gBAAgBhH,QAAQ;AACjC6G,YAAMC,SAAS;IAChB,WAAUE,gBAAgBhH,QAAQ;AACjC4G,aAAOE,SAAS;IACjB;EACF;AAED,MAAID,MAAM,GAAG;AACX,WAAOA,MAAM;EACd,OAAM;AACL,WAAO;EACR;AACF;AAED,IAAMF,qCAAmC,SAAnCA,kCACJ5I,OACAoI,eACAD,OACAlG,QACW;AACX,MAAQiH,YAAclJ,MAAdkJ;AACR,MAAIC,WAAW;AAEf,SACEhB,QAAQe,aACRjB,kBAAgBjI,OAAOmI,OAAOC,aAAf,EAA8BnG,SAASA,QACtD;AACAkG,aAASgB;AACTA,gBAAY;EACb;AAED,SAAOR,8BACL3I,OACAoI,eACAhF,KAAKE,IAAI6E,OAAOe,YAAY,CAA5B,GACA9F,KAAK4F,MAAMb,QAAQ,CAAnB,GACAlG,MALgC;AAOnC;AAED,IAAMkI,wBAAwB,SAAxBA,uBAAwB,OAAA,OAGzB;AAAA,MAFDjB,YAEC,MAFDA;AAEC,MADDb,kBACC,MADDA,iBAAiB8C,oBAChB,MADgBA,mBAAmB5C,oBACnC,MADmCA;AAEtC,MAAI6C,2BAA2B;AAI/B,MAAI7C,qBAAqBW,WAAW;AAClCX,wBAAoBW,YAAY;EACjC;AAED,MAAIX,qBAAqB,GAAG;AAC1B,QAAMZ,eAAeU,gBAAgBE,iBAAD;AACpC6C,+BAA2BzD,aAAa1F,SAAS0F,aAAa7K;EAC/D;AAED,MAAM8K,qBAAqBsB,YAAYX,oBAAoB;AAC3D,MAAMV,6BAA6BD,qBAAqBuD;AAExD,SAAOC,2BAA2BvD;AACnC;AAED,IAAMwD,mBAAmBpB,oBAAoB;EAC3CC,eAAe,SAAA,cACblK,OACAmI,OACAC,eAHa;AAAA,WAIFH,kBAAgBjI,OAAOmI,OAAOC,aAAf,EAA8BnG;EAJ3C;EAMfmI,aAAa,SAAA,YACXpK,OACAmI,OACAC,eAHW;AAAA,WAIAA,cAAcC,gBAAgBF,KAA9B,EAAqCrL;EAJrC;EAMbqN;EAEAf,+BAA+B,SAAAA,+BAC7BpJ,OACAmI,OACAhE,OACAkF,cACAjB,eACA9D,eACW;AACX,QAAQrG,YAAqC+B,MAArC/B,WAAWX,SAA0B0C,MAA1B1C,QAAQuN,SAAkB7K,MAAlB6K,QAAQxN,QAAU2C,MAAV3C;AAGnC,QAAMyN,eAAe7M,cAAc,gBAAgB4M,WAAW;AAC9D,QAAM/N,QAAUgO,eAAezN,QAAQC;AACvC,QAAMqK,eAAeM,kBAAgBjI,OAAOmI,OAAOC,aAAf;AAIpC,QAAMkB,qBAAqBa,sBAAsBnK,OAAOoI,aAAR;AAEhD,QAAMmB,YAAYnG,KAAKC,IACrB,GACAD,KAAKE,IAAIgG,qBAAqBxM,OAAM6K,aAAa1F,MAAjD,CAFgB;AAIlB,QAAMuH,YAAYpG,KAAKC,IACrB,GACAsE,aAAa1F,SAASnF,QAAO6K,aAAa7K,OAAOwH,aAFjC;AAKlB,QAAIH,UAAU,SAAS;AACrB,UACEkF,gBAAgBG,YAAY1M,SAC5BuM,gBAAgBE,YAAYzM,OAC5B;AACAqH,gBAAQ;MACT,OAAM;AACLA,gBAAQ;MACT;IACF;AAED,YAAQA,OAAR;MACE,KAAK;AACH,eAAOoF;MACT,KAAK;AACH,eAAOC;MACT,KAAK;AACH,eAAOpG,KAAKqG,MAAMD,aAAaD,YAAYC,aAAa,CAAjD;MACT,KAAK;MACL;AACE,YAAIH,gBAAgBG,aAAaH,gBAAgBE,WAAW;AAC1D,iBAAOF;QACR,WAAUA,eAAeG,WAAW;AACnC,iBAAOA;QACR,OAAM;AACL,iBAAOD;QACR;IAfL;EAiBD;EAEDc,wBAAwB,SAAA,uBACtBrK,OACAiC,QACAmG,eAHsB;AAAA,WAIXK,kBAAgBzI,OAAOoI,eAAenG,MAAvB;EAJJ;EAMxBqI,2BAA2B,SAAA,0BACzBtK,OACAyG,YACA4C,cACAjB,eACW;AACX,QAAQnK,YAAgD+B,MAAhD/B,WAAWX,SAAqC0C,MAArC1C,QAAQ4L,YAA6BlJ,MAA7BkJ,WAAW2B,SAAkB7K,MAAlB6K,QAAQxN,QAAU2C,MAAV3C;AAG9C,QAAMyN,eAAe7M,cAAc,gBAAgB4M,WAAW;AAC9D,QAAM/N,QAAUgO,eAAezN,QAAQC;AACvC,QAAMqK,eAAeM,kBAAgBjI,OAAOyG,YAAY2B,aAApB;AACpC,QAAMmB,YAAYF,eAAevM;AAEjC,QAAImF,SAAS0F,aAAa1F,SAAS0F,aAAa7K;AAChD,QAAI4J,YAAYD;AAEhB,WAAOC,YAAYwC,YAAY,KAAKjH,SAASsH,WAAW;AACtD7C;AACAzE,gBAAUgG,kBAAgBjI,OAAO0G,WAAW0B,aAAnB,EAAkCtL;IAC5D;AAED,WAAO4J;EACR;EAED7G,mBAxG2C,SAAAA,mBAwGzBG,OAAmBK,UAA8B;AACjE,QAAA,QAAgCL,OAAxBmL,oBAAR,MAAQA;AAER,QAAM/C,gBAAgB;MACpBC,iBAAiB,CAAA;MACjB8C,mBAAmBA,qBAAqB7D;MACxCiB,mBAAmB;IAHC;AAMtBlI,aAASiL,kBAAkB,SACzBnD,OACAyB,mBACG;AAAA,UADHA,sBACG,QAAA;AADHA,4BAA8B;MAC3B;AACHxB,oBAAcG,oBAAoBnF,KAAKE,IACrC8E,cAAcG,mBACdJ,QAAQ,CAFwB;AASlC9H,eAASyB,mBAAmB,EAA5B;AAEA,UAAI8H,mBAAmB;AACrBvJ,iBAAS0J,YAAT;MACD;IACF;AAED,WAAO3B;EACR;EAEDtI,uCAAuC;EAEvCC,eAAe,SAAAA,eAAA,OAAoC;AAAA,QAAjCuI,WAAiC,MAAjCA;AAChB,QAAIzJ,MAAuC;AACzC,UAAI,OAAOyJ,aAAa,YAAY;AAClC,cAAMjB,MACJ,iFAAA,OAEMiB,aAAa,OAAO,SAAS,OAAOA,YAF1C,mBADS;MAKZ;IACF;EACF;AApJ0C,CAAD;AC/J5C,IAAMiD,gBAAgBvM,oBAAoB;EACxCC,iBAAiB,SAAAA,iBAAA,MAA8BkJ,OAA9B;AAAA,QAAGxG,cAAH,KAAGA;AAAH,WACfwG,QAAUxG;EADK;EAGjBvC,gBAAgB,SAAAA,gBAAA,OAA8B+I,OAA9B;AAAA,QAAGxG,cAAH,MAAGA;AAAH,WACZA;EADY;EAGhBjC,cAAc,SAAAA,cAAA,OAA4ByI,OAA5B;AAAA,QAAGvG,YAAH,MAAGA;AAAH,WACZuG,QAAUvG;EADE;EAGdnC,cAAc,SAAAA,cAAA,OAA4B0I,OAA5B;AAAA,QAAGvG,YAAH,MAAGA;AAAH,WACVA;EADU;EAGdvC,yBAAyB,SAAAA,yBAAA,OAAA;AAAA,QAAGgF,WAAH,MAAGA,UAAUzC,YAAb,MAAaA;AAAb,WACrBA,YAA2ByC;EADN;EAGzB/E,wBAAwB,SAAAA,wBAAA,OAAA;AAAA,QAAG8E,cAAH,MAAGA,aAAazC,cAAhB,MAAgBA;AAAhB,WACpBA,cAA6ByC;EADT;EAGxB7E,gCAAgC,SAAAA,gCAAA,OAE9BhB,aACA4F,OACA/F,YACAgK,eACA9D,eACW;AAAA,QANTF,cAMS,MANTA,aAAazC,cAMJ,MANIA,aAAatE,QAMjB,MANiBA;AAO5B,QAAMmO,mBAAmBpI,KAAKC,IAC5B,GACAe,cAAgBzC,cAA6BtE,KAFtB;AAIzB,QAAMkM,YAAYnG,KAAKE,IACrBkI,kBACAjN,cAAgBoD,WAFA;AAIlB,QAAM6H,YAAYpG,KAAKC,IACrB,GACA9E,cAAgBoD,cACdtE,QACAiH,gBACE3C,WALY;AAQlB,QAAIwC,UAAU,SAAS;AACrB,UAAI/F,cAAcoL,YAAYnM,SAASe,cAAcmL,YAAYlM,OAAO;AACtE8G,gBAAQ;MACT,OAAM;AACLA,gBAAQ;MACT;IACF;AAED,YAAQA,OAAR;MACE,KAAK;AACH,eAAOoF;MACT,KAAK;AACH,eAAOC;MACT,KAAK;AAGH,YAAMiC,eAAerI,KAAKqG,MACxBD,aAAaD,YAAYC,aAAa,CADnB;AAGrB,YAAIiC,eAAerI,KAAKsI,KAAKrO,QAAQ,CAAlB,GAAsB;AACvC,iBAAO;QACR,WAAUoO,eAAeD,mBAAmBpI,KAAK4F,MAAM3L,QAAQ,CAAnB,GAAuB;AAClE,iBAAOmO;QACR,OAAM;AACL,iBAAOC;QACR;MACH,KAAK;MACL;AACE,YAAIrN,cAAcoL,aAAapL,cAAcmL,WAAW;AACtD,iBAAOnL;QACR,WAAUoL,YAAYD,WAAW;AAGhC,iBAAOC;QACR,WAAUpL,aAAaoL,WAAW;AACjC,iBAAOA;QACR,OAAM;AACL,iBAAOD;QACR;IA9BL;EAgCD;EAED/J,6BAA6B,SAAAA,6BAAA,OAE3Bf,UACA0F,OACA1D,WACA2H,eACA9D,eACW;AAAA,QANT1C,YAMS,MANTA,WAAWtE,SAMF,MANEA,QAAQ+G,WAMV,MANUA;AAOrB,QAAMsH,gBAAgBvI,KAAKC,IACzB,GACAgB,WAAazC,YAA2BtE,MAFpB;AAItB,QAAMiM,YAAYnG,KAAKE,IACrBqI,eACAlN,WAAamD,SAFG;AAIlB,QAAM4H,YAAYpG,KAAKC,IACrB,GACA5E,WAAamD,YACXtE,SACAgH,gBACE1C,SALY;AAQlB,QAAIuC,UAAU,SAAS;AACrB,UAAI1D,aAAa+I,YAAYlM,UAAUmD,aAAa8I,YAAYjM,QAAQ;AACtE6G,gBAAQ;MACT,OAAM;AACLA,gBAAQ;MACT;IACF;AAED,YAAQA,OAAR;MACE,KAAK;AACH,eAAOoF;MACT,KAAK;AACH,eAAOC;MACT,KAAK;AAGH,YAAMiC,eAAerI,KAAKqG,MACxBD,aAAaD,YAAYC,aAAa,CADnB;AAGrB,YAAIiC,eAAerI,KAAKsI,KAAKpO,SAAS,CAAnB,GAAuB;AACxC,iBAAO;QACR,WAAUmO,eAAeE,gBAAgBvI,KAAK4F,MAAM1L,SAAS,CAApB,GAAwB;AAChE,iBAAOqO;QACR,OAAM;AACL,iBAAOF;QACR;MACH,KAAK;MACL;AACE,YAAIhL,aAAa+I,aAAa/I,aAAa8I,WAAW;AACpD,iBAAO9I;QACR,WAAU+I,YAAYD,WAAW;AAGhC,iBAAOC;QACR,WAAU/I,YAAY+I,WAAW;AAChC,iBAAOA;QACR,OAAM;AACL,iBAAOD;QACR;IA9BL;EAgCD;EAEDrK,8BAA8B,SAAAA,8BAAA,OAE5Bd,YAF4B;AAAA,QAC1BuD,cAD0B,MAC1BA,aAAayC,cADa,MACbA;AADa,WAI5BhB,KAAKC,IACH,GACAD,KAAKE,IACHc,cAAc,GACdhB,KAAK4F,MAAM5K,aAAeuD,WAA1B,CAFF,CAFF;EAJ4B;EAY9BxC,iCAAiC,SAAAA,iCAAA,QAE/BsH,YACArI,YACW;AAAA,QAHTuD,cAGS,OAHTA,aAAayC,cAGJ,OAHIA,aAAa/G,QAGjB,OAHiBA;AAI5B,QAAM+E,OAAOqE,aAAe9E;AAC5B,QAAMiK,oBAAoBxI,KAAKsI,MAC5BrO,QAAQe,aAAagE,QAAUT,WADR;AAG1B,WAAOyB,KAAKC,IACV,GACAD,KAAKE;MACHc,cAAc;MACdqC,aAAamF,oBAAoB;;IAFnC,CAFK;EAOR;EAEDjM,2BAA2B,SAAAA,2BAAA,QAEzBc,WAFyB;AAAA,QACvBmB,YADuB,OACvBA,WAAWyC,WADY,OACZA;AADY,WAIzBjB,KAAKC,IACH,GACAD,KAAKE,IAAIe,WAAW,GAAGjB,KAAK4F,MAAMvI,YAAcmB,SAAzB,CAAvB,CAFF;EAJyB;EAS3BhC,8BAA8B,SAAAA,8BAAA,QAE5B6G,YACAhG,WACW;AAAA,QAHTmB,YAGS,OAHTA,WAAWyC,WAGF,OAHEA,UAAU/G,SAGZ,OAHYA;AAIvB,QAAMiF,MAAMkE,aAAe7E;AAC3B,QAAMiK,iBAAiBzI,KAAKsI,MACzBpO,SAASmD,YAAY8B,OAASX,SADV;AAGvB,WAAOwB,KAAKC,IACV,GACAD,KAAKE;MACHe,WAAW;MACXoC,aAAaoF,iBAAiB;;IAFhC,CAFK;EAOR;EAEDhM,mBAhNwC,SAAAA,mBAgNtBG,OAAwB;EAEzC;EAEDF,uCAAuC;EAEvCC,eAAe,SAAAA,eAAA,QAAkD;AAAA,QAA/C4B,cAA+C,OAA/CA,aAAaC,YAAkC,OAAlCA;AAC7B,QAAI/C,MAAuC;AACzC,UAAI,OAAO8C,gBAAgB,UAAU;AACnC,cAAM0F,MACJ,kFAAA,OAGI1F,gBAAgB,OAAO,SAAS,OAAOA,eAH3C,mBADS;MAOZ;AAED,UAAI,OAAOC,cAAc,UAAU;AACjC,cAAMyF,MACJ,gFAAA,OAEMzF,cAAc,OAAO,SAAS,OAAOA,aAF3C,mBADS;MAKZ;IACF;EACF;AA1OuC,CAAD;ACEzC,IAAMkK,gBAAgB7B,oBAAoB;EACxCC,eAAe,SAAAA,eAAA,MAA2B/B,OAA3B;AAAA,QAAGG,WAAH,KAAGA;AAAH,WACbH,QAAUG;EADG;EAGf8B,aAAa,SAAAA,aAAA,OAA2BjC,OAA3B;AAAA,QAAGG,WAAH,MAAGA;AAAH,WACTA;EADS;EAGb6B,uBAAuB,SAAAA,uBAAA,OAAA;AAAA,QAAGjB,YAAH,MAAGA,WAAWZ,WAAd,MAAcA;AAAd,WACnBA,WAA0BY;EADP;EAGvBE,+BAA+B,SAAAA,+BAAA,OAE7BjB,OACAhE,OACAkF,cACAjB,eACA9D,eACW;AAAA,QANTrG,YAMS,MANTA,WAAWX,SAMF,MANEA,QAAQ4L,YAMV,MANUA,WAAWZ,WAMrB,MANqBA,UAAUuC,SAM/B,MAN+BA,QAAQxN,QAMvC,MANuCA;AAQlD,QAAMyN,eAAe7M,cAAc,gBAAgB4M,WAAW;AAC9D,QAAM/N,QAAUgO,eAAezN,QAAQC;AACvC,QAAMyO,iBAAiB3I,KAAKC,IAC1B,GACA6F,YAAcZ,WAA0BxL,KAFnB;AAIvB,QAAMyM,YAAYnG,KAAKE,IACrByI,gBACA5D,QAAUG,QAFM;AAIlB,QAAMkB,YAAYpG,KAAKC,IACrB,GACA8E,QAAUG,WACRxL,QACEwL,WACFhE,aALc;AAQlB,QAAIH,UAAU,SAAS;AACrB,UACEkF,gBAAgBG,YAAY1M,SAC5BuM,gBAAgBE,YAAYzM,OAC5B;AACAqH,gBAAQ;MACT,OAAM;AACLA,gBAAQ;MACT;IACF;AAED,YAAQA,OAAR;MACE,KAAK;AACH,eAAOoF;MACT,KAAK;AACH,eAAOC;MACT,KAAK,UAAU;AAGb,YAAMiC,eAAerI,KAAKqG,MACxBD,aAAaD,YAAYC,aAAa,CADnB;AAGrB,YAAIiC,eAAerI,KAAKsI,KAAK5O,QAAO,CAAjB,GAAqB;AACtC,iBAAO;QACR,WAAU2O,eAAeM,iBAAiB3I,KAAK4F,MAAMlM,QAAO,CAAlB,GAAsB;AAC/D,iBAAOiP;QACR,OAAM;AACL,iBAAON;QACR;MACF;MACD,KAAK;MACL;AACE,YAAIpC,gBAAgBG,aAAaH,gBAAgBE,WAAW;AAC1D,iBAAOF;QACR,WAAUA,eAAeG,WAAW;AACnC,iBAAOA;QACR,OAAM;AACL,iBAAOD;QACR;IA3BL;EA6BD;EAEDc,wBAAwB,SAAAA,wBAAA,OAEtBpI,QAFsB;AAAA,QACpBiH,YADoB,MACpBA,WAAWZ,WADS,MACTA;AADS,WAItBlF,KAAKC,IACH,GACAD,KAAKE,IAAI4F,YAAY,GAAG9F,KAAK4F,MAAM/G,SAAWqG,QAAtB,CAAxB,CAFF;EAJsB;EASxBgC,2BAA2B,SAAAA,2BAAA,OAEzB7D,YACA4C,cACW;AAAA,QAHTpL,YAGS,MAHTA,WAAWX,SAGF,MAHEA,QAAQ4L,YAGV,MAHUA,WAAWZ,WAGrB,MAHqBA,UAAUuC,SAG/B,MAH+BA,QAAQxN,QAGvC,MAHuCA;AAKlD,QAAMyN,eAAe7M,cAAc,gBAAgB4M,WAAW;AAC9D,QAAM5I,SAASwE,aAAe6B;AAC9B,QAAMxL,QAAUgO,eAAezN,QAAQC;AACvC,QAAM0O,kBAAkB5I,KAAKsI,MAC1B5O,QAAOuM,eAAepH,UAAYqG,QADb;AAGxB,WAAOlF,KAAKC,IACV,GACAD,KAAKE;MACH4F,YAAY;MACZzC,aAAauF,kBAAkB;;IAFjC,CAFK;EAOR;EAEDnM,mBA7GwC,SAAAA,mBA6GtBG,OAAwB;EAEzC;EAEDF,uCAAuC;EAEvCC,eAAe,SAAAA,eAAA,OAAoC;AAAA,QAAjCuI,WAAiC,MAAjCA;AAChB,QAAIzJ,MAAuC;AACzC,UAAI,OAAOyJ,aAAa,UAAU;AAChC,cAAMjB,MACJ,+EAAA,OAEMiB,aAAa,OAAO,SAAS,OAAOA,YAF1C,mBADS;MAKZ;IACF;EACF;AA7HuC,CAAD;ACJ1B,SAAS2D,eAAeC,MAAcC,MAAuB;AAC1E,WAASC,aAAaF,MAAM;AAC1B,QAAI,EAAEE,aAAaD,OAAO;AACxB,aAAO;IACR;EACF;AACD,WAASC,cAAaD,MAAM;AAC1B,QAAID,KAAKE,UAAD,MAAgBD,KAAKC,UAAD,GAAa;AACvC,aAAO;IACR;EACF;AACD,SAAO;AACR;;;ACTc,SAASC,SACtBC,WACAvI,WACS;AACT,MAAewI,YAA2BD,UAAlClP,OAAqBoP,WAA7B,8BAA0CF,WAA1C,SAAA;AACA,MAAeG,YAA2B1I,UAAlC3G,OAAqBsP,WAA7B,8BAA0C3I,WAA1C,UAAA;AAEA,SACE,CAACkI,eAAeM,WAAWE,SAAZ,KAA0B,CAACR,eAAeO,UAAUE,QAAX;AAE3D;ACTc,SAASC,sBACtB5I,WACA6I,WACS;AACT,SACE,CAACP,SAAS,KAAKrM,OAAO+D,SAAb,KAA2BkI,eAAe,KAAK7L,OAAOwM,SAAb;AAErD;", "names": ["t", "e", "isEqual", "hasNativePerformanceNow", "performance", "now", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "tick", "call", "requestAnimationFrame", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "IS_SCROLLING_DEBOUNCE_INTERVAL", "defaultItemKey", "columnIndex", "data", "rowIndex", "devWarningsOverscanCount", "devWarningsOverscanRowsColumnsCount", "devWarningsTagName", "process", "window", "WeakSet", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "memoizeOne", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "key", "hasOwnProperty", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "estimatedTotalHeight", "estimatedTotalWidth", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "has", "add", "console", "warn", "Error", "DEFAULT_ESTIMATED_ITEM_SIZE", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "numUnmeasuredItems", "totalSizeOfUnmeasuredItems", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "index", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "i", "findNearestItem", "lastMeasuredItemOffset", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "devWarningsDirection", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "shouldComponentUpdate", "nextState"]}