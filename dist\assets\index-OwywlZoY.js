const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InformesService-DExmTbda.js","assets/auth-Cw5QfmsP.js","assets/ui-vendor-COFtXQcG.js","assets/react-vendor-C9XH6RF0.js","assets/ImprovedPinControlService-XBKYJtn2.js","assets/admin-COBm5LhQ.js","assets/admin-B4zyQOk2.css","assets/Dashboard-CzmZ2YPh.js","assets/enhancedSupabaseService-l8rh_Z40.js","assets/Profile-CG8sshPl.js","assets/Settings-BtnAXd6r.js","assets/Home-KEGZC7aD.js","assets/Help-zJ-JGg7W.js","assets/Configuracion-DHIDHWnV.js","assets/PinManagementService-DCClnbJn.js","assets/Candidates-DEdWuYpf.js","assets/useToast-BdMcWFRD.js","assets/VerbalInfo-DMr_XPf4.js","assets/Users-76G2iQen.js","assets/Reports-D9-TYKru.js","assets/Reports-Dl_zACwQ.css","assets/Patients-B0GIPyGn.js","assets/TestPage-DwpF-uOp.js","assets/CompleteReport-DrjCEflC.js","assets/interpretacionesAptitudes-Bt_sak-B.js","assets/SavedReports-C8OVLklp.js","assets/ViewSavedReport-BVZoAplE.js","assets/PinAssignmentPanel-BTvLVMrV.js","assets/Students-D9E4Btb8.js","assets/Tests-B0YJWWYQ.js","assets/Reports-V6zzM1ZL.js","assets/Tests-CBZ8QDe0.js","assets/TestCard-7H4NnEOB.js","assets/TestCard-BIeKcx_6.css","assets/Results-Cl5RXDBq.js","assets/Patients-DMJlJz6N.js","assets/utils-vendor-BIgtPQlV.js","assets/Patients-TixZtU9H.css","assets/Questionnaire-B_GPDx2Z.js","assets/InformePaciente-CaWt0M_-.js","assets/BasicLogin-O_elgI_M.js"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,o=(a,i,t)=>i in a?e(a,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[i]=t,n=(e,a)=>{for(var i in a||(a={}))r.call(a,i)&&o(e,i,a[i]);if(t)for(var i of t(a))s.call(a,i)&&o(e,i,a[i]);return e},l=(e,t)=>a(e,i(t)),c=(e,a)=>{var i={};for(var o in e)r.call(e,o)&&a.indexOf(o)<0&&(i[o]=e[o]);if(null!=e&&t)for(var o of t(e))a.indexOf(o)<0&&s.call(e,o)&&(i[o]=e[o]);return i},u=(e,a,i)=>o(e,"symbol"!=typeof a?a+"":a,i),d=(e,a,i)=>new Promise((t,r)=>{var s=e=>{try{n(i.next(e))}catch(Ls){r(Ls)}},o=e=>{try{n(i.throw(e))}catch(Ls){r(Ls)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,o);n((i=i.apply(e,a)).next())});import{j as m,O as p,D as b,E as f,s as g,aq as N,ar as x,a5 as h,K as v,v as j,a6 as V,a8 as y,w as E,d as C,a as D,r as B,Q as w,a7 as A,as as R,at as S,F as T,au as I}from"./auth-Cw5QfmsP.js";import{f as _,b as z,a as O,r as P,d as M,L as k,O as q,u as L,R as $,e as F,h as U,i as Q,j as W,N as H,B as G}from"./react-vendor-C9XH6RF0.js";import{Q as J,k as Y}from"./ui-vendor-COFtXQcG.js";import{C as K,b as X,a as Z,c as ee,B as ae,S as ie}from"./admin-COBm5LhQ.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver(e=>{for(const i of e)if("childList"===i.type)for(const e of i.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)}).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();var te,re={};const se=z(function(){if(te)return re;te=1;var e=_(),a=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;return re.createRoot=function(i,t){a.usingClientEntryPoint=!0;try{return e.createRoot(i,t)}finally{a.usingClientEntryPoint=!1}},re.hydrateRoot=function(i,t,r){a.usingClientEntryPoint=!0;try{return e.hydrateRoot(i,t,r)}finally{a.usingClientEntryPoint=!1}},re}());var oe,ne,le={exports:{}},ce={};ne||(ne=1,le.exports=(oe||(oe=1,
/**
   * @license React
   * use-sync-external-store-with-selector.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */
function(){"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var e=O(),a="function"==typeof Object.is?Object.is:function(e,a){return e===a&&(0!==e||1/e==1/a)||e!=e&&a!=a},i=e.useSyncExternalStore,t=e.useRef,r=e.useEffect,s=e.useMemo,o=e.useDebugValue;ce.useSyncExternalStoreWithSelector=function(e,n,l,c,u){var d=t(null);if(null===d.current){var m={hasValue:!1,value:null};d.current=m}else m=d.current;d=s(function(){function e(e){if(!r){if(r=!0,i=e,e=c(e),void 0!==u&&m.hasValue){var s=m.value;if(u(s,e))return t=s}return t=e}if(s=t,a(i,e))return s;var o=c(e);return void 0!==u&&u(s,o)?(i=e,s):(i=e,t=o)}var i,t,r=!1,s=void 0===l?null:l;return[function(){return e(n())},null===s?void 0:function(){return e(s())}]},[n,l,c,u]);var p=i(e,d[0],d[1]);return r(function(){m.hasValue=!0,m.value=p},[p]),o(p),p},"undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}()),ce)),le.exports;var ue={notify(){},get:()=>[]};var de=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),me=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),pe=(()=>de||me?P.useLayoutEffect:P.useEffect)(),be=Symbol.for("react-redux-context"),fe="undefined"!=typeof globalThis?globalThis:{};function ge(){var e;if(!P.createContext)return{};const a=null!=(e=fe[be])?e:fe[be]=new Map;let i=a.get(P.createContext);return i||(i=P.createContext(null),i.displayName="ReactRedux",a.set(P.createContext,i)),i}var Ne=ge();var xe=function(e){const{children:a,context:i,serverState:t,store:r}=e,s=P.useMemo(()=>{const a=function(e){let a,i=ue,t=0,r=!1;function s(){l.onStateChange&&l.onStateChange()}function o(){t++,a||(a=e.subscribe(s),i=function(){let e=null,a=null;return{clear(){e=null,a=null},notify(){(()=>{let a=e;for(;a;)a.callback(),a=a.next})()},get(){const a=[];let i=e;for(;i;)a.push(i),i=i.next;return a},subscribe(i){let t=!0;const r=a={callback:i,next:null,prev:a};return r.prev?r.prev.next=r:e=r,function(){t&&null!==e&&(t=!1,r.next?r.next.prev=r.prev:a=r.prev,r.prev?r.prev.next=r.next:e=r.next)}}}}())}function n(){t--,a&&0===t&&(a(),a=void 0,i.clear(),i=ue)}const l={addNestedSub:function(e){o();const a=i.subscribe(e);let t=!1;return()=>{t||(t=!0,a(),n())}},notifyNestedSubs:function(){i.notify()},handleChangeWrapper:s,isSubscribed:function(){return r},trySubscribe:function(){r||(r=!0,o())},tryUnsubscribe:function(){r&&(r=!1,n())},getListeners:()=>i};return l}(r),i={store:r,subscription:a,getServerState:t?()=>t:void 0};{const{identityFunctionCheck:a="once",stabilityCheck:t="once"}=e;return Object.assign(i,{stabilityCheck:t,identityFunctionCheck:a})}},[r,t]),o=P.useMemo(()=>r.getState(),[r]);pe(()=>{const{subscription:e}=s;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==r.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[s,o]);const n=i||Ne;return P.createElement(n.Provider,{value:s},a)},he=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),ve=()=>Math.random().toString(36).substring(7).split("").join("."),je={INIT:`@@redux/INIT${ve()}`,REPLACE:`@@redux/REPLACE${ve()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${ve()}`};function Ve(e){if("object"!=typeof e||null===e)return!1;let a=e;for(;null!==Object.getPrototypeOf(a);)a=Object.getPrototypeOf(a);return Object.getPrototypeOf(e)===a||null===Object.getPrototypeOf(e)}function ye(e){if(void 0===e)return"undefined";if(null===e)return"null";const a=typeof e;switch(a){case"boolean":case"string":case"number":case"symbol":case"function":return a}if(Array.isArray(e))return"array";if(function(e){return e instanceof Date||"function"==typeof e.toDateString&&"function"==typeof e.getDate&&"function"==typeof e.setDate}(e))return"date";if(function(e){return e instanceof Error||"string"==typeof e.message&&e.constructor&&"number"==typeof e.constructor.stackTraceLimit}(e))return"error";const i=function(e){return"function"==typeof e.constructor?e.constructor.name:null}(e);switch(i){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return i}return Object.prototype.toString.call(e).slice(8,-1).toLowerCase().replace(/\s/g,"")}function Ee(e){let a=typeof e;return a=ye(e),a}function Ce(e,a,i){if("function"!=typeof e)throw new Error(`Expected the root reducer to be a function. Instead, received: '${Ee(e)}'`);if("function"==typeof a&&"function"==typeof i||"function"==typeof i&&"function"==typeof arguments[3])throw new Error("It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.");if("function"==typeof a&&void 0===i&&(i=a,a=void 0),void 0!==i){if("function"!=typeof i)throw new Error(`Expected the enhancer to be a function. Instead, received: '${Ee(i)}'`);return i(Ce)(e,a)}let t=e,r=a,s=new Map,o=s,n=0,l=!1;function c(){o===s&&(o=new Map,s.forEach((e,a)=>{o.set(a,e)}))}function u(){if(l)throw new Error("You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");return r}function d(e){if("function"!=typeof e)throw new Error(`Expected the listener to be a function. Instead, received: '${Ee(e)}'`);if(l)throw new Error("You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.");let a=!0;c();const i=n++;return o.set(i,e),function(){if(a){if(l)throw new Error("You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.");a=!1,c(),o.delete(i),s=null}}}function m(e){if(!Ve(e))throw new Error(`Actions must be plain objects. Instead, the actual type was: '${Ee(e)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);if(void 0===e.type)throw new Error('Actions may not have an undefined "type" property. You may have misspelled an action type string constant.');if("string"!=typeof e.type)throw new Error(`Action "type" property must be a string. Instead, the actual type was: '${Ee(e.type)}'. Value was: '${e.type}' (stringified)`);if(l)throw new Error("Reducers may not dispatch actions.");try{l=!0,r=t(r,e)}finally{l=!1}return(s=o).forEach(e=>{e()}),e}m({type:je.INIT});return{dispatch:m,subscribe:d,getState:u,replaceReducer:function(e){if("function"!=typeof e)throw new Error(`Expected the nextReducer to be a function. Instead, received: '${Ee(e)}`);t=e,m({type:je.REPLACE})},[he]:function(){const e=d;return{subscribe(a){if("object"!=typeof a||null===a)throw new Error(`Expected the observer to be an object. Instead, received: '${Ee(a)}'`);function i(){const e=a;e.next&&e.next(u())}i();return{unsubscribe:e(i)}},[he](){return this}}}}}function De(e){"undefined"!=typeof console&&console.error;try{throw new Error(e)}catch(Ls){}}function Be(e){const a=Object.keys(e),i={};for(let o=0;o<a.length;o++){const t=a[o];void 0===e[t]&&De(`No reducer provided for key "${t}"`),"function"==typeof e[t]&&(i[t]=e[t])}const t=Object.keys(i);let r,s;r={};try{!function(e){Object.keys(e).forEach(a=>{const i=e[a];if(void 0===i(void 0,{type:je.INIT}))throw new Error(`The slice reducer for key "${a}" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);if(void 0===i(void 0,{type:je.PROBE_UNKNOWN_ACTION()}))throw new Error(`The slice reducer for key "${a}" returned undefined when probed with a random type. Don't try to handle '${je.INIT}' or other actions in "redux/*" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`)})}(i)}catch(Ls){s=Ls}return function(e={},a){if(s)throw s;{const t=function(e,a,i,t){const r=Object.keys(a),s=i&&i.type===je.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(0===r.length)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!Ve(e))return`The ${s} has unexpected type of "${Ee(e)}". Expected argument to be an object with the following keys: "${r.join('", "')}"`;const o=Object.keys(e).filter(e=>!a.hasOwnProperty(e)&&!t[e]);return o.forEach(e=>{t[e]=!0}),i&&i.type===je.REPLACE?void 0:o.length>0?`Unexpected ${o.length>1?"keys":"key"} "${o.join('", "')}" found in ${s}. Expected to find one of the known reducer keys instead: "${r.join('", "')}". Unexpected keys will be ignored.`:void 0}(e,i,a,r);t&&De(t)}let o=!1;const n={};for(let r=0;r<t.length;r++){const s=t[r],l=i[s],c=e[s],u=l(c,a);if(void 0===u){const e=a&&a.type;throw new Error(`When called with an action of type ${e?`"${String(e)}"`:"(unknown type)"}, the slice reducer for key "${s}" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`)}n[s]=u,o=o||u!==c}return o=o||t.length!==Object.keys(e).length,o?n:e}}function we(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,a)=>(...i)=>e(a(...i)))}function Ae(e){return Ve(e)&&"type"in e&&"string"==typeof e.type}var Re=Symbol.for("immer-nothing"),Se=Symbol.for("immer-draftable"),Te=Symbol.for("immer-state"),Ie=[function(e){return`The plugin for '${e}' has not been loaded into Immer. To enable the plugin, import and call \`enable${e}()\` when initializing your application.`},function(e){return`produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${e}'`},"This object has been frozen and should not be mutated",function(e){return"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? "+e},"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.","Immer forbids circular references","The first or second argument to `produce` must be a function","The third argument to `produce` must be a function or undefined","First argument to `createDraft` must be a plain object, an array, or an immerable object","First argument to `finishDraft` must be a draft returned by `createDraft`",function(e){return`'current' expects a draft, got: ${e}`},"Object.defineProperty() cannot be used on an Immer draft","Object.setPrototypeOf() cannot be used on an Immer draft","Immer only supports deleting array indices","Immer only supports setting array indices and the 'length' property",function(e){return`'original' expects a draft, got: ${e}`}];function _e(e,...a){{const i=Ie[e],t="function"==typeof i?i.apply(null,a):i;throw new Error(`[Immer] ${t}`)}}var ze=Object.getPrototypeOf;function Oe(e){return!!e&&!!e[Te]}function Pe(e){var a;return!!e&&(ke(e)||Array.isArray(e)||!!e[Se]||!!(null==(a=e.constructor)?void 0:a[Se])||Ue(e)||Qe(e))}var Me=Object.prototype.constructor.toString();function ke(e){if(!e||"object"!=typeof e)return!1;const a=ze(e);if(null===a)return!0;const i=Object.hasOwnProperty.call(a,"constructor")&&a.constructor;return i===Object||"function"==typeof i&&Function.toString.call(i)===Me}function qe(e,a){0===Le(e)?Reflect.ownKeys(e).forEach(i=>{a(i,e[i],e)}):e.forEach((i,t)=>a(t,i,e))}function Le(e){const a=e[Te];return a?a.type_:Array.isArray(e)?1:Ue(e)?2:Qe(e)?3:0}function $e(e,a){return 2===Le(e)?e.has(a):Object.prototype.hasOwnProperty.call(e,a)}function Fe(e,a,i){const t=Le(e);2===t?e.set(a,i):3===t?e.add(i):e[a]=i}function Ue(e){return e instanceof Map}function Qe(e){return e instanceof Set}function We(e){return e.copy_||e.base_}function He(e,a){if(Ue(e))return new Map(e);if(Qe(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const i=ke(e);if(!0===a||"class_only"===a&&!i){const a=Object.getOwnPropertyDescriptors(e);delete a[Te];let i=Reflect.ownKeys(a);for(let t=0;t<i.length;t++){const r=i[t],s=a[r];!1===s.writable&&(s.writable=!0,s.configurable=!0),(s.get||s.set)&&(a[r]={configurable:!0,writable:!0,enumerable:s.enumerable,value:e[r]})}return Object.create(ze(e),a)}{const a=ze(e);if(null!==a&&i)return n({},e);const t=Object.create(a);return Object.assign(t,e)}}function Ge(e,a=!1){return Ye(e)||Oe(e)||!Pe(e)||(Le(e)>1&&(e.set=e.add=e.clear=e.delete=Je),Object.freeze(e),a&&Object.entries(e).forEach(([e,a])=>Ge(a,!0))),e}function Je(){_e(2)}function Ye(e){return Object.isFrozen(e)}var Ke,Xe={};function Ze(e){const a=Xe[e];return a||_e(0,e),a}function ea(){return Ke}function aa(e,a){a&&(Ze("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=a)}function ia(e){ta(e),e.drafts_.forEach(sa),e.drafts_=null}function ta(e){e===Ke&&(Ke=e.parent_)}function ra(e){return Ke={drafts_:[],parent_:Ke,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function sa(e){const a=e[Te];0===a.type_||1===a.type_?a.revoke_():a.revoked_=!0}function oa(e,a){a.unfinalizedDrafts_=a.drafts_.length;const i=a.drafts_[0];return void 0!==e&&e!==i?(i[Te].modified_&&(ia(a),_e(4)),Pe(e)&&(e=na(a,e),a.parent_||ca(a,e)),a.patches_&&Ze("Patches").generateReplacementPatches_(i[Te].base_,e,a.patches_,a.inversePatches_)):e=na(a,i,[]),ia(a),a.patches_&&a.patchListener_(a.patches_,a.inversePatches_),e!==Re?e:void 0}function na(e,a,i){if(Ye(a))return a;const t=a[Te];if(!t)return qe(a,(r,s)=>la(e,t,a,r,s,i)),a;if(t.scope_!==e)return a;if(!t.modified_)return ca(e,t.base_,!0),t.base_;if(!t.finalized_){t.finalized_=!0,t.scope_.unfinalizedDrafts_--;const a=t.copy_;let r=a,s=!1;3===t.type_&&(r=new Set(a),a.clear(),s=!0),qe(r,(r,o)=>la(e,t,a,r,o,i,s)),ca(e,a,!1),i&&e.patches_&&Ze("Patches").generatePatches_(t,i,e.patches_,e.inversePatches_)}return t.copy_}function la(e,a,i,t,r,s,o){if(r===i&&_e(5),Oe(r)){const o=na(e,r,s&&a&&3!==a.type_&&!$e(a.assigned_,t)?s.concat(t):void 0);if(Fe(i,t,o),!Oe(o))return;e.canAutoFreeze_=!1}else o&&i.add(r);if(Pe(r)&&!Ye(r)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;na(e,r),a&&a.scope_.parent_||"symbol"==typeof t||!Object.prototype.propertyIsEnumerable.call(i,t)||ca(e,r)}}function ca(e,a,i=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&Ge(a,i)}var ua={get(e,a){if(a===Te)return e;const i=We(e);if(!$e(i,a))return function(e,a,i){var t;const r=pa(a,i);return r?"value"in r?r.value:null==(t=r.get)?void 0:t.call(e.draft_):void 0}(e,i,a);const t=i[a];return e.finalized_||!Pe(t)?t:t===ma(e.base_,a)?(fa(e),e.copy_[a]=ga(t,e)):t},has:(e,a)=>a in We(e),ownKeys:e=>Reflect.ownKeys(We(e)),set(e,a,i){const t=pa(We(e),a);if(null==t?void 0:t.set)return t.set.call(e.draft_,i),!0;if(!e.modified_){const t=ma(We(e),a),o=null==t?void 0:t[Te];if(o&&o.base_===i)return e.copy_[a]=i,e.assigned_[a]=!1,!0;if(((r=i)===(s=t)?0!==r||1/r==1/s:r!=r&&s!=s)&&(void 0!==i||$e(e.base_,a)))return!0;fa(e),ba(e)}var r,s;return e.copy_[a]===i&&(void 0!==i||a in e.copy_)||Number.isNaN(i)&&Number.isNaN(e.copy_[a])||(e.copy_[a]=i,e.assigned_[a]=!0),!0},deleteProperty:(e,a)=>(void 0!==ma(e.base_,a)||a in e.base_?(e.assigned_[a]=!1,fa(e),ba(e)):delete e.assigned_[a],e.copy_&&delete e.copy_[a],!0),getOwnPropertyDescriptor(e,a){const i=We(e),t=Reflect.getOwnPropertyDescriptor(i,a);return t?{writable:!0,configurable:1!==e.type_||"length"!==a,enumerable:t.enumerable,value:i[a]}:t},defineProperty(){_e(11)},getPrototypeOf:e=>ze(e.base_),setPrototypeOf(){_e(12)}},da={};function ma(e,a){const i=e[Te];return(i?We(i):e)[a]}function pa(e,a){if(!(a in e))return;let i=ze(e);for(;i;){const e=Object.getOwnPropertyDescriptor(i,a);if(e)return e;i=ze(i)}}function ba(e){e.modified_||(e.modified_=!0,e.parent_&&ba(e.parent_))}function fa(e){e.copy_||(e.copy_=He(e.base_,e.scope_.immer_.useStrictShallowCopy_))}qe(ua,(e,a)=>{da[e]=function(){return arguments[0]=arguments[0][0],a.apply(this,arguments)}}),da.deleteProperty=function(e,a){return isNaN(parseInt(a))&&_e(13),da.set.call(this,e,a,void 0)},da.set=function(e,a,i){return"length"!==a&&isNaN(parseInt(a))&&_e(14),ua.set.call(this,e[0],a,i,e[0])};function ga(e,a){const i=Ue(e)?Ze("MapSet").proxyMap_(e,a):Qe(e)?Ze("MapSet").proxySet_(e,a):function(e,a){const i=Array.isArray(e),t={type_:i?1:0,scope_:a?a.scope_:ea(),modified_:!1,finalized_:!1,assigned_:{},parent_:a,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let r=t,s=ua;i&&(r=[t],s=da);const{revoke:o,proxy:n}=Proxy.revocable(r,s);return t.draft_=n,t.revoke_=o,n}(e,a);return(a?a.scope_:ea()).drafts_.push(i),i}function Na(e){if(!Pe(e)||Ye(e))return e;const a=e[Te];let i;if(a){if(!a.modified_)return a.base_;a.finalized_=!0,i=He(e,a.scope_.immer_.useStrictShallowCopy_)}else i=He(e,!0);return qe(i,(e,a)=>{Fe(i,e,Na(a))}),a&&(a.finalized_=!1),i}var xa=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,a,i)=>{if("function"==typeof e&&"function"!=typeof a){const i=a;a=e;const t=this;return function(e=i,...r){return t.produce(e,e=>a.call(this,e,...r))}}let t;if("function"!=typeof a&&_e(6),void 0!==i&&"function"!=typeof i&&_e(7),Pe(e)){const r=ra(this),s=ga(e,void 0);let o=!0;try{t=a(s),o=!1}finally{o?ia(r):ta(r)}return aa(r,i),oa(t,r)}if(!e||"object"!=typeof e){if(t=a(e),void 0===t&&(t=e),t===Re&&(t=void 0),this.autoFreeze_&&Ge(t,!0),i){const a=[],r=[];Ze("Patches").generateReplacementPatches_(e,t,a,r),i(a,r)}return t}_e(1,e)},this.produceWithPatches=(e,a)=>{if("function"==typeof e)return(a,...i)=>this.produceWithPatches(a,a=>e(a,...i));let i,t;return[this.produce(e,a,(e,a)=>{i=e,t=a}),i,t]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){Pe(e)||_e(8),Oe(e)&&(e=function(e){Oe(e)||_e(10,e);return Na(e)}(e));const a=ra(this),i=ga(e,void 0);return i[Te].isManual_=!0,ta(a),i}finishDraft(e,a){const i=e&&e[Te];i&&i.isManual_||_e(9);const{scope_:t}=i;return aa(t,a),oa(void 0,t)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,a){let i;for(i=a.length-1;i>=0;i--){const t=a[i];if(0===t.path.length&&"replace"===t.op){e=t.value;break}}i>-1&&(a=a.slice(i+1));const t=Ze("Patches").applyPatches_;return Oe(e)?t(e,a):this.produce(e,e=>t(e,a))}},ha=xa.produce;xa.produceWithPatches.bind(xa);xa.setAutoFreeze.bind(xa),xa.setUseStrictShallowCopy.bind(xa);xa.applyPatches.bind(xa);xa.createDraft.bind(xa),xa.finishDraft.bind(xa);var va=(e,a,i)=>{if(1===a.length&&a[0]===i){let a=!1;try{const i={};e(i)===i&&(a=!0)}catch(Ls){}if(a){let e;try{throw new Error}catch(Ls){({stack:e}=Ls)}}}},ja=(e,a,i)=>{const{memoize:t,memoizeOptions:r}=a,{inputSelectorResults:s,inputSelectorResultsCopy:o}=e,n=t(()=>({}),...r);if(!(n.apply(null,s)===n.apply(null,o))){let e;try{throw new Error}catch(Ls){({stack:e}=Ls)}}},Va={inputStabilityCheck:"once",identityFunctionCheck:"once"};var ya=e=>Array.isArray(e)?e:[e];function Ea(e){const a=Array.isArray(e[0])?e[0]:e;return function(e,a="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){const i=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw new TypeError(`${a}[${i}]`)}}(a,"createSelector expects all input-selectors to be functions, but received the following types: "),a}function Ca(e,a){const i=[],{length:t}=e;for(let r=0;r<t;r++)i.push(e[r].apply(null,a));return i}var Da="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function Ba(){return{s:0,v:void 0,o:null,p:null}}function wa(e,a={}){let i={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:t}=a;let r,s=0;function o(){var a,o;let n=i;const{length:l}=arguments;for(let e=0,i=l;e<i;e++){const a=arguments[e];if("function"==typeof a||"object"==typeof a&&null!==a){let e=n.o;null===e&&(n.o=e=new WeakMap);const i=e.get(a);void 0===i?(n=Ba(),e.set(a,n)):n=i}else{let e=n.p;null===e&&(n.p=e=new Map);const i=e.get(a);void 0===i?(n=Ba(),e.set(a,n)):n=i}}const c=n;let u;if(1===n.s)u=n.v;else if(u=e.apply(null,arguments),s++,t){const e=null!=(o=null==(a=null==r?void 0:r.deref)?void 0:a.call(r))?o:r;null!=e&&t(e,u)&&(u=e,0!==s&&s--);r="object"==typeof u&&null!==u||"function"==typeof u?new Da(u):u}return c.s=1,c.v=u,u}return o.clearCache=()=>{i={s:0,v:void 0,o:null,p:null},o.resetResultsCount()},o.resultsCount=()=>s,o.resetResultsCount=()=>{s=0},o}function Aa(e,...a){const i="function"==typeof e?{memoize:e,memoizeOptions:a}:e,t=(...e)=>{let a,t=0,r=0,s={},o=e.pop();"object"==typeof o&&(s=o,o=e.pop()),function(e,a="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(a)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);const l=n(n({},i),s),{memoize:c,memoizeOptions:u=[],argsMemoize:d=wa,argsMemoizeOptions:m=[],devModeChecks:p={}}=l,b=ya(u),f=ya(m),g=Ea(e),N=c(function(){return t++,o.apply(null,arguments)},...b);let x=!0;const h=d(function(){r++;const e=Ca(g,arguments);a=N.apply(null,e);{const{identityFunctionCheck:i,inputStabilityCheck:t}=((e,a)=>{const{identityFunctionCheck:i,inputStabilityCheck:t}=n(n({},Va),a);return{identityFunctionCheck:{shouldRun:"always"===i||"once"===i&&e,run:va},inputStabilityCheck:{shouldRun:"always"===t||"once"===t&&e,run:ja}}})(x,p);if(i.shouldRun&&i.run(o,e,a),t.shouldRun){const a=Ca(g,arguments);t.run({inputSelectorResults:e,inputSelectorResultsCopy:a},{memoize:c,memoizeOptions:b},arguments)}x&&(x=!1)}return a},...f);return Object.assign(h,{resultFunc:o,memoizedResultFunc:N,dependencies:g,dependencyRecomputations:()=>r,resetDependencyRecomputations:()=>{r=0},lastResult:()=>a,recomputations:()=>t,resetRecomputations:()=>{t=0},memoize:c,argsMemoize:d})};return Object.assign(t,{withTypes:()=>t}),t}var Ra=Aa(wa),Sa=Object.assign((e,a=Ra)=>{!function(e,a="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(a)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const i=Object.keys(e);return a(i.map(a=>e[a]),(...e)=>e.reduce((e,a,t)=>(e[i[t]]=a,e),{}))},{withTypes:()=>Sa});function Ta(e){return({dispatch:a,getState:i})=>t=>r=>"function"==typeof r?r(a,i,e):t(r)}var Ia=Ta(),_a=Ta,za="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?we:we.apply(null,arguments)},Oa=e=>e&&"function"==typeof e.match;function Pa(e,a){function i(...i){if(a){let t=a(...i);if(!t)throw new Error("prepareAction did not return an object");return n(n({type:e,payload:t.payload},"meta"in t&&{meta:t.meta}),"error"in t&&{error:t.error})}return{type:e,payload:i[0]}}return i.toString=()=>`${e}`,i.type=e,i.match=a=>Ae(a)&&a.type===e,i}function Ma(e){return"function"==typeof e&&"type"in e&&Oa(e)}function ka(e,a){let i=0;return{measureTime(e){const a=Date.now();try{return e()}finally{const e=Date.now();i+=e-a}},warnIfExceeded(){}}}var qa=class e extends Array{constructor(...a){super(...a),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...a){return 1===a.length&&Array.isArray(a[0])?new e(...a[0].concat(this)):new e(...a.concat(this))}};function La(e){return Pe(e)?ha(e,()=>{}):e}function $a(e,a,i){return e.has(a)?e.get(a):e.set(a,i(a)).get(a)}function Fa(e){return"object"!=typeof e||null==e||Object.isFrozen(e)}function Ua(e,a,i){const t=Qa(e,a,i);return{detectMutations:()=>Wa(e,a,t,i)}}function Qa(e,a=[],i,t="",r=new Set){const s={value:i};if(!e(i)&&!r.has(i)){r.add(i),s.children={};for(const r in i){const o=t?t+"."+r:r;a.length&&-1!==a.indexOf(o)||(s.children[r]=Qa(e,a,i[r],o))}}return s}function Wa(e,a=[],i,t,r=!1,s=""){const o=i?i.value:void 0,n=o===t;if(r&&!n&&!Number.isNaN(t))return{wasMutated:!0,path:s};if(e(o)||e(t))return{wasMutated:!1};const l={};for(let u in i.children)l[u]=!0;for(let u in t)l[u]=!0;const c=a.length>0;for(let u in l){const r=s?s+"."+u:u;if(c){if(a.some(e=>e instanceof RegExp?e.test(r):r===e))continue}const o=Wa(e,a,i.children[u],t[u],n,r);if(o.wasMutated)return o}return{wasMutated:!1}}function Ha(e){const a=typeof e;return null==e||"string"===a||"boolean"===a||"number"===a||Array.isArray(e)||Ve(e)}function Ga(e,a="",i=Ha,t,r=[],s){let o;if(!i(e))return{keyPath:a||"<root>",value:e};if("object"!=typeof e||null===e)return!1;if(null==s?void 0:s.has(e))return!1;const n=null!=t?t(e):Object.entries(e),l=r.length>0;for(const[c,u]of n){const e=a?a+"."+c:c;if(l){if(r.some(a=>a instanceof RegExp?a.test(e):e===a))continue}if(!i(u))return{keyPath:e,value:u};if("object"==typeof u&&(o=Ga(u,e,i,t,r,s),o))return o}return s&&Ja(e)&&s.add(e),!1}function Ja(e){if(!Object.isFrozen(e))return!1;for(const a of Object.values(e))if("object"==typeof a&&null!==a&&!Ja(a))return!1;return!0}function Ya(e){return"boolean"==typeof e}var Ka=()=>function(e){const{thunk:a=!0,immutableCheck:i=!0,serializableCheck:t=!0,actionCreatorCheck:r=!0}=null!=e?e:{};let s=new qa;if(a&&(Ya(a)?s.push(Ia):s.push(_a(a.extraArgument))),i){let e={};Ya(i)||(e=i),s.unshift(function(e={}){{let a=function(e,a,t,r){return JSON.stringify(e,i(a,r),t)},i=function(e,a){let i=[],t=[];return a||(a=function(e,a){return i[0]===a?"[Circular ~]":"[Circular ~."+t.slice(0,i.indexOf(a)).join(".")+"]"}),function(r,s){if(i.length>0){var o=i.indexOf(this);~o?i.splice(o+1):i.push(this),~o?t.splice(o,1/0,r):t.push(r),~i.indexOf(s)&&(s=a.call(this,r,s))}else i.push(s);return null==e?s:e.call(this,r,s)}},{isImmutable:t=Fa,ignoredPaths:r,warnAfter:s=32}=e;const o=Ua.bind(null,t,r);return({getState:e})=>{let i,t=e(),r=o(t);return s=>n=>{const l=ka();l.measureTime(()=>{if(t=e(),i=r.detectMutations(),r=o(t),i.wasMutated)throw new Error(`A state mutation was detected between dispatches, in the path '${i.path||""}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`)});const c=s(n);return l.measureTime(()=>{if(t=e(),i=r.detectMutations(),r=o(t),i.wasMutated)throw new Error(`A state mutation was detected inside a dispatch, in the path: ${i.path||""}. Take a look at the reducer(s) handling the action ${a(n)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`)}),l.warnIfExceeded(),c}}}}(e))}if(t){let e={};Ya(t)||(e=t),s.push(function(e={}){{const{isSerializable:a=Ha,getEntries:i,ignoredActions:t=[],ignoredActionPaths:r=["meta.arg","meta.baseQueryMeta"],ignoredPaths:s=[],warnAfter:o=32,ignoreState:n=!1,ignoreActions:l=!1,disableCache:c=!1}=e,u=!c&&WeakSet?new WeakSet:void 0;return e=>o=>c=>{if(!Ae(c))return o(c);const d=o(c),m=ka();return l||t.length&&-1!==t.indexOf(c.type)||m.measureTime(()=>{const e=Ga(c,"",a,i,r,u);if(e){const{keyPath:a,value:i}=e}}),n||(m.measureTime(()=>{const t=Ga(e.getState(),"",a,i,s,u);if(t){const{keyPath:e,value:a}=t}}),m.warnIfExceeded()),d}}}(e))}if(r){let e={};Ya(r)||(e=r),s.unshift(function(e={}){const{isActionCreator:a=Ma}=e;return()=>e=>i=>(a(i),e(i))}(e))}return s},Xa="RTK_autoBatch",Za=e=>a=>{setTimeout(a,e)},ei=e=>function(a){const{autoBatch:i=!0}=null!=a?a:{};let t=new qa(e);return i&&t.push(((e={type:"raf"})=>a=>(...i)=>{const t=a(...i);let r=!0,s=!1,o=!1;const n=new Set,l="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Za(10):"callback"===e.type?e.queueNotification:Za(e.timeout),c=()=>{o=!1,s&&(s=!1,n.forEach(e=>e()))};return Object.assign({},t,{subscribe(e){const a=t.subscribe(()=>r&&e());return n.add(e),()=>{a(),n.delete(e)}},dispatch(e){var a;try{return r=!(null==(a=null==e?void 0:e.meta)?void 0:a[Xa]),s=!r,s&&(o||(o=!0,l(c))),t.dispatch(e)}finally{r=!0}}})})("object"==typeof i?i:void 0)),t};function ai(e){const a={},i=[];let t;const r={addCase(e,s){if(i.length>0)throw new Error("`builder.addCase` should only be called before calling `builder.addMatcher`");if(t)throw new Error("`builder.addCase` should only be called before calling `builder.addDefaultCase`");const o="string"==typeof e?e:e.type;if(!o)throw new Error("`builder.addCase` cannot be called with an empty action type");if(o in a)throw new Error(`\`builder.addCase\` cannot be called with two reducers for the same action type '${o}'`);return a[o]=s,r},addMatcher(e,a){if(t)throw new Error("`builder.addMatcher` should only be called before calling `builder.addDefaultCase`");return i.push({matcher:e,reducer:a}),r},addDefaultCase(e){if(t)throw new Error("`builder.addDefaultCase` can only be called once");return t=e,r}};return e(r),[a,i,t]}var ii=(e,a)=>Oa(e)?e.match(a):e(a);function ti(...e){return a=>e.some(e=>ii(e,a))}var ri=(e=21)=>{let a="",i=e;for(;i--;)a+="ModuleSymbhasOwnPr-**********ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return a},si=["name","message","stack","code"],oi=class{constructor(e,a){u(this,"_type"),this.payload=e,this.meta=a}},ni=class{constructor(e,a){u(this,"_type"),this.payload=e,this.meta=a}},li=e=>{if("object"==typeof e&&null!==e){const a={};for(const i of si)"string"==typeof e[i]&&(a[i]=e[i]);return a}return{message:String(e)}},ci="External signal was aborted",ui=(()=>{function e(e,a,i){const t=Pa(e+"/fulfilled",(e,a,i,t)=>({payload:e,meta:l(n({},t||{}),{arg:i,requestId:a,requestStatus:"fulfilled"})})),r=Pa(e+"/pending",(e,a,i)=>({payload:void 0,meta:l(n({},i||{}),{arg:a,requestId:e,requestStatus:"pending"})})),s=Pa(e+"/rejected",(e,a,t,r,s)=>({payload:r,error:(i&&i.serializeError||li)(e||"Rejected"),meta:l(n({},s||{}),{arg:t,requestId:a,rejectedWithValue:!!r,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}));return Object.assign(function(e,{signal:o}={}){return(n,l,c)=>{const u=(null==i?void 0:i.idGenerator)?i.idGenerator(e):ri(),m=new AbortController;let p,b;function f(e){b=e,m.abort()}o&&(o.aborted?f(ci):o.addEventListener("abort",()=>f(ci),{once:!0}));const g=function(){return d(this,null,function*(){var o,d;let g;try{let s=null==(o=null==i?void 0:i.condition)?void 0:o.call(i,e,{getState:l,extra:c});if(null!==(N=s)&&"object"==typeof N&&"function"==typeof N.then&&(s=yield s),!1===s||m.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const x=new Promise((e,a)=>{p=()=>{a({name:"AbortError",message:b||"Aborted"})},m.signal.addEventListener("abort",p)});n(r(u,e,null==(d=null==i?void 0:i.getPendingMeta)?void 0:d.call(i,{requestId:u,arg:e},{getState:l,extra:c}))),g=yield Promise.race([x,Promise.resolve(a(e,{dispatch:n,getState:l,extra:c,requestId:u,signal:m.signal,abort:f,rejectWithValue:(e,a)=>new oi(e,a),fulfillWithValue:(e,a)=>new ni(e,a)})).then(a=>{if(a instanceof oi)throw a;return a instanceof ni?t(a.payload,u,e,a.meta):t(a,u,e)})])}catch(x){g=x instanceof oi?s(null,u,e,x.payload,x.meta):s(x,u,e)}finally{p&&m.signal.removeEventListener("abort",p)}var N;return i&&!i.dispatchConditionRejection&&s.match(g)&&g.meta.condition||n(g),g})}();return Object.assign(g,{abort:f,requestId:u,arg:e,unwrap:()=>g.then(di)})}},{pending:r,rejected:s,fulfilled:t,settled:ti(s,t),typePrefix:e})}return e.withTypes=()=>e,e})();function di(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var mi=Symbol.for("rtk-slice-createasyncthunk");function pi(e,a){return`${e}/${a}`}function bi({creators:e}={}){var a;const i=null==(a=null==e?void 0:e.asyncThunk)?void 0:a[mi];return function(e){const{name:a,reducerPath:t=a}=e;if(!a)throw new Error("`name` is a required option for createSlice");"undefined"!=typeof process&&e.initialState;const r=("function"==typeof e.reducers?e.reducers(function(){function e(e,a){return n({_reducerDefinitionType:"asyncThunk",payloadCreator:e},a)}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...a)=>e(...a)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,a)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:a}),asyncThunk:e}}()):e.reducers)||{},s=Object.keys(r),o={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},u={addCase(e,a){const i="string"==typeof e?e:e.type;if(!i)throw new Error("`context.addCase` cannot be called with an empty action type");if(i in o.sliceCaseReducersByType)throw new Error("`context.addCase` cannot be called with two reducers for the same action type: "+i);return o.sliceCaseReducersByType[i]=a,u},addMatcher:(e,a)=>(o.sliceMatchers.push({matcher:e,reducer:a}),u),exposeAction:(e,a)=>(o.actionCreators[e]=a,u),exposeCaseReducer:(e,a)=>(o.sliceCaseReducersByName[e]=a,u)};function d(){if("object"==typeof e.extraReducers)throw new Error("The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice");const[a={},i=[],t]="function"==typeof e.extraReducers?ai(e.extraReducers):[e.extraReducers],r=n(n({},a),o.sliceCaseReducersByType);return function(e,a){if("object"==typeof a)throw new Error("The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer");let i,[t,r,s]=ai(a);if("function"==typeof e)i=()=>La(e());else{const a=La(e);i=()=>a}function o(e=i(),a){let o=[t[a.type],...r.filter(({matcher:e})=>e(a)).map(({reducer:e})=>e)];return 0===o.filter(e=>!!e).length&&(o=[s]),o.reduce((e,i)=>{if(i){if(Oe(e)){const t=i(e,a);return void 0===t?e:t}if(Pe(e))return ha(e,e=>i(e,a));{const t=i(e,a);if(void 0===t){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return t}}return e},e)}return o.getInitialState=i,o}(e.initialState,e=>{for(let a in r)e.addCase(a,r[a]);for(let a of o.sliceMatchers)e.addMatcher(a.matcher,a.reducer);for(let a of i)e.addMatcher(a.matcher,a.reducer);t&&e.addDefaultCase(t)})}s.forEach(t=>{const s=r[t],o={reducerName:t,type:pi(a,t),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(s)?function({type:e,reducerName:a,createNotation:i},t,r){let s,o;if("reducer"in t){if(i&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(t))throw new Error("Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.");s=t.reducer,o=t.prepare}else s=t;r.addCase(e,s).exposeCaseReducer(a,s).exposeAction(a,o?Pa(e,o):Pa(e))}(o,s,u):function({type:e,reducerName:a},i,t,r){if(!r)throw new Error("Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.");const{payloadCreator:s,fulfilled:o,pending:n,rejected:l,settled:c,options:u}=i,d=r(e,s,u);t.exposeAction(a,d),o&&t.addCase(d.fulfilled,o);n&&t.addCase(d.pending,n);l&&t.addCase(d.rejected,l);c&&t.addMatcher(d.settled,c);t.exposeCaseReducer(a,{fulfilled:o||Ni,pending:n||Ni,rejected:l||Ni,settled:c||Ni})}(o,s,u,i)});const m=e=>e,p=new Map,b=new WeakMap;let f;function g(e,a){return f||(f=d()),f(e,a)}function N(){return f||(f=d()),f.getInitialState()}function x(a,i=!1){function t(e){let r=e[a];if(void 0===r){if(!i)throw new Error("selectSlice returned undefined for an uninjected slice reducer");r=$a(b,t,N)}return r}function r(a=m){const t=$a(p,i,()=>new WeakMap);return $a(t,a,()=>{var t;const r={};for(const[s,o]of Object.entries(null!=(t=e.selectors)?t:{}))r[s]=fi(o,a,()=>$a(b,a,N),i);return r})}return{reducerPath:a,getSelectors:r,get selectors(){return r(t)},selectSlice:t}}const h=l(n({name:a,reducer:g,actions:o.actionCreators,caseReducers:o.sliceCaseReducersByName,getInitialState:N},x(t)),{injectInto(e,a={}){var i=a,{reducerPath:r}=i,s=c(i,["reducerPath"]);const o=null!=r?r:t;return e.inject({reducerPath:o,reducer:g},s),n(n({},h),x(o,!0))}});return h}}function fi(e,a,i,t){function r(r,...s){let o=a(r);if(void 0===o){if(!t)throw new Error("selectState returned undefined for an uninjected slice reducer");o=i()}return e(o,...s)}return r.unwrapped=e,r}var gi=bi();function Ni(){}Error;var xi=Pa("__rtkq/focused"),hi=Pa("__rtkq/unfocused"),vi=Pa("__rtkq/online"),ji=Pa("__rtkq/offline"),Vi=!1;Symbol("forceQueryFn");WeakMap;new Error("Promise never resolved before cacheEntryRemoved.");const yi="validation",Ei="authentication",Ci="authorization",Di="network",Bi="server",wi="client",Ai="unknown",Ri="low",Si="medium",Ti="high",Ii="critical";class _i extends Error{constructor({message:e,type:a=Ai,severity:i=Si,code:t=null,details:r=null,userMessage:s=null,shouldLog:o=!0,shouldNotify:n=!0,retryable:l=!1,originalError:c=null}){super(e),this.name="AppError",this.type=a,this.severity=i,this.code=t,this.details=r,this.userMessage=s||this.generateUserMessage(),this.shouldLog=o,this.shouldNotify=n,this.retryable=l,this.originalError=c,this.timestamp=(new Date).toISOString(),this.stack=(null==c?void 0:c.stack)||this.stack}generateUserMessage(){const e={[yi]:"Por favor, verifique los datos ingresados.",[Ei]:"Error de autenticación. Verifique sus credenciales.",[Ci]:"No tiene permisos para realizar esta acción.",[Di]:"Error de conexión. Verifique su conexión a internet.",[Bi]:"Error del servidor. Intente nuevamente en unos momentos.",[wi]:"Error en la aplicación. Recargue la página.",[Ai]:"Ha ocurrido un error inesperado."};return e[this.type]||e[Ai]}toJSON(){return{name:this.name,message:this.message,type:this.type,severity:this.severity,code:this.code,details:this.details,userMessage:this.userMessage,timestamp:this.timestamp,stack:this.stack}}}const zi=new class{constructor(){this.errorLog=[],this.maxLogSize=100,this.retryAttempts=new Map,this.maxRetries=3}handle(e,a={}){const i=this.normalizeError(e,a);return i.shouldLog&&this.logError(i,a),i.shouldNotify&&this.notifyUser(i),i}normalizeError(e,a={}){return e instanceof _i?e:(null==e?void 0:e.response)?this.handleHttpError(e,a):(null==e?void 0:e.code)?this.handleServiceError(e,a):e instanceof TypeError&&e.message.includes("fetch")?new _i({message:e.message,type:Di,severity:Ti,userMessage:"Error de conexión. Verifique su conexión a internet.",retryable:!0,originalError:e}):new _i({message:e.message||"Error desconocido",type:Ai,severity:Si,originalError:e})}handleHttpError(e,a={}){var i,t;const r=null==(i=e.response)?void 0:i.status,s=null==(t=e.response)?void 0:t.data,o={400:{type:yi,severity:Ri,userMessage:"Datos inválidos. Verifique la información ingresada."},401:{type:Ei,severity:Si,userMessage:"Sesión expirada. Por favor, inicie sesión nuevamente."},403:{type:Ci,severity:Si,userMessage:"No tiene permisos para realizar esta acción."},404:{type:wi,severity:Ri,userMessage:"Recurso no encontrado."},422:{type:yi,severity:Ri,userMessage:"Datos inválidos. Verifique la información ingresada."},429:{type:Bi,severity:Si,userMessage:"Demasiadas solicitudes. Intente nuevamente en unos momentos.",retryable:!0},500:{type:Bi,severity:Ti,userMessage:"Error del servidor. Intente nuevamente en unos momentos.",retryable:!0},502:{type:Bi,severity:Ti,userMessage:"Servicio temporalmente no disponible.",retryable:!0},503:{type:Bi,severity:Ti,userMessage:"Servicio temporalmente no disponible.",retryable:!0}}[r]||{type:Bi,severity:Si,userMessage:"Error del servidor. Intente nuevamente."};return new _i(n({message:(null==s?void 0:s.message)||e.message||`HTTP ${r} Error`,code:r,details:s,originalError:e},o))}handleServiceError(e,a={}){const i={PGRST116:{type:yi,userMessage:"Datos inválidos o faltantes."},PGRST301:{type:Ci,userMessage:"No tiene permisos para acceder a este recurso."},23505:{type:yi,userMessage:"Ya existe un registro con estos datos."},23503:{type:yi,userMessage:"Referencia inválida en los datos."},invalid_credentials:{type:Ei,userMessage:"Credenciales inválidas."},email_not_confirmed:{type:Ei,userMessage:"Email no confirmado. Verifique su correo electrónico."},signup_disabled:{type:Ci,userMessage:"Registro de usuarios deshabilitado."}}[e.code]||{type:Bi,userMessage:"Error del servicio. Intente nuevamente."};return new _i(n({message:e.message||"Service error",code:e.code,details:e.details,originalError:e},i))}logError(e,a={}){const i=l(n({},e.toJSON()),{context:a,url:window.location.href,userAgent:navigator.userAgent,timestamp:(new Date).toISOString()});this.errorLog.unshift(i),this.errorLog.length>this.maxLogSize&&this.errorLog.pop(),e.severity}notifyUser(e){const a={position:"top-right",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0};switch(e.severity){case Ii:case Ti:J.error(e.userMessage,l(n({},a),{autoClose:8e3}));break;case Si:J.warning(e.userMessage,a);break;case Ri:J.info(e.userMessage,l(n({},a),{autoClose:3e3}));break;default:J(e.userMessage,a)}}showSuccess(e,a={}){J.success(e,n({position:"top-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}showInfo(e,a={}){J.info(e,n({position:"top-right",autoClose:4e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0},a))}retry(e,a){return d(this,arguments,function*(e,a,i=this.maxRetries){const t=this.retryAttempts.get(a)||0;if(t>=i)throw this.retryAttempts.delete(a),new _i({message:"Maximum retry attempts exceeded",type:wi,severity:Ti,userMessage:"Operación fallida después de varios intentos."});try{const i=yield e();return this.retryAttempts.delete(a),i}catch(r){this.retryAttempts.set(a,t+1);const e=Math.min(1e3*Math.pow(2,t),1e4);throw yield new Promise(a=>setTimeout(a,e)),r}})}sendToMonitoring(e){return d(this,null,function*(){})}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[],this.retryAttempts.clear()}createErrorBoundaryHandler(){return(e,a)=>{const i=new _i({message:e.message,type:wi,severity:Ti,details:a,userMessage:"Ha ocurrido un error en la aplicación. La página se recargará automáticamente.",originalError:e});this.handle(i,{errorBoundary:!0}),setTimeout(()=>{window.location.reload()},3e3)}}},Oi=(e,a)=>zi.handle(e,a),Pi="not_started",Mi="in_progress",ki="paused",qi="completed",Li="expired",$i=ui("test/startTestSession",(e,a)=>d(null,[e,a],function*({testId:e,userId:a},{rejectWithValue:i}){try{const i=yield fetch(`/api/tests/${e}/start`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:a})});if(!i.ok)throw new Error("Failed to start test");return yield i.json()}catch(t){return improvedErrorHandler.handleError(t,{context:"startTestSession",userId:a,testId:e}),i(t.message)}})),Fi=ui("test/submitAnswer",(e,a)=>d(null,[e,a],function*({sessionId:e,questionId:a,answer:i,timeSpent:t},{rejectWithValue:r}){try{const r=yield fetch(`/api/test-sessions/${e}/answers`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({questionId:a,answer:i,timeSpent:t,timestamp:Date.now()})});if(!r.ok)throw new Error("Failed to submit answer");return yield r.json()}catch(s){return Oi(s,{context:"submitAnswer",sessionId:e,questionId:a}),r(s.message)}})),Ui=ui("test/completeTestSession",(e,a)=>d(null,[e,a],function*({sessionId:e},{getState:a,rejectWithValue:i}){try{const i=a(),{answers:t,startTime:r}=i.test.currentSession,s=yield fetch(`/api/test-sessions/${e}/complete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({answers:t,completedAt:Date.now(),totalTime:Date.now()-r})});if(!s.ok)throw new Error("Failed to complete test");return yield s.json()}catch(t){return Oi(t,{context:"completeTestSession",sessionId:e}),i(t.message)}})),Qi=ui("test/fetchTestHistory",(e,a)=>d(null,[e,a],function*({userId:e,limit:a=10,offset:i=0},{rejectWithValue:t}){try{const t=yield fetch(`/api/users/${e}/test-history?limit=${a}&offset=${i}`);if(!t.ok)throw new Error("Failed to fetch test history");return yield t.json()}catch(r){return Oi(r,{context:"fetchTestHistory",userId:e}),t(r.message)}})),Wi=ui("test/fetchTestResults",(e,a)=>d(null,[e,a],function*({sessionId:e},{rejectWithValue:a}){try{const a=yield fetch(`/api/test-sessions/${e}/results`);if(!a.ok)throw new Error("Failed to fetch test results");return yield a.json()}catch(i){return Oi(i,{context:"fetchTestResults",sessionId:e}),a(i.message)}})),Hi={currentSession:{id:null,testId:null,userId:null,status:Pi,startTime:null,endTime:null,currentQuestionIndex:0,totalQuestions:0,answers:{},timeSpent:{},progress:0,allowedTime:null,timeRemaining:null,isPaused:!1,pauseStartTime:null,totalPauseTime:0},testConfig:{id:null,name:"",description:"",type:null,version:"1.0",timeLimit:null,questionsPerPage:1,allowBackNavigation:!1,allowPause:!0,randomizeQuestions:!1,randomizeAnswers:!1,showProgress:!0,showTimeRemaining:!0,autoSubmit:!0,passingScore:null},questions:[],currentQuestion:null,results:{sessionId:null,scores:{},percentile:null,interpretation:"",recommendations:[],detailedAnalysis:{},comparisonData:null,generatedAt:null},history:{tests:[],totalCount:0,currentPage:1,hasMore:!1},availableTests:[],loading:{starting:!1,submitting:!1,completing:!1,fetchingHistory:!1,fetchingResults:!1,fetchingTests:!1},errors:{start:null,submit:null,complete:null,history:null,results:null,general:null},ui:{showInstructions:!0,showConfirmation:!1,showResults:!1,highlightedAnswers:[],flaggedQuestions:[],reviewMode:!1,fullscreen:!1},statistics:{totalTestsTaken:0,averageScore:0,bestScore:0,lastTestDate:null,testsByType:{},monthlyProgress:[]},currentTest:null,testResults:null,answeredQuestions:{},timeRemaining:0,testStarted:!1,testCompleted:!1,error:null},Gi=gi({name:"test",initialState:Hi,reducers:{initializeSession:(e,a)=>{const{sessionId:i,testConfig:t,questions:r}=a.payload;e.currentSession=l(n({},Hi.currentSession),{id:i,testId:t.id,status:Pi,totalQuestions:r.length,allowedTime:t.timeLimit,timeRemaining:t.timeLimit}),e.testConfig=t,e.questions=r,e.currentQuestion=r[0]||null,e.currentTest=t,e.timeRemaining=t.timeLimit||0},startSession:e=>{e.currentSession.status=Mi,e.currentSession.startTime=Date.now(),e.ui.showInstructions=!1,e.testStarted=!0},pauseSession:e=>{e.testConfig.allowPause&&e.currentSession.status===Mi&&(e.currentSession.status=ki,e.currentSession.isPaused=!0,e.currentSession.pauseStartTime=Date.now())},resumeSession:e=>{e.currentSession.isPaused&&(e.currentSession.status=Mi,e.currentSession.isPaused=!1,e.currentSession.pauseStartTime&&(e.currentSession.totalPauseTime+=Date.now()-e.currentSession.pauseStartTime,e.currentSession.pauseStartTime=null))},goToQuestion:(e,a)=>{const i=a.payload;i>=0&&i<e.questions.length&&(e.currentSession.currentQuestionIndex=i,e.currentQuestion=e.questions[i],e.currentSession.progress=(i+1)/e.questions.length*100)},nextQuestion:e=>{const a=e.currentSession.currentQuestionIndex+1;a<e.questions.length&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)},previousQuestion:e=>{if(e.testConfig.allowBackNavigation){const a=e.currentSession.currentQuestionIndex-1;a>=0&&(e.currentSession.currentQuestionIndex=a,e.currentQuestion=e.questions[a],e.currentSession.progress=(a+1)/e.questions.length*100)}},setAnswer:(e,a)=>{const{questionId:i,answer:t,timeSpent:r}=a.payload;e.currentSession.answers[i]={value:t,timestamp:Date.now(),timeSpent:r||0},r&&(e.currentSession.timeSpent[i]=r),e.answeredQuestions[i]=t},clearAnswer:(e,a)=>{const i=a.payload;delete e.currentSession.answers[i],delete e.currentSession.timeSpent[i],delete e.answeredQuestions[i]},flagQuestion:(e,a)=>{const i=a.payload;e.ui.flaggedQuestions.includes(i)||e.ui.flaggedQuestions.push(i)},unflagQuestion:(e,a)=>{const i=a.payload;e.ui.flaggedQuestions=e.ui.flaggedQuestions.filter(e=>e!==i)},updateTimeRemaining:(e,a)=>{const i=a.payload;e.currentSession.timeRemaining=Math.max(0,i),e.timeRemaining=i,i<=0&&e.testConfig.autoSubmit&&(e.currentSession.status=Li)},toggleInstructions:e=>{e.ui.showInstructions=!e.ui.showInstructions},setShowConfirmation:(e,a)=>{e.ui.showConfirmation=a.payload},setReviewMode:(e,a)=>{e.ui.reviewMode=a.payload},toggleFullscreen:e=>{e.ui.fullscreen=!e.ui.fullscreen},highlightAnswers:(e,a)=>{e.ui.highlightedAnswers=a.payload},setResults:(e,a)=>{e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload},clearResults:e=>{e.results=Hi.results,e.ui.showResults=!1,e.testResults=null},updateStatistics:(e,a)=>{e.statistics=n(n({},e.statistics),a.payload)},setError:(e,a)=>{const{type:i,error:t}=a.payload;void 0!==e.errors[i]&&(e.errors[i]=t),e.error=t},clearError:(e,a)=>{const i=a.payload;void 0!==e.errors[i]&&(e.errors[i]=null),"general"===i&&(e.error=null)},clearAllErrors:e=>{e.errors=Hi.errors,e.error=null},setCurrentTest:(e,a)=>{var i;e.currentTest=a.payload,e.answeredQuestions={},e.testStarted=!1,e.testCompleted=!1,e.timeRemaining=(null==(i=a.payload)?void 0:i.duration)?60*a.payload.duration:0,a.payload&&(e.testConfig=l(n(n({},e.testConfig),a.payload),{timeLimit:a.payload.duration?60*a.payload.duration:null}),e.currentSession.timeRemaining=e.timeRemaining)},startTest:e=>{e.testStarted=!0,e.currentSession.status=Mi,e.currentSession.startTime=Date.now()},answerQuestion:(e,a)=>{const{questionId:i,answerId:t}=a.payload;e.answeredQuestions[i]=t,e.currentSession.answers[i]={value:t,timestamp:Date.now(),timeSpent:0}},completeTest:e=>{e.testCompleted=!0,e.currentSession.status=qi,e.currentSession.endTime=Date.now()},setTestResults:(e,a)=>{e.testResults=a.payload,e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0},setLoading:(e,a)=>{e.loading=a.payload,Object.keys(e.loading).forEach(i=>{e.loading[i]=a.payload})},resetSession:e=>{e.currentSession=Hi.currentSession,e.currentQuestion=null,e.questions=[],e.testConfig=Hi.testConfig,e.ui=Hi.ui,e.errors=Hi.errors},resetTestState:()=>Hi,resetTest:()=>Hi},extraReducers:e=>{e.addCase($i.pending,e=>{e.loading.starting=!0,e.errors.start=null,e.loading=!0}).addCase($i.fulfilled,(e,a)=>{e.loading.starting=!1,e.loading=!1;const{session:i,config:t,questions:r}=a.payload;e.currentSession=l(n(n({},e.currentSession),i),{status:Mi,startTime:Date.now()}),e.testConfig=t,e.questions=r,e.currentQuestion=r[0]||null,e.currentTest=t,e.testStarted=!0}).addCase($i.rejected,(e,a)=>{e.loading.starting=!1,e.loading=!1,e.errors.start=a.payload,e.error=a.payload}).addCase(Fi.pending,e=>{e.loading.submitting=!0,e.errors.submit=null}).addCase(Fi.fulfilled,(e,a)=>{e.loading.submitting=!1}).addCase(Fi.rejected,(e,a)=>{e.loading.submitting=!1,e.errors.submit=a.payload}).addCase(Ui.pending,e=>{e.loading.completing=!0,e.errors.complete=null,e.loading=!0}).addCase(Ui.fulfilled,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.currentSession.status=qi,e.currentSession.endTime=Date.now(),e.testCompleted=!0,a.payload.results&&(e.results=l(n(n({},e.results),a.payload.results),{sessionId:e.currentSession.id,generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload.results)}).addCase(Ui.rejected,(e,a)=>{e.loading.completing=!1,e.loading=!1,e.errors.complete=a.payload,e.error=a.payload}).addCase(Qi.pending,e=>{e.loading.fetchingHistory=!0,e.errors.history=null}).addCase(Qi.fulfilled,(e,a)=>{e.loading.fetchingHistory=!1;const{tests:i,totalCount:t,page:r,hasMore:s}=a.payload;1===r?e.history.tests=i:e.history.tests.push(...i),e.history.totalCount=t,e.history.currentPage=r,e.history.hasMore=s}).addCase(Qi.rejected,(e,a)=>{e.loading.fetchingHistory=!1,e.errors.history=a.payload}).addCase(Wi.pending,e=>{e.loading.fetchingResults=!0,e.errors.results=null}).addCase(Wi.fulfilled,(e,a)=>{e.loading.fetchingResults=!1,e.results=l(n(n({},e.results),a.payload),{generatedAt:Date.now()}),e.ui.showResults=!0,e.testResults=a.payload}).addCase(Wi.rejected,(e,a)=>{e.loading.fetchingResults=!1,e.errors.results=a.payload})}}),{initializeSession:Ji,startSession:Yi,pauseSession:Ki,resumeSession:Xi,goToQuestion:Zi,nextQuestion:et,previousQuestion:at,setAnswer:it,clearAnswer:tt,flagQuestion:rt,unflagQuestion:st,toggleInstructions:ot,setShowConfirmation:nt,setReviewMode:lt,toggleFullscreen:ct,highlightAnswers:ut,setResults:dt,clearResults:mt,updateStatistics:pt,clearError:bt,clearAllErrors:ft,resetSession:gt,resetTest:Nt,setCurrentTest:xt,startTest:ht,updateTimeRemaining:vt,answerQuestion:jt,completeTest:Vt,setTestResults:yt,setLoading:Et,setError:Ct,resetTestState:Dt}=Gi.actions,Bt=function(e){const a=Ka(),{reducer:i,middleware:t,devTools:r=!0,duplicateMiddlewareCheck:s=!0,preloadedState:o,enhancers:c}=e||{};let u,d;if("function"==typeof i)u=i;else{if(!Ve(i))throw new Error("`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers");u=Be(i)}if(t&&"function"!=typeof t)throw new Error("`middleware` field must be a callback");if("function"==typeof t){if(d=t(a),!Array.isArray(d))throw new Error("when using a middleware builder function, an array of middleware must be returned")}else d=a();if(d.some(e=>"function"!=typeof e))throw new Error("each middleware provided to configureStore must be a function");if(s){let e=new Set;d.forEach(a=>{if(e.has(a))throw new Error("Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.");e.add(a)})}let m=we;r&&(m=za(n({trace:!0},"object"==typeof r&&r)));const p=function(...e){return a=>(i,t)=>{const r=a(i,t);let s=()=>{throw new Error("Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.")};const o={getState:r.getState,dispatch:(e,...a)=>s(e,...a)},c=e.map(e=>e(o));return s=we(...c)(r.dispatch),l(n({},r),{dispatch:s})}}(...d),b=ei(p);if(c&&"function"!=typeof c)throw new Error("`enhancers` field must be a callback");let f="function"==typeof c?c(b):b();if(!Array.isArray(f))throw new Error("`enhancers` callback must return an array");if(f.some(e=>"function"!=typeof e))throw new Error("each enhancer provided to configureStore must be a function");return d.length&&f.includes(p),Ce(u,o,m(...f))}({reducer:{test:Gi.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}),devTools:!0});var wt;wt=Bt.dispatch,function(){const e=()=>wt(xi()),a=()=>wt(vi()),i=()=>wt(ji()),t=()=>{"visible"===window.document.visibilityState?e():wt(hi())};Vi||"undefined"!=typeof window&&window.addEventListener&&(window.addEventListener("visibilitychange",t,!1),window.addEventListener("focus",e,!1),window.addEventListener("online",a,!1),window.addEventListener("offline",i,!1),Vi=!0)}(),Bt.getState,Bt.dispatch;const At=({children:e})=>m.jsxDEV("div",{className:"page-content opacity-100 transition-opacity duration-200 ease-in-out",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/transitions/PageTransition.jsx",lineNumber:8,columnNumber:5},void 0),Rt=({className:e=""})=>m.jsxDEV("div",{className:`title-container ${e}`,children:[m.jsxDEV("div",{className:"inline-flex items-center",children:m.jsxDEV("h1",{className:"simple-title text-2xl font-bold text-blue-900",children:m.jsxDEV("span",{children:"BAT-7 Batería de Aptitudes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:13,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:12,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:10,columnNumber:7},void 0),m.jsxDEV("style",{jsx:"true",children:"\n        .title-container {\n          display: inline-block;\n        }\n        \n        .simple-title {\n          font-weight: 800;\n          letter-spacing: 1px;\n          color: #1d387a;\n        }\n        \n        /* Responsive */\n        @media (max-width: 768px) {\n          .simple-title {\n            font-size: 1.5rem;\n          }\n        }\n\n        @media (max-width: 480px) {\n          .simple-title {\n            font-size: 1.25rem;\n            text-align: center;\n            line-height: 1.2;\n          }\n        }\n\n        @media (max-width: 360px) {\n          .simple-title {\n            font-size: 1rem;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:17,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/AnimatedTitle.jsx",lineNumber:9,columnNumber:5},void 0),St=({isOpen:e,toggleSidebar:a,onLogout:i})=>{const t=L(),[r,s]=P.useState(()=>{const e=localStorage.getItem("sidebarFavorites");return e?JSON.parse(e):{dashboard:!1,home:!1,patients:!1,tests:!1,reports:!1,administration:!1,settings:!1,help:!1}});P.useEffect(()=>{localStorage.setItem("sidebarFavorites",JSON.stringify(r))},[r]);const o=(e,a)=>{a.preventDefault(),a.stopPropagation(),s(a=>l(n({},a),{[e]:!a[e]}))},c=e=>"/"===e?"/"===t.pathname:t.pathname===e||t.pathname.startsWith(e)&&(t.pathname.length===e.length||"/"===t.pathname[e.length]),u=[{title:"Navegación Principal",items:[{name:"Inicio",path:"/home",icon:"home",key:"home"},{name:"Pacientes",path:"/admin/patients",icon:"users",key:"patients"},{name:"Cuestionario",path:"/student/questionnaire",icon:"clipboard-list",key:"tests"},{name:"Resultados",path:"/admin/reports",icon:"chart-bar",key:"reports"}]},{title:"Administración",items:[{name:"Panel Admin",path:"/admin/administration",icon:"shield-alt",key:"administration"},{name:"Configuración",path:"/configuracion",icon:"cog",key:"settings"}]},{title:"Soporte",items:[{name:"Ayuda",path:"/help",icon:"question-circle",key:"help"}]}],d=u.flatMap(e=>e.items).filter(e=>r[e.key]);return m.jsxDEV("div",{className:"sidebar bg-[#121940] text-[#a4b1cd] fixed top-0 left-0 h-full z-50 transition-all duration-300 ease-in-out\n                     "+(e?"w-64":"w-[70px]"),children:[m.jsxDEV("div",{className:"sidebar-header p-5 flex justify-between items-center border-b border-opacity-10 border-white",children:[e&&m.jsxDEV("h1",{className:"sidebar-logo text-3xl font-bold text-white text-center flex-1",children:["Activatu",m.jsxDEV("span",{className:"text-[#ffda0a]",children:"mente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:95,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:94,columnNumber:11},void 0),m.jsxDEV("button",{onClick:a,className:"collapse-button text-[#a4b1cd] cursor-pointer hover:text-white",title:e?"Contraer menú":"Expandir menú","aria-label":e?"Contraer menú":"Expandir menú",children:m.jsxDEV("i",{className:"fas "+(e?"fa-chevron-left":"fa-chevron-right")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:104,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:98,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:92,columnNumber:7},void 0),d.length>0&&m.jsxDEV("div",{className:"sidebar-section py-2 border-b border-opacity-10 border-white",children:[e&&m.jsxDEV("h2",{className:"uppercase text-xs px-5 py-2 tracking-wider font-semibold text-gray-400",children:"FAVORITOS"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:112,columnNumber:13},void 0),m.jsxDEV("ul",{className:"menu-list",children:d.map(a=>m.jsxDEV("li",{className:"py-3 px-5 hover:bg-opacity-10 hover:bg-white transition-all duration-300 relative transform hover:translate-x-1\n                          "+(c(a.path)?"bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":""),children:m.jsxDEV("div",{className:"flex items-center justify-between w-full",children:[m.jsxDEV(k,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[m.jsxDEV("i",{className:`fas fa-${a.icon} ${e?"mr-3":""} w-5 text-center transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:128,columnNumber:21},void 0),e&&m.jsxDEV("span",{children:a.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:129,columnNumber:32},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:124,columnNumber:19},void 0),e&&m.jsxDEV("span",{className:"text-[#ffda0a] cursor-pointer hover:scale-110 transition-transform duration-200",onClick:e=>o(a.key,e),title:"Quitar de favoritos",children:m.jsxDEV("i",{className:"fas fa-star"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:137,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:132,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:123,columnNumber:17},void 0)},`fav-${a.key}`,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:118,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:116,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:110,columnNumber:9},void 0),m.jsxDEV("div",{className:"sidebar-content py-2 flex-1 overflow-y-auto",children:u.map((a,i)=>m.jsxDEV("div",{className:"mb-4",children:[e&&m.jsxDEV("div",{className:"px-5 py-2 mb-2",children:m.jsxDEV("h3",{className:"section-title text-xs font-semibold text-gray-400 uppercase tracking-wider",children:a.title},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:154,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:153,columnNumber:15},void 0),!e&&i>0&&m.jsxDEV("div",{className:"section-separator"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:162,columnNumber:15},void 0),m.jsxDEV("ul",{className:"menu-list space-y-1",children:a.items.map(a=>m.jsxDEV("li",{className:"menu-item mx-2 rounded-lg transition-all duration-300 relative transform hover:translate-x-1\n                            "+(c(a.path)?"active bg-[#ffda0a] bg-opacity-20 border-l-4 border-[#ffda0a] shadow-lg":"hover:bg-white hover:bg-opacity-10"),children:m.jsxDEV("div",{className:"flex items-center justify-between w-full px-3 py-3",children:[m.jsxDEV(k,{to:a.path,className:"flex items-center flex-grow transition-colors duration-200 "+(c(a.path)?"text-[#ffda0a] font-semibold":"text-[#a4b1cd] hover:text-white"),children:[m.jsxDEV("i",{className:`menu-icon fas fa-${a.icon} ${e?"mr-4":"text-center"} w-5 transition-colors duration-200 ${c(a.path)?"text-[#ffda0a]":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:178,columnNumber:23},void 0),e&&m.jsxDEV("span",{className:"font-medium",children:a.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:179,columnNumber:34},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:174,columnNumber:21},void 0),e&&m.jsxDEV("span",{className:"favorite-star cursor-pointer hover:text-[#ffda0a] transition-all duration-200 ml-2 "+(r[a.key]?"active text-[#ffda0a]":"text-gray-400"),onClick:e=>o(a.key,e),title:r[a.key]?"Quitar de favoritos":"Añadir a favoritos",children:m.jsxDEV("i",{className:(r[a.key]?"fas":"far")+" fa-star text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:187,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:182,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:173,columnNumber:19},void 0)},a.name,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:168,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:166,columnNumber:13},void 0)]},a.title,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:150,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:148,columnNumber:7},void 0),m.jsxDEV("div",{className:"mt-auto p-5 border-t border-opacity-10 border-white",children:e?m.jsxDEV("button",{className:"logout-button flex items-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 hover:bg-red-500 hover:bg-opacity-10 rounded-lg p-3 border border-transparent hover:border-red-500 hover:border-opacity-30",onClick:i,"aria-label":"Cerrar sesión",children:[m.jsxDEV("i",{className:"fas fa-door-open mr-3 transition-transform duration-200"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:206,columnNumber:13},void 0),m.jsxDEV("span",{className:"font-medium",children:"Cerrar Sesión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:207,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:201,columnNumber:11},void 0):m.jsxDEV("button",{className:"logout-button flex justify-center w-full text-gray-400 hover:text-red-400 cursor-pointer transition-all duration-200 p-3 rounded-lg border border-transparent hover:border-red-500 hover:border-opacity-30 hover:bg-red-500 hover:bg-opacity-10",onClick:i,title:"Cerrar Sesión","aria-label":"Cerrar sesión",children:m.jsxDEV("i",{className:"fas fa-door-open transition-transform duration-200"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:216,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:210,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:199,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:90,columnNumber:5},void 0)},Tt=()=>{const{user:e,isAdmin:a,isPsicologo:i,isCandidato:t,logout:r}=p(),[s,o]=P.useState(!0),[n,l]=P.useState(!1),c=P.useRef(null),u=M(),b=i,f=e?`${e.nombre||""} ${e.apellido||""}`.trim():"",g=null==e?void 0:e.email,N=()=>d(null,null,function*(){try{yield r(),u("/login")}catch(e){window.location.href="/login"}});return P.useEffect(()=>{const e=e=>{c.current&&!c.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),m.jsxDEV("div",{className:"min-h-screen bg-gray-50 flex",children:[m.jsxDEV(St,{isOpen:s,toggleSidebar:()=>{o(!s)},onLogout:N},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:274,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-1 transition-all duration-300 ease-in-out\n                    "+(s?"ml-64":"ml-[70px]"),children:[m.jsxDEV("header",{className:"bg-white shadow-sm",children:m.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxDEV("div",{className:"flex justify-between h-16 items-center",children:[m.jsxDEV("div",{className:"flex items-center",children:m.jsxDEV(Rt,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:284,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:283,columnNumber:15},void 0),m.jsxDEV("div",{className:"flex items-center relative",ref:c,children:[m.jsxDEV("button",{className:"flex items-center space-x-3 cursor-pointer hover:bg-gray-50 rounded-lg px-3 py-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>{l(!n)},id:"user-menu-button","aria-expanded":n,"aria-haspopup":"true","aria-label":"Abrir menú de usuario",children:[m.jsxDEV("div",{className:"flex flex-col items-end",children:[m.jsxDEV("span",{className:"text-sm font-medium text-gray-800",children:f||g||"Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:298,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-xs text-gray-500",children:m.jsxDEV("span",{className:"inline-flex items-center",children:[m.jsxDEV("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:303,columnNumber:25},void 0),m.jsxDEV("span",{className:"text-green-600 font-semibold",children:"Activo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:304,columnNumber:25},void 0),m.jsxDEV("span",{className:"mx-2",children:"•"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:305,columnNumber:25},void 0),m.jsxDEV("span",{className:"text-amber-600 font-medium",children:a?"Administrador":b?"Psicólogo":"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:306,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:302,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:301,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:297,columnNumber:19},void 0),m.jsxDEV("div",{className:"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-md",children:m.jsxDEV("i",{className:"fas fa-user-shield"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:313,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:312,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-gray-400",children:m.jsxDEV("i",{className:`fas fa-chevron-${n?"up":"down"} text-xs transition-transform duration-200 ${n?"rotate-180":""}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:316,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:315,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:289,columnNumber:17},void 0),n&&m.jsxDEV("div",{className:"absolute right-0 top-full mt-2 w-72 bg-white rounded-xl menu-shadow border border-gray-200 z-50 overflow-hidden animate-in user-menu-dropdown",role:"menu","aria-orientation":"vertical","aria-labelledby":"user-menu-button",children:[m.jsxDEV("div",{className:"px-5 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-blue-50",children:m.jsxDEV("div",{className:"flex items-start space-x-4",children:[m.jsxDEV("div",{className:"h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white shadow-lg ring-2 ring-blue-100",children:m.jsxDEV("i",{className:"fas fa-user-shield text-lg"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:327,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:326,columnNumber:25},void 0),m.jsxDEV("div",{className:"flex-1 min-w-0",children:[m.jsxDEV("p",{className:"text-base font-semibold text-gray-900 truncate",children:f||g||"Usuario Desarrollo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:330,columnNumber:27},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600 truncate mt-0.5",children:g||"<EMAIL>"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:333,columnNumber:27},void 0),m.jsxDEV("div",{className:"flex items-center mt-2 space-x-2",children:[m.jsxDEV("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200",children:[m.jsxDEV("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-1.5 animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:338,columnNumber:31},void 0),"Activo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:337,columnNumber:29},void 0),m.jsxDEV("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 border border-amber-200",children:a?"Administrador":b?"Psicólogo":"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:341,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:336,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:329,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:325,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:324,columnNumber:21},void 0),m.jsxDEV("div",{className:"py-2",children:[m.jsxDEV(k,{to:"/profile",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 focus:outline-none focus:bg-blue-50 focus:text-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a mi perfil",children:[m.jsxDEV("i",{className:"fas fa-user-cog mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:359,columnNumber:25},void 0),m.jsxDEV("span",{children:"Mi Perfil"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:360,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:351,columnNumber:23},void 0),m.jsxDEV(k,{to:"/configuracion",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 focus:outline-none focus:bg-amber-50 focus:text-amber-700 focus:ring-2 focus:ring-amber-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Ir a configuración del sistema",children:[m.jsxDEV("i",{className:"fas fa-cog mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:371,columnNumber:25},void 0),m.jsxDEV("span",{children:"Configuración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:372,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:363,columnNumber:23},void 0),m.jsxDEV(k,{to:"/help",className:"flex items-center px-5 py-3 text-sm font-medium text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-200 focus:outline-none focus:bg-green-50 focus:text-green-700 focus:ring-2 focus:ring-green-500 focus:ring-inset",onClick:()=>l(!1),role:"menuitem",tabIndex:0,"aria-label":"Obtener ayuda y soporte",children:[m.jsxDEV("i",{className:"fas fa-question-circle mr-4 text-gray-400 w-4 text-center"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:383,columnNumber:25},void 0),m.jsxDEV("span",{children:"Ayuda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:384,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:375,columnNumber:23},void 0),m.jsxDEV("div",{className:"border-t border-gray-200 my-2 mx-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:387,columnNumber:23},void 0),m.jsxDEV("button",{className:"flex w-full items-center px-5 py-3 text-sm font-medium text-red-700 hover:bg-red-50 hover:text-red-800 transition-all duration-200 focus:outline-none focus:bg-red-50 focus:text-red-800 focus:ring-2 focus:ring-red-500 focus:ring-inset group",onClick:()=>{l(!1),N()},role:"menuitem",tabIndex:0,"aria-label":"Cerrar sesión y salir del sistema",children:[m.jsxDEV("i",{className:"fas fa-door-open mr-4 text-red-500 w-4 text-center group-hover:animate-pulse"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:399,columnNumber:25},void 0),m.jsxDEV("span",{children:"Cerrar Sesión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:400,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:389,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:350,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:322,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:288,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:282,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:281,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:280,columnNumber:9},void 0),m.jsxDEV("main",{className:"py-10",children:m.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxDEV(At,{children:m.jsxDEV(q,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:415,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:414,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:412,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:411,columnNumber:9},void 0),m.jsxDEV("footer",{className:"bg-white border-t border-gray-200 py-8",children:m.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:m.jsxDEV("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," BAT-7 Evaluaciones. Todos los derechos reservados."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:423,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:422,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:421,columnNumber:9},void 0),m.jsxDEV(Y,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:430,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:277,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/layout/Layout.jsx",lineNumber:272,columnNumber:5},void 0)},It=({fullScreen:e=!1,message:a="Cargando..."})=>{const i=e?"fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50":"flex flex-col items-center justify-center py-8";return m.jsxDEV("div",{className:i,children:m.jsxDEV("div",{className:"flex flex-col items-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:11,columnNumber:9},void 0),a&&m.jsxDEV("p",{className:"text-gray-600",children:a},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:12,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:10,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/Loading.jsx",lineNumber:9,columnNumber:5},void 0)};class _t extends $.Component{constructor(e){super(e),u(this,"handleRetry",()=>{this.setState({hasError:!1,error:null,errorInfo:null})}),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,a){this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?m.jsxDEV("div",{className:"min-h-64 flex items-center justify-center bg-red-50 rounded-lg border border-red-200 p-8",children:m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV(b,{className:"w-12 h-12 text-red-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:35,columnNumber:13},this),m.jsxDEV("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:this.props.title||"Something went wrong"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:36,columnNumber:13},this),m.jsxDEV("p",{className:"text-red-600 mb-4",children:this.props.message||"An error occurred while loading this component."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:39,columnNumber:13},this),this.state.error&&m.jsxDEV("details",{className:"text-left bg-red-100 p-4 rounded mb-4",children:[m.jsxDEV("summary",{className:"cursor-pointer font-medium",children:"Error Details"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:45,columnNumber:17},this),m.jsxDEV("pre",{className:"text-sm mt-2 overflow-auto",children:[this.state.error.toString(),this.state.errorInfo.componentStack]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:46,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:44,columnNumber:15},this),m.jsxDEV("button",{onClick:this.handleRetry,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 mx-auto transition-colors",children:[m.jsxDEV(f,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:57,columnNumber:15},this),m.jsxDEV("span",{children:"Try Again"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:58,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:53,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:34,columnNumber:11},this)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/common/ErrorBoundary.jsx",lineNumber:33,columnNumber:9},this):this.props.children}}const zt=(e,a,i={})=>{const{serialize:t=JSON.stringify,deserialize:r=JSON.parse,syncAcrossTabs:s=!0,errorOnFailure:o=!1}=i,[n,l]=P.useState(()=>{if("undefined"==typeof window)return a;try{const i=window.localStorage.getItem(e);return i?r(i):a}catch(i){if(o)throw i;return a}}),c=P.useCallback(a=>{try{const i=a instanceof Function?a(n):a;l(i),"undefined"!=typeof window&&(void 0===i?window.localStorage.removeItem(e):window.localStorage.setItem(e,t(i)))}catch(i){if(o)throw i}},[e,t,n,o]),u=P.useCallback(()=>{try{l(void 0),"undefined"!=typeof window&&window.localStorage.removeItem(e)}catch(a){if(o)throw a}},[e,o]);return P.useEffect(()=>{if(!s||"undefined"==typeof window)return;const a=a=>{if(a.key===e&&a.newValue!==t(n))try{const e=a.newValue?r(a.newValue):void 0;l(e)}catch(i){if(o)throw i}};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e,n,t,r,s,o]),[n,c,u]},Ot=P.createContext(),Pt=()=>{const e=P.useContext(Ot);if(!e)throw new Error("usePatientSession must be used within a PatientSessionProvider");return e},Mt=({children:e})=>{const[a,i]=zt("bat7_selected_patient",null),[t,r]=P.useState(!1),[s,o]=zt("bat7_selected_level","E"),[n,l]=zt("bat7_completed_tests",[]),[c,u]=zt("bat7_session_start",null);P.useEffect(()=>{a&&c&&r(!0)},[a,c]);const d={selectedPatient:a,isSessionActive:t,selectedLevel:s,completedTests:n,sessionStartTime:c,startPatientSession:(e,a="E")=>{i(e),o(a),r(!0),u((new Date).toISOString()),l([])},endPatientSession:()=>{i(null),r(!1),u(null),l([])},markTestCompleted:e=>{l(a=>a.includes(e)?a:[...a,e])},isTestCompleted:e=>n.includes(e),updateSelectedLevel:e=>{o(e)},getSessionInfo:()=>t&&a?{patient:a,level:s,startTime:c,completedTests:n,duration:c?Math.floor((new Date-new Date(c))/1e3/60):0}:null,clearSessionData:()=>{i(null),r(!1),u(null),l([]),o("E")},hasActiveSession:t&&a,sessionDuration:c?Math.floor((new Date-new Date(c))/1e3/60):0};return m.jsxDEV(Ot.Provider,{value:d,children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/context/PatientSessionContext.jsx",lineNumber:128,columnNumber:5},void 0)},kt={verbal:{id:"verbal",name:"Test de Aptitud Verbal",type:"verbal",description:"Test V - Evaluación de analogías verbales. Este test evalúa la capacidad para identificar relaciones entre palabras y conceptos, midiendo el razonamiento verbal y la comprensión de relaciones lógicas.",duration:12,numberOfQuestions:32,instructions:["Lee cada pregunta detenidamente antes de responder.","En cada ejercicio, debes encontrar la palabra que completa la frase dotándola de sentido.","Para las analogías verbales, identifica la relación exacta entre el primer par de palabras.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja rápidamente, ya que el tiempo es limitado.","Si no estás completamente seguro de una respuesta, elige la opción que creas más correcta; no se penalizan los errores."],additionalInfo:"Este test evalúa tu capacidad para comprender relaciones entre conceptos expresados a través de palabras. Implica el dominio del lenguaje y la habilidad para entender relaciones lógicas entre conceptos verbales.",components:[{name:"Analogías Verbales",description:"Mide tu capacidad para identificar relaciones entre conceptos"},{name:"Razonamiento Verbal",description:"Evalúa tu habilidad para entender relaciones lógicas"},{name:"Comprensión Lingüística",description:"Mide tu dominio del lenguaje y vocabulario"},{name:"Pensamiento Abstracto",description:"Evalúa tu capacidad para identificar patrones conceptuales"}],recommendations:["Fíjate bien en la relación entre el primer par de palabras para identificar el patrón que debes aplicar.","Si no encuentras la respuesta inmediatamente, analiza cada opción eliminando las que claramente no cumplen con la relación buscada.","Recuerda que las relaciones pueden ser de diversos tipos: causa-efecto, parte-todo, función, oposición, etc.","Si terminas antes del tiempo concedido, aprovecha para revisar tus respuestas."]},ortografia:{id:"ortografia",name:"Test de Ortografía",type:"ortografia",description:"Test O - Evaluación de la capacidad para identificar errores ortográficos en palabras.",duration:10,numberOfQuestions:32,instructions:["En cada grupo de cuatro palabras, identificar la única palabra que está mal escrita (intencionadamente).","La falta de ortografía puede ser de cualquier tipo, incluyendo errores en letras o la ausencia/presencia incorrecta de una tilde.","Marcar la letra correspondiente (A, B, C o D) a la palabra mal escrita.","Trabajar rápidamente. Si no se está seguro, elegir la opción que parezca más correcta (no se penaliza el error).","Si se termina antes, repasar las respuestas."],additionalInfo:"Este test evalúa tu dominio de las reglas ortográficas del español, incluyendo acentuación, uso de letras específicas y formación de palabras.",components:[{name:"Ortografía General",description:"Mide tu conocimiento de las reglas básicas de escritura"},{name:"Acentuación",description:"Evalúa tu dominio de las reglas de acentuación"},{name:"Uso de Letras",description:"Mide tu conocimiento del uso correcto de letras que pueden confundirse"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar errores sutiles"}],recommendations:["Revisa visualmente cada palabra con atención.","Recuerda las reglas de acentuación de palabras agudas, llanas y esdrújulas.","Presta especial atención a las letras que suelen causar confusión: b/v, g/j, h, etc.","Observa la presencia o ausencia de tildes en las palabras."],examples:[{question:"A. año, B. berso, C. vuelo, D. campana",answer:"B",explanation:'La grafía correcta es "verso".'},{question:"A. bosque, B. armario, C. telon, D. libro",answer:"C",explanation:'La palabra correcta es "telón", lleva tilde en la "o".'}]},razonamiento:{id:"razonamiento",name:"Test de Razonamiento",type:"razonamiento",description:"Test R - Evaluación de la capacidad para identificar patrones y continuar series lógicas de figuras.",duration:20,numberOfQuestions:32,instructions:["Observar una serie de figuras y determinar qué figura (A, B, C o D) debería ir a continuación, sustituyendo al interrogante, siguiendo la lógica de la serie.","Analiza cuidadosamente cómo evolucionan las figuras en cada serie.","Busca patrones como rotaciones, traslaciones, adiciones o sustracciones de elementos.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Trabaja metódicamente, ya que algunas secuencias pueden tener patrones complejos.","Si no estás completamente seguro de una respuesta, intenta descartar opciones que claramente no siguen el patrón."],additionalInfo:"Este test evalúa tu capacidad para identificar patrones lógicos y aplicarlos para predecir el siguiente elemento en una secuencia. Es una medida del razonamiento inductivo y del pensamiento lógico-abstracto.",components:[{name:"Razonamiento Inductivo",description:"Mide tu capacidad para identificar reglas a partir de ejemplos"},{name:"Pensamiento Lógico",description:"Evalúa tu habilidad para aplicar reglas sistemáticamente"},{name:"Visualización Espacial",description:"Mide tu capacidad para manipular imágenes mentalmente"},{name:"Atención al Detalle",description:"Evalúa tu capacidad para detectar patrones sutiles"}],recommendations:["Intenta identificar más de un patrón en cada serie (puede haber cambios en color, forma, tamaño y posición).","Observa si hay ciclos repetitivos en los patrones.","Analiza cada elemento individualmente si la figura es compleja.","Si encuentras dificultades, intenta verbalizar el patrón para hacerlo más claro."]},atencion:{id:"atencion",name:"Test de Atención",type:"atencion",description:"Test A - Evaluación de la rapidez y precisión en la localización de símbolos.",duration:8,numberOfQuestions:80,instructions:["En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado.","El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página.","El símbolo puede aparecer 0, 1, 2 o 3 veces en cada fila, pero nunca más de 3.","Deberás marcar cuántas veces aparece el símbolo en cada fila (0, 1, 2 o 3).","Trabaja con rapidez y precisión, asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando.","Avanza sistemáticamente por cada fila, de izquierda a derecha.","Presta especial atención a símbolos muy similares al modelo pero que no son idénticos."],additionalInfo:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. Es una medida de la atención selectiva y sostenida, así como de la velocidad y precisión en el procesamiento de información visual.",components:[{name:"Atención Selectiva",description:"Mide tu capacidad para enfocarte en elementos específicos"},{name:"Velocidad Perceptiva",description:"Evalúa tu rapidez para procesar información visual"},{name:"Discriminación Visual",description:"Mide tu habilidad para distinguir detalles visuales"},{name:"Concentración",description:"Evalúa tu capacidad para mantener el foco durante una tarea repetitiva"}],recommendations:["Mantén un ritmo constante, sin detenerte demasiado en ningún elemento.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas.","Utiliza el dedo o un marcador para seguir las filas si te ayuda a mantener el enfoque.","Evita distracciones y mantén la concentración en la tarea."]},espacial:{id:"espacial",name:"Test de Aptitud Espacial",type:"espacial",description:"Test E - Evaluación del razonamiento espacial con cubos y redes.",duration:15,numberOfQuestions:28,instructions:["En cada ejercicio encontrarás un cubo junto con su modelo desplegado, al que se le han borrado casi todos los números y letras.","Tu tarea consistirá en averiguar qué número o letra debería aparecer en lugar del interrogante (?) y en qué orientación.","En el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente).","Observa cuidadosamente la orientación y posición relativa de las caras en el cubo.","Considera cómo las distintas caras del cubo se conectan entre sí en el modelo desplegado.","Visualiza mentalmente el proceso de plegado del modelo para formar el cubo.","Entre las cuatro opciones, solo UNA es la correcta.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas."],additionalInfo:"Este test evalúa tu capacidad para manipular objetos mentalmente en el espacio tridimensional. Es una medida de la visualización espacial, rotación mental y comprensión de relaciones espaciales.",components:[{name:"Visualización Espacial",description:"Mide tu capacidad para manipular objetos en 3D mentalmente"},{name:"Rotación Mental",description:"Evalúa tu habilidad para rotar figuras en tu mente"},{name:"Relaciones Espaciales",description:"Mide tu comprensión de cómo se conectan las partes de un objeto"},{name:"Razonamiento Geométrico",description:"Evalúa tu entendimiento de principios geométricos básicos"}],recommendations:["Utiliza marcas mentales para orientarte en la ubicación de cada cara del cubo.","Fíjate en detalles específicos de los diseños en cada cara para determinar su orientación correcta.","Si es necesario, utiliza tus manos para ayudarte a visualizar el plegado del modelo.","Si en algún ejercicio no estás completamente seguro de cuál puede ser la respuesta, elige la opción que creas que es más correcta; no se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas."]},mecanico:{id:"mecanico",name:"Test de Razonamiento Mecánico",type:"mecanico",description:"Test M - Evaluación de la comprensión de principios físicos y mecánicos básicos.",duration:12,numberOfQuestions:28,instructions:["Observar dibujos que representan diversas situaciones físicas o mecánicas y responder a una pregunta sobre cada situación, eligiendo la opción más adecuada.","Analiza los elementos del dibujo y cómo interactúan entre sí.","Aplica principios básicos de física y mecánica como palancas, poleas, engranajes, fuerzas, etc.","Ten en cuenta la dirección de las fuerzas, el movimiento o el equilibrio en cada situación.","Entre las opciones presentadas, selecciona la que mejor explica el fenómeno o predice el resultado.","Marca la letra correspondiente (A, B, C o D) en la hoja de respuestas.","Si no estás seguro, intenta aplicar el sentido común y los principios básicos que conozcas."],additionalInfo:"Este test evalúa tu comprensión intuitiva de principios físicos y mecánicos, así como tu capacidad para aplicar estos principios a situaciones prácticas. No requiere conocimientos técnicos avanzados, sino una comprensión básica de cómo funcionan los objetos en el mundo físico.",components:[{name:"Comprensión Física",description:"Mide tu entendimiento de principios físicos básicos"},{name:"Razonamiento Mecánico",description:"Evalúa tu capacidad para entender sistemas mecánicos"},{name:"Resolución de Problemas",description:"Mide tu habilidad para aplicar principios a situaciones nuevas"},{name:"Intuición Tecnológica",description:"Evalúa tu comprensión natural de cómo funcionan las máquinas"}],recommendations:["Recuerda principios básicos como la ley de la palanca, la transmisión de fuerzas en poleas y engranajes.","Considera factores como la gravedad, la fricción y la inercia cuando analices cada situación.","Visualiza el movimiento o la acción que ocurriría en la situación presentada.","Si tienes dificultades, intenta simplificar el problema a sus componentes más básicos."]},numerico:{id:"numerico",name:"Test de Aptitud Numérica",type:"numerico",description:"Test N - Resolución de problemas numéricos, series y tablas.",duration:20,numberOfQuestions:32,instructions:["En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver.","Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante.","Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente en la hoja de respuestas.","Asegúrate de que coincida con el ejercicio que estás contestando.","","El tiempo máximo para su realización es de 20 minutos, por lo que deberás trabajar rápidamente.","Esfuérzate al máximo en encontrar la respuesta correcta.","Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta.","No se penalizará el error.","Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."],additionalInfo:"Este test evalúa tu capacidad para resolver problemas numéricos mediante el análisis de igualdades, series y tablas. Mide el razonamiento matemático, la identificación de patrones numéricos y la habilidad para trabajar con datos organizados.",components:[{name:"Igualdades Numéricas",description:"Resolver ecuaciones con elementos faltantes"},{name:"Series Numéricas",description:"Identificar patrones y continuar secuencias"},{name:"Tablas de Datos",description:"Analizar información organizada y encontrar valores faltantes"},{name:"Cálculo Mental",description:"Realizar operaciones matemáticas con rapidez y precisión"}],recommendations:["Lee cuidadosamente cada problema antes de intentar resolverlo.","En las igualdades, calcula primero el lado conocido de la ecuación.","En las series, busca patrones simples antes de considerar reglas más complejas.","En las tablas, analiza las relaciones entre filas y columnas.","Verifica tus cálculos cuando sea posible.","Si no estás seguro, elige la opción que te parezca más lógica."]},bat7:{id:"bat7",name:"Batería Completa BAT-7",type:"battery",description:"Evaluación completa de aptitudes y habilidades cognitivas.",duration:120,numberOfQuestions:184,instructions:["Lee atentamente cada pregunta antes de responder.","Responde a todas las preguntas, aunque no estés seguro/a de la respuesta.","Administra bien tu tiempo. Si una pregunta te resulta difícil, pasa a la siguiente y vuelve a ella más tarde.","No uses calculadora ni ningún otro dispositivo o material durante el test.","Una vez iniciado el test, no podrás pausarlo. Asegúrate de disponer del tiempo necesario para completarlo.","Responde con honestidad. Este test está diseñado para evaluar tus habilidades actuales.","Cada subtest tiene instrucciones específicas que deberás leer antes de comenzar esa sección."],additionalInfo:"La batería BAT-7 está compuesta por siete pruebas independientes que evalúan diferentes aptitudes: verbal, espacial, numérica, mecánica, razonamiento, atención y ortografía. Cada prueba tiene un tiempo específico de realización y unas instrucciones particulares.",subtests:[{id:"verbal",name:"Test de Aptitud Verbal (V)",duration:12,questions:32,description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos."},{id:"ortografia",name:"Test de Ortografía (O)",duration:10,questions:32,description:"Identificación de palabras con errores ortográficos."},{id:"razonamiento",name:"Test de Razonamiento (R)",duration:20,questions:32,description:"Continuación de series lógicas de figuras."},{id:"atencion",name:"Test de Atención (A)",duration:8,questions:80,description:"Rapidez y precisión en la localización de símbolos."},{id:"espacial",name:"Test de Visualización Espacial (E)",duration:15,questions:28,description:"Razonamiento espacial con cubos y redes."},{id:"mecanico",name:"Test de Razonamiento Mecánico (M)",duration:12,questions:28,description:"Comprensión de principios físicos y mecánicos básicos."},{id:"numerico",name:"Test de Razonamiento Numérico (N)",duration:20,questions:32,description:"Resolución de problemas numéricos, series y tablas."}],recommendations:["Descansa adecuadamente antes de realizar la batería completa.","Realiza los tests en un ambiente tranquilo y sin distracciones.","Gestiona tu energía a lo largo de toda la batería, ya que algunos tests son más exigentes que otros.","Mantén una actitud positiva y confía en tus capacidades."]}},qt={bat7:{icon:"fas fa-clipboard-list",color:"text-purple-600"},verbal:{icon:"fas fa-comments",color:"text-blue-600"},espacial:{icon:"fas fa-cube",color:"text-indigo-600"},atencion:{icon:"fas fa-eye",color:"text-red-600"},razonamiento:{icon:"fas fa-puzzle-piece",color:"text-amber-600"},numerico:{icon:"fas fa-calculator",color:"text-teal-600"},mecanico:{icon:"fas fa-cogs",color:"text-slate-600"},ortografia:{icon:"fas fa-spell-check",color:"text-green-600"}},Lt=()=>{var e;const{testId:a}=F(),i=M(),t=L(),[r,s]=P.useState(null),[o,n]=P.useState(!0),[l,c]=P.useState(!1),u=null==(e=t.state)?void 0:e.patientId;P.useEffect(()=>{d(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),kt[a]?s(kt[a]):J.warning(`No se encontraron instrucciones para el test: ${a}`),n(!1)}catch(e){J.error("Error al cargar la información del test"),n(!1)}})},[a]);const p=qt[a]||{icon:"fas fa-clipboard-list",color:"text-gray-600"};return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6 text-center",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:`${p.icon} ${p.color} mr-2`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:84,columnNumber:11},void 0),"Instrucciones del Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:83,columnNumber:9},void 0),!o&&r&&m.jsxDEV("p",{className:"text-gray-600",children:r.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:88,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:82,columnNumber:7},void 0),o?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:95,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando instrucciones del test..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:96,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:94,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:93,columnNumber:9},void 0):r?m.jsxDEV(m.Fragment,{children:[m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"text-center",children:m.jsxDEV("h2",{className:"text-lg font-medium",children:"Información General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:103,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:102,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Descripción"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:108,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-700",children:r.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:109,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:107,columnNumber:17},void 0),m.jsxDEV("div",{className:"grid grid-cols-2 gap-4",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Duración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:113,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-gray-700 text-center",children:[r.duration," minutos"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:114,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:112,columnNumber:19},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-md font-medium mb-2 text-center",children:"Preguntas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:117,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-gray-700 text-center",children:[r.numberOfQuestions," preguntas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:118,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:116,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:111,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:106,columnNumber:15},void 0),r.additionalInfo&&m.jsxDEV("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-4 mb-4",children:m.jsxDEV("p",{className:"text-blue-700",children:r.additionalInfo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:125,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:124,columnNumber:17},void 0),"battery"!==r.type&&r.components&&m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("h3",{className:"text-md font-medium mb-3 text-center",children:"Componentes Evaluados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:132,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:r.components.map((e,a)=>m.jsxDEV("div",{className:"border rounded p-3",children:[m.jsxDEV("p",{className:"font-medium",children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:136,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:137,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:135,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:133,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:131,columnNumber:17},void 0),"battery"===r.type&&r.subtests&&r.subtests.length>0&&m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("h3",{className:"text-md font-medium mb-3 text-center",children:"Subtests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:147,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:r.subtests.map((e,a)=>m.jsxDEV("div",{className:"border rounded p-3",children:[m.jsxDEV("p",{className:"font-medium",children:[a+1,". ",e.name]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:151,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600 mt-1",children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:152,columnNumber:25},void 0),m.jsxDEV("div",{className:"flex justify-between mt-2 text-sm text-gray-600",children:[m.jsxDEV("span",{children:[e.duration," min"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:154,columnNumber:27},void 0),m.jsxDEV("span",{children:[e.questions," preguntas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:155,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:153,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:150,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:148,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:146,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:105,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:101,columnNumber:11},void 0),m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"text-center",children:m.jsxDEV("h2",{className:"text-lg font-medium",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:167,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:166,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"space-y-3",children:r.instructions.map((e,a)=>{if(""===e)return m.jsxDEV("div",{className:"h-4"},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:174,columnNumber:28},void 0);if(e.startsWith("**")&&e.endsWith("**"))return m.jsxDEV("h4",{className:"text-lg font-semibold text-gray-800 mt-6 mb-3 border-b border-gray-200 pb-2",children:e.replace(/\*\*/g,"")},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:180,columnNumber:23},void 0);const i=r.instructions.slice(0,a+1).filter(e=>""!==e&&!e.startsWith("**")).length;return m.jsxDEV("div",{className:"flex items-start",children:[m.jsxDEV("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3 mt-0.5",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:193,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:196,columnNumber:23},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:192,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:170,columnNumber:15},void 0),r.recommendations&&m.jsxDEV("div",{className:"mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-md font-medium text-yellow-800 mb-2",children:"Recomendaciones Adicionales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:205,columnNumber:19},void 0),m.jsxDEV("ul",{className:"space-y-2 text-yellow-700",children:r.recommendations.map((e,a)=>m.jsxDEV("li",{children:["• ",e]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:208,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:206,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:204,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:169,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:165,columnNumber:11},void 0),m.jsxDEV(K,{children:[m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"flex items-start mb-4",children:[m.jsxDEV("input",{type:"checkbox",id:"accept-conditions",checked:l,onChange:e=>c(e.target.checked),className:"h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500 mt-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:219,columnNumber:17},void 0),m.jsxDEV("label",{htmlFor:"accept-conditions",className:"ml-3 text-gray-700",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:226,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:218,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:217,columnNumber:13},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:l?"primary":"outline",onClick:()=>{l?(J.info("Iniciando test..."),"battery"===r.type?r.subtests&&r.subtests.length>0?i(`/test/${r.subtests[0].id}`,{state:{patientId:u}}):i("/student/tests"):i(`/test/${r.id}`,{state:{patientId:u}})):J.warning("Debes aceptar las condiciones para continuar")},disabled:!l,children:"Iniciar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:232,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:231,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:216,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:100,columnNumber:9},void 0):m.jsxDEV(K,{children:m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"py-8 text-center",children:m.jsxDEV("p",{className:"text-gray-500",children:"No se encontró información para el test solicitado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:246,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:245,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:244,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:243,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Instructions.jsx",lineNumber:81,columnNumber:5},void 0)},$t=Object.freeze(Object.defineProperty({__proto__:null,default:Lt},Symbol.toStringTag,{value:"Module"})),Ft={"12-13":{V:[{pc:99,pd:[30,32]},{pc:97,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[27,27]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:55,pd:[22,22]},{pc:50,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:25,pd:[18,18]},{pc:20,pd:[17,17]},{pc:15,pd:[16,16]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[27,28]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[23,23]},{pc:80,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:45,pd:[18,18]},{pc:55,pd:[18,18]},{pc:50,pd:[17,17]},{pc:40,pd:[16,16]},{pc:35,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],A:[{pc:99,pd:[49,80]},{pc:98,pd:[48,48]},{pc:97,pd:[46,47]},{pc:96,pd:[44,45]},{pc:95,pd:[43,43]},{pc:90,pd:[39,42]},{pc:85,pd:[36,38]},{pc:80,pd:[35,35]},{pc:75,pd:[34,34]},{pc:70,pd:[33,33]},{pc:65,pd:[31,32]},{pc:60,pd:[29,30]},{pc:55,pd:[28,28]},{pc:50,pd:[27,27]},{pc:45,pd:[26,26]},{pc:40,pd:[25,25]},{pc:35,pd:[24,24]},{pc:30,pd:[23,23]},{pc:25,pd:[22,22]},{pc:20,pd:[21,21]},{pc:15,pd:[19,20]},{pc:10,pd:[17,18]},{pc:5,pd:[15,16]},{pc:4,pd:[13,14]},{pc:2,pd:[12,12]},{pc:1,pd:[0,11]}],CON:[{pc:99,pd:[98,100]},{pc:97,pd:[96,97]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[88,88]},{pc:75,pd:[85,87]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[74,75]},{pc:40,pd:[72,73]},{pc:35,pd:[69,71]},{pc:30,pd:[67,68]},{pc:25,pd:[64,66]},{pc:20,pd:[61,63]},{pc:15,pd:[56,60]},{pc:10,pd:[47,55]},{pc:5,pd:[36,46]},{pc:4,pd:[33,35]},{pc:3,pd:[29,32]},{pc:2,pd:[28,28]},{pc:1,pd:[0,27]}],R:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:96,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:70,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[8,10]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}],N:[{pc:99,pd:[28,32]},{pc:98,pd:[27,27]},{pc:97,pd:[26,26]},{pc:96,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[22,23]},{pc:85,pd:[22,22]},{pc:85,pd:[20,21]},{pc:80,pd:[19,19]},{pc:75,pd:[18,18]},{pc:70,pd:[17,17]},{pc:65,pd:[16,16]},{pc:60,pd:[15,15]},{pc:55,pd:[14,14]},{pc:50,pd:[13,13]},{pc:45,pd:[12,12]},{pc:40,pd:[11,11]},{pc:35,pd:[10,10]},{pc:25,pd:[9,9]},{pc:20,pd:[8,8]},{pc:15,pd:[7,7]},{pc:10,pd:[6,6]},{pc:5,pd:[5,5]},{pc:3,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[25,28]},{pc:96,pd:[24,24]},{pc:95,pd:[23,23]},{pc:90,pd:[22,22]},{pc:85,pd:[21,21]},{pc:80,pd:[20,20]},{pc:70,pd:[19,19]},{pc:60,pd:[18,18]},{pc:50,pd:[17,17]},{pc:45,pd:[16,16]},{pc:35,pd:[15,15]},{pc:30,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[27,28]},{pc:85,pd:[26,26]},{pc:80,pd:[25,25]},{pc:70,pd:[24,24]},{pc:65,pd:[23,23]},{pc:60,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:35,pd:[17,17]},{pc:30,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:4,pd:[8,8]},{pc:3,pd:[7,7]},{pc:2,pd:[6,6]},{pc:1,pd:[0,5]}]},"13-14":{V:[{pc:99,pd:[31,32]},{pc:98,pd:[30,30]},{pc:95,pd:[29,29]},{pc:90,pd:[28,28]},{pc:85,pd:[27,27]},{pc:75,pd:[26,26]},{pc:65,pd:[25,25]},{pc:60,pd:[24,24]},{pc:50,pd:[23,23]},{pc:45,pd:[22,22]},{pc:35,pd:[21,21]},{pc:30,pd:[20,20]},{pc:25,pd:[19,19]},{pc:20,pd:[18,18]},{pc:15,pd:[16,17]},{pc:10,pd:[15,15]},{pc:5,pd:[13,14]},{pc:4,pd:[12,12]},{pc:2,pd:[11,11]},{pc:1,pd:[0,10]}],E:[{pc:99,pd:[28,28]},{pc:97,pd:[27,27]},{pc:95,pd:[26,26]},{pc:90,pd:[25,25]},{pc:85,pd:[24,24]},{pc:80,pd:[23,23]},{pc:75,pd:[22,22]},{pc:65,pd:[21,21]},{pc:60,pd:[20,20]},{pc:50,pd:[19,19]},{pc:45,pd:[18,18]},{pc:40,pd:[17,17]},{pc:35,pd:[16,16]},{pc:25,pd:[15,15]},{pc:20,pd:[14,14]},{pc:15,pd:[13,13]},{pc:10,pd:[12,12]},{pc:5,pd:[10,11]},{pc:4,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}],A:[{pc:99,pd:[60,80]},{pc:98,pd:[55,59]},{pc:97,pd:[51,54]},{pc:96,pd:[49,50]},{pc:95,pd:[48,48]},{pc:90,pd:[42,47]},{pc:85,pd:[39,41]},{pc:80,pd:[37,38]},{pc:75,pd:[35,36]},{pc:70,pd:[34,34]},{pc:65,pd:[33,33]},{pc:60,pd:[31,32]},{pc:55,pd:[30,30]},{pc:50,pd:[29,29]},{pc:45,pd:[28,28]},{pc:40,pd:[26,27]},{pc:35,pd:[25,25]},{pc:30,pd:[24,24]},{pc:25,pd:[23,23]},{pc:20,pd:[22,22]},{pc:15,pd:[20,21]},{pc:10,pd:[18,19]},{pc:5,pd:[14,17]},{pc:3,pd:[13,13]},{pc:2,pd:[10,12]},{pc:1,pd:[0,9]}],CON:[{pc:99,pd:[100,100]},{pc:98,pd:[97,99]},{pc:97,pd:[96,96]},{pc:96,pd:[95,95]},{pc:95,pd:[94,94]},{pc:90,pd:[91,93]},{pc:85,pd:[89,90]},{pc:80,pd:[87,88]},{pc:75,pd:[85,86]},{pc:70,pd:[83,84]},{pc:65,pd:[82,82]},{pc:60,pd:[80,81]},{pc:55,pd:[78,79]},{pc:50,pd:[76,77]},{pc:45,pd:[75,75]},{pc:40,pd:[72,74]},{pc:35,pd:[70,71]},{pc:30,pd:[68,69]},{pc:25,pd:[66,67]},{pc:20,pd:[61,65]},{pc:15,pd:[57,60]},{pc:10,pd:[49,56]},{pc:5,pd:[37,48]},{pc:4,pd:[35,36]},{pc:3,pd:[31,34]},{pc:2,pd:[29,30]},{pc:1,pd:[0,28]}],R:[{pc:99,pd:[30,32]},{pc:98,pd:[29,29]},{pc:95,pd:[28,28]},{pc:90,pd:[26,27]},{pc:85,pd:[25,25]},{pc:80,pd:[24,24]},{pc:70,pd:[23,23]},{pc:65,pd:[22,22]},{pc:55,pd:[21,21]},{pc:50,pd:[20,20]},{pc:45,pd:[19,19]},{pc:40,pd:[18,18]},{pc:30,pd:[17,17]},{pc:25,pd:[16,16]},{pc:20,pd:[15,15]},{pc:15,pd:[13,14]},{pc:10,pd:[11,12]},{pc:5,pd:[9,10]},{pc:3,pd:[8,8]},{pc:2,pd:[7,7]},{pc:1,pd:[0,6]}],N:[{pc:99,pd:[29,32]},{pc:98,pd:[28,28]},{pc:97,pd:[27,27]},{pc:96,pd:[26,26]},{pc:95,pd:[25,25]},{pc:90,pd:[24,24]},{pc:85,pd:[22,23]},{pc:80,pd:[21,21]},{pc:70,pd:[19,20]},{pc:65,pd:[17,18]},{pc:50,pd:[15,15]},{pc:45,pd:[14,14]},{pc:40,pd:[13,13]},{pc:30,pd:[12,12]},{pc:25,pd:[10,11]},{pc:20,pd:[9,9]},{pc:15,pd:[8,8]},{pc:10,pd:[7,7]},{pc:5,pd:[5,6]},{pc:2,pd:[4,4]},{pc:1,pd:[0,3]}],M:[{pc:99,pd:[26,28]},{pc:97,pd:[25,25]},{pc:95,pd:[24,24]},{pc:90,pd:[23,23]},{pc:85,pd:[22,22]},{pc:75,pd:[21,21]},{pc:70,pd:[20,20]},{pc:60,pd:[19,19]},{pc:50,pd:[18,18]},{pc:45,pd:[17,17]},{pc:40,pd:[16,16]},{pc:30,pd:[15,15]},{pc:25,pd:[14,14]},{pc:20,pd:[13,13]},{pc:15,pd:[12,12]},{pc:10,pd:[11,11]},{pc:5,pd:[10,10]},{pc:4,pd:[9,9]},{pc:3,pd:[8,8]},{pc:1,pd:[0,7]}],O:[{pc:99,pd:[32,32]},{pc:98,pd:[31,31]},{pc:95,pd:[30,30]},{pc:90,pd:[29,29]},{pc:85,pd:[27,28]},{pc:80,pd:[26,26]},{pc:70,pd:[25,25]},{pc:65,pd:[24,24]},{pc:60,pd:[23,23]},{pc:50,pd:[22,22]},{pc:45,pd:[21,21]},{pc:40,pd:[20,20]},{pc:35,pd:[19,19]},{pc:30,pd:[18,18]},{pc:25,pd:[17,17]},{pc:20,pd:[16,16]},{pc:15,pd:[14,15]},{pc:10,pd:[13,13]},{pc:5,pd:[10,12]},{pc:3,pd:[9,9]},{pc:2,pd:[8,8]},{pc:1,pd:[0,7]}]}},Ut=(e,a,i)=>{if(null==a)return null;let t;if("string"!=typeof a&&(a=String(a)),12===i)t="12-13";else{if(13!==i&&14!==i)return null;t="13-14"}const r=Ft[t];if(!r)return null;const s=r[a.toUpperCase()];if(!s)return null;for(const o of s)if(e>=o.pd[0]&&e<=o.pd[1])return o.pc;return e<s[s.length-1].pd[0]?s[s.length-1].pc:e>s[0].pd[1]?s[0].pc:null};class Qt{static calcularEdad(e){if(!e)return null;const a=new Date,i=new Date(e);let t=a.getFullYear()-i.getFullYear();const r=a.getMonth()-i.getMonth();return(r<0||0===r&&a.getDate()<i.getDate())&&t--,t}static determinarGrupoEdad(e){return 12===e?"12-13":13===e||14===e?"13-14":null}static convertirPdAPC(e,a,i){return d(this,null,function*(){try{const{data:t,error:r}=yield g.from("pacientes").select("fecha_nacimiento").eq("id",i).single();if(r)return null;const s=this.calcularEdad(t.fecha_nacimiento);if(!s)return null;return{pc:Ut(e,a,s),edad:s,grupoEdad:this.determinarGrupoEdad(s),pd:e}}catch(t){return null}})}static actualizarResultadoConPC(e,a){return d(this,null,function*(){try{const{data:i,error:t}=yield g.from("resultados").update({percentil:a,updated_at:(new Date).toISOString()}).eq("id",e).select();return t?null:i[0]}catch(i){return null}})}static procesarConversionAutomatica(e,a,i,t){return d(this,null,function*(){try{const r=yield this.convertirPdAPC(a,i,t);if(!r)return null;return yield this.actualizarResultadoConPC(e,r.pc,r.edad,r.grupoEdad)}catch(r){return null}})}static obtenerInterpretacionPC(e){return e>=98?{nivel:"Muy Alto",color:"text-green-700",bg:"bg-green-100"}:e>=85?{nivel:"Alto",color:"text-blue-700",bg:"bg-blue-100"}:e>=70?{nivel:"Medio-Alto",color:"text-indigo-700",bg:"bg-indigo-100"}:e>=31?{nivel:"Medio",color:"text-gray-700",bg:"bg-gray-100"}:e>=16?{nivel:"Medio-Bajo",color:"text-yellow-700",bg:"bg-yellow-100"}:e>=3?{nivel:"Bajo",color:"text-orange-700",bg:"bg-orange-100"}:{nivel:"Muy Bajo",color:"text-red-700",bg:"bg-red-100"}}static recalcularPCPaciente(e){return d(this,null,function*(){var a;try{const{data:i,error:t}=yield g.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo)\n        ").eq("paciente_id",e).is("percentil",null);if(t)return!1;let r=0;for(const s of i){(yield this.procesarConversionAutomatica(s.id,s.puntaje_directo,null==(a=s.aptitudes)?void 0:a.codigo,e))&&r++}return!0}catch(i){return!1}})}}class Wt{static getAptitudeId(e){return d(this,null,function*(){try{const a=this.TEST_APTITUDE_MAP[e];if(!a)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:i,error:t}=yield g.from("aptitudes").select("id").eq("codigo",a).single();if(t)throw t;return i.id}catch(a){throw a}})}static calculateConcentration(e,a=0){return e&&0!==e?e/(e+a)*100:0}static saveTestResult(e){return d(this,arguments,function*({patientId:e,testType:a,correctCount:i,incorrectCount:t,unansweredCount:r,timeUsed:s,totalQuestions:o,answers:n={},errores:l=0}){try{if(!e)throw new Error("ID del paciente es requerido");if(!a)throw new Error("Tipo de test es requerido");const m=yield this.getAptitudeId(a),p=i||0;let b=null;"atencion"===a&&(b=this.calculateConcentration(p,l));const f={paciente_id:e,aptitud_id:m,puntaje_directo:p,tiempo_segundos:s||0,respuestas:n,errores:l,concentracion:b,respuestas_correctas:i||0,respuestas_incorrectas:t||0,respuestas_sin_contestar:r||0,total_preguntas:o||0,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:x,error:h}=yield g.from("resultados").insert([f]).select().single();if(h)throw h;try{const a=(yield N(()=>d(this,null,function*(){const{default:e}=yield import("./InformesService-DExmTbda.js");return{default:e}}),__vite__mapDeps([0,1,2,3,4,5,6]))).default;(yield a.generarInformeAutomatico(e,x.id))&&J.success("Resultado guardado e informe generado automáticamente")}catch(c){}try{if(x.percentil)return J.success(`Resultado guardado y convertido automáticamente (PC: ${x.percentil})`),x;const i=this.TEST_APTITUDE_MAP[a];if(i&&x.id){const a=yield ie.forzarConversionResultado(x.id);if(a.success)return a.resultado;{const a=yield Qt.procesarConversionAutomatica(x.id,p,i,e);if(a)return J.success(`Resultado guardado y convertido (PC: ${a.percentil})`),a}}}catch(u){J.warning("Resultado guardado, pero falló la conversión automática a PC")}return J.success("Resultado guardado correctamente en la base de datos"),x}catch(m){throw J.error(`Error al guardar resultado: ${m.message}`),m}})}static getPatientResults(e){return d(this,null,function*(){try{const{data:a,error:i}=yield g.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(i)throw i;return a||[]}catch(a){throw a}})}static hasTestResult(e,a){return d(this,null,function*(){try{const i=yield this.getAptitudeId(a),{data:t,error:r}=yield g.from("resultados").select("id").eq("paciente_id",e).eq("aptitud_id",i).limit(1);if(r)throw r;return t&&t.length>0}catch(i){return!1}})}static updateTestResult(e,a){return d(this,null,function*(){try{const{data:i,error:t}=yield g.from("resultados").update(l(n({},a),{updated_at:(new Date).toISOString()})).eq("id",e).select().single();if(t)throw t;return J.success("Resultado actualizado correctamente"),i}catch(i){throw J.error(`Error al actualizar resultado: ${i.message}`),i}})}static deleteTestResult(e){return d(this,null,function*(){try{const{error:a}=yield g.from("resultados").delete().eq("id",e);if(a)throw a;return J.success("Resultado eliminado correctamente"),!0}catch(a){throw J.error(`Error al eliminar resultado: ${a.message}`),a}})}static getPatientStats(e){return d(this,null,function*(){try{const a=yield this.getPatientResults(e),i={totalTests:a.length,averageScore:0,completedTests:a.map(e=>{var a;return null==(a=e.aptitudes)?void 0:a.codigo}).filter(Boolean),lastTestDate:a.length>0?a[0].created_at:null};if(a.length>0){const e=a.reduce((e,a)=>e+(a.puntaje_directo||0),0);i.averageScore=Math.round(e/a.length)}return i}catch(a){throw a}})}}u(Wt,"TEST_APTITUDE_MAP",{verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"});const Ht=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(!0),[s,o]=P.useState([]),[c,u]=P.useState(0),[p,b]=P.useState({}),[f,g]=P.useState(720),[N,x]=P.useState(!1),h=null==(e=i.state)?void 0:e.patientId,v={1:"4",2:"1",3:"4",4:"2",5:"2",6:"1",7:"3",8:"2",9:"3",10:"4",11:"3",12:"4",13:"3",14:"4",15:"2",16:"3",17:"3",18:"2",19:"3",20:"2",21:"3",22:"3",23:"4",24:"3",25:"2",26:"1",27:"3",28:"1",29:"2",30:"2",31:"1",32:"1"},j={a:"1",b:"2",c:"3",d:"4"};P.useEffect(()=>{d(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800)),o([{id:1,type:"analogies",text:"Ciudad es a hombre como colmena es a ...",options:[{id:"a",text:"Hormiga"},{id:"b",text:"Mosquito"},{id:"c",text:"Araña"},{id:"d",text:"Abeja"}],correctAnswer:"d"},{id:2,type:"analogies",text:"Batido es a batir como zumo es a ...",options:[{id:"a",text:"Exprimir"},{id:"b",text:"Aplastar"},{id:"c",text:"Machacar"},{id:"d",text:"Succionar"}],correctAnswer:"a"},{id:3,type:"analogies",text:"Consejero es a consejo como cantante es a ...",options:[{id:"a",text:"Fama"},{id:"b",text:"Éxito"},{id:"c",text:"Composición"},{id:"d",text:"Canción"}],correctAnswer:"d"},{id:4,type:"analogies",text:"Estufa es a calor como nevera es a ...",options:[{id:"a",text:"Temperatura"},{id:"b",text:"Frío"},{id:"c",text:"Conservación"},{id:"d",text:"Congelación"}],correctAnswer:"b"},{id:5,type:"analogies",text:"Martillo es a clavo como destornillador es a ...",options:[{id:"a",text:"Hierro"},{id:"b",text:"Tornillo"},{id:"c",text:"Remache"},{id:"d",text:"Herramienta"}],correctAnswer:"b"},{id:6,type:"analogies",text:"Asa es a cesta como pomo es a ...",options:[{id:"a",text:"Puerta"},{id:"b",text:"Tirador"},{id:"c",text:"Envase"},{id:"d",text:"Manillar"}],correctAnswer:"a"},{id:7,type:"analogies",text:"Líquido es a sopa como sólido es a ...",options:[{id:"a",text:"Comer"},{id:"b",text:"Bebida"},{id:"c",text:"Plátano"},{id:"d",text:"Gaseoso"}],correctAnswer:"c"},{id:8,type:"analogies",text:"Ballena es a acuático como león es a ...",options:[{id:"a",text:"Carnívoro"},{id:"b",text:"Terrestre"},{id:"c",text:"Depredador"},{id:"d",text:"Devorador"}],correctAnswer:"b"},{id:9,type:"analogies",text:"Restar es a sumar como arreglar es a ...",options:[{id:"a",text:"Incluir"},{id:"b",text:"Corregir"},{id:"c",text:"Estropear"},{id:"d",text:"Resarcir"}],correctAnswer:"c"},{id:10,type:"analogies",text:"Más es a menos como después es a ...",options:[{id:"a",text:"Tiempo"},{id:"b",text:"Siguiente"},{id:"c",text:"Pronto"},{id:"d",text:"Antes"}],correctAnswer:"d"},{id:11,type:"analogies",text:"Fémur es a hueso como corazón es a ...",options:[{id:"a",text:"Glándula"},{id:"b",text:"Vena"},{id:"c",text:"Músculo"},{id:"d",text:"Arteria"}],correctAnswer:"c"},{id:12,type:"analogies",text:"Cuatro es a cinco como cuadrado es a ...",options:[{id:"a",text:"Triángulo"},{id:"b",text:"Heptágono"},{id:"c",text:"Hexágono"},{id:"d",text:"Pentágono"}],correctAnswer:"d"},{id:13,type:"analogies",text:"Harina es a trigo como cerveza es a ...",options:[{id:"a",text:"Manzana"},{id:"b",text:"Patata"},{id:"c",text:"Cebada"},{id:"d",text:"Alfalfa"}],correctAnswer:"c"},{id:14,type:"analogies",text:"Pie es a cuerpo como bombilla es a ...",options:[{id:"a",text:"Ojos"},{id:"b",text:"Luz"},{id:"c",text:"Vela"},{id:"d",text:"Lámpara"}],correctAnswer:"d"},{id:15,type:"analogies",text:"Excavar es a cavidad como alinear es a ...",options:[{id:"a",text:"Seguido"},{id:"b",text:"Recta"},{id:"c",text:"Acodo"},{id:"d",text:"Ensamblar"}],correctAnswer:"b"},{id:16,type:"analogies",text:"Harina es a pan como leche es a ...",options:[{id:"a",text:"Vaca"},{id:"b",text:"Trigo"},{id:"c",text:"Yogur"},{id:"d",text:"Agua"}],correctAnswer:"c"},{id:17,type:"analogies",text:"Círculo es a cuadrado como esfera es a ...",options:[{id:"a",text:"Cuadrilátero"},{id:"b",text:"Rombo"},{id:"c",text:"Cubo"},{id:"d",text:"Circunferencia"}],correctAnswer:"c"},{id:18,type:"analogies",text:"Bicicleta es a avión como metal es a ...",options:[{id:"a",text:"Solidez"},{id:"b",text:"Madera"},{id:"c",text:"Velocidad"},{id:"d",text:"Fragmento"}],correctAnswer:"b"},{id:19,type:"analogies",text:"Doctora es a doctor como amazona es a ...",options:[{id:"a",text:"Piloto"},{id:"b",text:"Modisto"},{id:"c",text:"Jinete"},{id:"d",text:"Bailarín"}],correctAnswer:"c"},{id:20,type:"analogies",text:"Escultor es a estudio como actor es a ...",options:[{id:"a",text:"Arte"},{id:"b",text:"Escenario"},{id:"c",text:"Drama"},{id:"d",text:"Literatura"}],correctAnswer:"b"},{id:21,type:"analogies",text:"Perder es a ganar como reposo es a ...",options:[{id:"a",text:"Ganancia"},{id:"b",text:"Descanso"},{id:"c",text:"Actividad"},{id:"d",text:"Calma"}],correctAnswer:"c"},{id:22,type:"analogies",text:"Encubierto es a clandestino como endeble es a ...",options:[{id:"a",text:"Doblado"},{id:"b",text:"Simple"},{id:"c",text:"Delicado"},{id:"d",text:"Comprimido"}],correctAnswer:"c"},{id:23,type:"analogies",text:"Apocado es a tímido como arrogante es a ...",options:[{id:"a",text:"Listo"},{id:"b",text:"Humilde"},{id:"c",text:"Virtuoso"},{id:"d",text:"Soberbio"}],correctAnswer:"d"},{id:24,type:"analogies",text:"Rodillo es a masa como torno es a ...",options:[{id:"a",text:"Escayola"},{id:"b",text:"Goma"},{id:"c",text:"Arcilla"},{id:"d",text:"Pintura"}],correctAnswer:"c"},{id:25,type:"analogies",text:"Hora es a tiempo como litro es a ...",options:[{id:"a",text:"Peso"},{id:"b",text:"Capacidad"},{id:"c",text:"Balanza"},{id:"d",text:"Cantidad"}],correctAnswer:"b"},{id:26,type:"analogies",text:"Indefenso es a desvalido como enlazado es a ...",options:[{id:"a",text:"Conexo"},{id:"b",text:"Recorrido"},{id:"c",text:"Torcido"},{id:"d",text:"Explorado"}],correctAnswer:"a"},{id:27,type:"analogies",text:"Reparar es a enmendar como mantener es a ...",options:[{id:"a",text:"Moderar"},{id:"b",text:"Presumir"},{id:"c",text:"Proseguir"},{id:"d",text:"Ayunar"}],correctAnswer:"c"},{id:28,type:"analogies",text:"Adelantar es a demorar como anticipar es a ...",options:[{id:"a",text:"Aplazar"},{id:"b",text:"Desistir"},{id:"c",text:"Proveer"},{id:"d",text:"Achacar"}],correctAnswer:"a"},{id:29,type:"analogies",text:"Infinito es a inagotable como vasto es a ...",options:[{id:"a",text:"Expedito"},{id:"b",text:"Colosal"},{id:"c",text:"Demorado"},{id:"d",text:"Confuso"}],correctAnswer:"b"},{id:30,type:"analogies",text:"Amenazar es a intimidar como articular es a ...",options:[{id:"a",text:"Legislar"},{id:"b",text:"Pronunciar"},{id:"c",text:"Afirmar"},{id:"d",text:"Arquear"}],correctAnswer:"b"},{id:31,type:"analogies",text:"Agua es a embudo como tierra es a ...",options:[{id:"a",text:"Criba"},{id:"b",text:"Fresadora"},{id:"c",text:"Cincel"},{id:"d",text:"Escariador"}],correctAnswer:"a"},{id:32,type:"analogies",text:"Prender es a extinguir como juntar es a ...",options:[{id:"a",text:"Separar"},{id:"b",text:"Unir"},{id:"c",text:"Apagar"},{id:"d",text:"Reducir"}],correctAnswer:"a"}]),r(!1)}catch(e){J.error("Error al cargar las preguntas del test"),r(!1)}})},[]);const V=P.useCallback(()=>d(null,null,function*(){try{const i=Object.keys(p).length,t=s.length,r=t-i;let o=0;Object.entries(p).forEach(([e,a])=>{v[e]===j[a]&&o++});const n=i-o,l=720-f,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"verbal"};if(h)try{yield Wt.saveTestResult({patientId:h,testType:"verbal",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:n}),J.success("Resultado guardado correctamente")}catch(e){J.error(`Error al guardar: ${e.message}`)}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/verbal",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}}),[p,s.length,f,a,h]);P.useEffect(()=>{if(!N||f<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),setTimeout(()=>V(),0),0):a-1)},1e3);return()=>clearInterval(e)},[N,f,V]);const y=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},E=s[c],C=!!E&&p[E.id];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-comments mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:592,columnNumber:13},void 0),"Test de Aptitud Verbal"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:591,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:595,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:590,columnNumber:9},void 0),N&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:y(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:599,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:598,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:589,columnNumber:7},void 0),t?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:609,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento verbal..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:610,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:608,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:607,columnNumber:9},void 0):N?m.jsxDEV(m.Fragment,{children:m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:715,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:E?(D=E.type,"analogies"===D?"Analogías":D):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:718,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:714,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:C?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:722,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:713,columnNumber:17},void 0),m.jsxDEV(Z,{children:E&&m.jsxDEV(m.Fragment,{children:[m.jsxDEV("div",{className:"text-gray-800 mb-6 whitespace-pre-line font-medium",children:E.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:729,columnNumber:23},void 0),m.jsxDEV("div",{className:"space-y-3",children:E.options.map(e=>m.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[E.id]===e.id&&e.id===E.correctAnswer?"bg-green-100 border-green-500":p[E.id]===e.id?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return a=E.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[E.id]===e.id?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:744,columnNumber:31},void 0),m.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:751,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:743,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:732,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:730,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:728,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:726,columnNumber:17},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:760,columnNumber:19},void 0),c<s.length-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>{c<s.length-1&&u(c+1)},children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:768,columnNumber:21},void 0):m.jsxDEV(ae,{variant:"primary",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:775,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:759,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:712,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:711,columnNumber:13},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:789,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:788,columnNumber:17},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:s.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-blue-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:794,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:792,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:813,columnNumber:23},void 0),m.jsxDEV("span",{children:[Object.keys(p).length," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:814,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:812,columnNumber:21},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/s.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:817,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:816,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:811,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:826,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:y(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:828,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:827,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:830,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:825,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:824,columnNumber:19},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full mt-2",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:836,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:791,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:787,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:786,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:710,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:709,columnNumber:9},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Verbal: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:616,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:615,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Verbal?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:621,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento verbal es la capacidad para comprender y establecer relaciones lógicas entre conceptos expresados mediante palabras. Implica entender analogías, relaciones semánticas y encontrar patrones en expresiones verbales."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:622,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en el ámbito académico y profesional, siendo especialmente relevante para carreras que requieren pensamiento lógico y analítico."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:625,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:620,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:631,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"A continuación encontrarás frases a las que les falta una palabra que ha sido sustituida por puntos suspensivos. Tu tarea consistirá en descubrir qué palabra falta para que la frase resulte verdadera y con sentido."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:632,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En cada ejercicio se proponen cuatro palabras u opciones de respuesta posibles. Entre las cuatro palabras solamente UNA es la opción correcta, la que completa mejor la frase dotándola de sentido."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:635,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:'Las frases tienen la siguiente estructura: "A es a B como C es a D". Deberás identificar la relación entre A y B, y aplicar la misma relación entre C y la palabra que falta (D).'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:638,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:630,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:644,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:[m.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo 1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:648,columnNumber:21},void 0)," ",m.jsxDEV("strong",{children:"Alto es a bajo como grande es a ..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:648,columnNumber:75},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:647,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Visible"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:651,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Gordo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:652,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"C. Pequeño"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:653,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"D. Poco"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:654,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:650,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",m.jsxDEV("strong",{children:"C. Pequeño"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:657,columnNumber:46},void 0),", porque grande y pequeño se relacionan de la misma forma que alto y bajo: son opuestos."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:656,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:646,columnNumber:17},void 0),m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:[m.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo 2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:663,columnNumber:21},void 0)," ",m.jsxDEV("strong",{children:"...?... es a estrella como tierra es a planeta."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:663,columnNumber:75},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:662,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"A. Luz"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:666,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"B. Calor"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:667,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-white p-3 rounded border border-gray-200",children:"C. Noche"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:668,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-lime-100 p-3 rounded border border-lime-300 font-medium",children:"D. Sol"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:669,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:665,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",m.jsxDEV("strong",{children:"D. Sol"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:672,columnNumber:46},void 0),', porque sol y estrella guardan entre sí la misma relación que tierra y planeta: el Sol es una estrella y la Tierra es un planeta. Fíjate igualmente en que cualquiera de las otras opciones no sería correcta; por ejemplo, en la opción B, es cierto que las estrellas producen calor, pero no tiene sentido la misma relación en las dos últimas palabras ("planeta" no produce "tierra").']},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:671,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:661,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:643,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:678,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[m.jsxDEV("li",{children:"El test consta de 32 preguntas de analogías verbales."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:680,columnNumber:19},void 0),m.jsxDEV("li",{children:["Dispondrás de ",m.jsxDEV("span",{className:"font-medium",children:"12 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:681,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:681,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:682,columnNumber:19},void 0),m.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:683,columnNumber:19},void 0),m.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:684,columnNumber:19},void 0),m.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:685,columnNumber:19},void 0),m.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:686,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:679,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:677,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:691,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:692,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:690,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:619,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:618,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{x(!0),J.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:699,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:698,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:614,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Verbal.jsx",lineNumber:588,columnNumber:5},void 0);var D},Gt=Object.freeze(Object.defineProperty({__proto__:null,default:Ht},Symbol.toStringTag,{value:"Module"}));var Jt,Yt,Kt,Xt,Zt,er,ar,ir,tr,rr,sr,or,nr,lr={exports:{}},cr={exports:{}},ur={};function dr(){return Yt||(Yt=1,cr.exports=(Jt||(Jt=1,function(){var e="function"==typeof Symbol&&Symbol.for,a=e?Symbol.for("react.element"):60103,i=e?Symbol.for("react.portal"):60106,t=e?Symbol.for("react.fragment"):60107,r=e?Symbol.for("react.strict_mode"):60108,s=e?Symbol.for("react.profiler"):60114,o=e?Symbol.for("react.provider"):60109,n=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,c=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,d=e?Symbol.for("react.suspense"):60113,m=e?Symbol.for("react.suspense_list"):60120,p=e?Symbol.for("react.memo"):60115,b=e?Symbol.for("react.lazy"):60116,f=e?Symbol.for("react.block"):60121,g=e?Symbol.for("react.fundamental"):60117,N=e?Symbol.for("react.responder"):60118,x=e?Symbol.for("react.scope"):60119;function h(e){if("object"==typeof e&&null!==e){var m=e.$$typeof;switch(m){case a:var f=e.type;switch(f){case l:case c:case t:case s:case r:case d:return f;default:var g=f&&f.$$typeof;switch(g){case n:case u:case b:case p:case o:return g;default:return m}}case i:return m}}}var v=l,j=c,V=n,y=o,E=a,C=u,D=t,B=b,w=p,A=i,R=s,S=r,T=d,I=!1;function _(e){return h(e)===c}ur.AsyncMode=v,ur.ConcurrentMode=j,ur.ContextConsumer=V,ur.ContextProvider=y,ur.Element=E,ur.ForwardRef=C,ur.Fragment=D,ur.Lazy=B,ur.Memo=w,ur.Portal=A,ur.Profiler=R,ur.StrictMode=S,ur.Suspense=T,ur.isAsyncMode=function(e){return I||(I=!0),_(e)||h(e)===l},ur.isConcurrentMode=_,ur.isContextConsumer=function(e){return h(e)===n},ur.isContextProvider=function(e){return h(e)===o},ur.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===a},ur.isForwardRef=function(e){return h(e)===u},ur.isFragment=function(e){return h(e)===t},ur.isLazy=function(e){return h(e)===b},ur.isMemo=function(e){return h(e)===p},ur.isPortal=function(e){return h(e)===i},ur.isProfiler=function(e){return h(e)===s},ur.isStrictMode=function(e){return h(e)===r},ur.isSuspense=function(e){return h(e)===d},ur.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===t||e===c||e===s||e===r||e===d||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===p||e.$$typeof===o||e.$$typeof===n||e.$$typeof===u||e.$$typeof===g||e.$$typeof===N||e.$$typeof===x||e.$$typeof===f)},ur.typeOf=h}()),ur)),cr.exports}
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/function mr(){if(Xt)return Kt;Xt=1;var e=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;return Kt=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var a={},i=0;i<10;i++)a["_"+String.fromCharCode(i)]=i;if("**********"!==Object.getOwnPropertyNames(a).map(function(e){return a[e]}).join(""))return!1;var t={};return"abcdefghijklmnopqrst".split("").forEach(function(e){t[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},t)).join("")}catch(r){return!1}}()?Object.assign:function(t,r){for(var s,o,n=function(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(t),l=1;l<arguments.length;l++){for(var c in s=Object(arguments[l]))a.call(s,c)&&(n[c]=s[c]);if(e){o=e(s);for(var u=0;u<o.length;u++)i.call(s,o[u])&&(n[o[u]]=s[o[u]])}}return n},Kt}function pr(){if(er)return Zt;er=1;return Zt="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function br(){return ir?ar:(ir=1,ar=Function.call.bind(Object.prototype.hasOwnProperty))}function fr(){if(rr)return tr;rr=1;var e,a=pr(),i={},t=br();function r(r,s,o,n,l){for(var c in r)if(t(r,c)){var u;try{if("function"!=typeof r[c]){var d=Error((n||"React class")+": "+o+" type `"+c+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof r[c]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw d.name="Invariant Violation",d}u=r[c](s,c,n,o,null,a)}catch(p){u=p}if(!u||u instanceof Error||e((n||"React class")+": type specification of "+o+" `"+c+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof u+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),u instanceof Error&&!(u.message in i)){i[u.message]=!0;var m=l?l():"";e("Failed "+o+" type: "+u.message+(null!=m?m:""))}}}return e=function(e){var a="Warning: "+e;try{throw new Error(a)}catch(i){}},r.resetWarningCache=function(){i={}},tr=r}function gr(){if(or)return sr;or=1;var e,a=dr(),i=mr(),t=pr(),r=br(),s=fr();function o(){return null}return e=function(e){var a="Warning: "+e;try{throw new Error(a)}catch(i){}},sr=function(n,l){var c="function"==typeof Symbol&&Symbol.iterator;var u="<<anonymous>>",d={array:f("array"),bigint:f("bigint"),bool:f("boolean"),func:f("function"),number:f("number"),object:f("object"),string:f("string"),symbol:f("symbol"),any:b(o),arrayOf:function(e){return b(function(a,i,r,s,o){if("function"!=typeof e)return new p("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var n=a[i];if(!Array.isArray(n))return new p("Invalid "+s+" `"+o+"` of type `"+x(n)+"` supplied to `"+r+"`, expected an array.");for(var l=0;l<n.length;l++){var c=e(n,l,r,s,o+"["+l+"]",t);if(c instanceof Error)return c}return null})},element:b(function(e,a,i,t,r){var s=e[a];return n(s)?null:new p("Invalid "+t+" `"+r+"` of type `"+x(s)+"` supplied to `"+i+"`, expected a single ReactElement.")}),elementType:b(function(e,i,t,r,s){var o=e[i];return a.isValidElementType(o)?null:new p("Invalid "+r+" `"+s+"` of type `"+x(o)+"` supplied to `"+t+"`, expected a single ReactElement type.")}),instanceOf:function(e){return b(function(a,i,t,r,s){if(!(a[i]instanceof e)){var o=e.name||u;return new p("Invalid "+r+" `"+s+"` of type `"+(((n=a[i]).constructor&&n.constructor.name?n.constructor.name:u)+"` supplied to `")+t+"`, expected instance of `"+o+"`.")}var n;return null})},node:b(function(e,a,i,t,r){return N(e[a])?null:new p("Invalid "+t+" `"+r+"` supplied to `"+i+"`, expected a ReactNode.")}),objectOf:function(e){return b(function(a,i,s,o,n){if("function"!=typeof e)return new p("Property `"+n+"` of component `"+s+"` has invalid PropType notation inside objectOf.");var l=a[i],c=x(l);if("object"!==c)return new p("Invalid "+o+" `"+n+"` of type `"+c+"` supplied to `"+s+"`, expected an object.");for(var u in l)if(r(l,u)){var d=e(l,u,s,o,n+"."+u,t);if(d instanceof Error)return d}return null})},oneOf:function(a){if(!Array.isArray(a))return e(arguments.length>1?"Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).":"Invalid argument supplied to oneOf, expected an array."),o;return b(function(e,i,t,r,s){for(var o=e[i],n=0;n<a.length;n++)if(m(o,a[n]))return null;var l=JSON.stringify(a,function(e,a){return"symbol"===h(a)?String(a):a});return new p("Invalid "+r+" `"+s+"` of value `"+String(o)+"` supplied to `"+t+"`, expected one of "+l+".")})},oneOfType:function(a){if(!Array.isArray(a))return e("Invalid argument supplied to oneOfType, expected an instance of array."),o;for(var i=0;i<a.length;i++){var s=a[i];if("function"!=typeof s)return e("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+v(s)+" at index "+i+"."),o}return b(function(e,i,s,o,n){for(var l=[],c=0;c<a.length;c++){var u=(0,a[c])(e,i,s,o,n,t);if(null==u)return null;u.data&&r(u.data,"expectedType")&&l.push(u.data.expectedType)}return new p("Invalid "+o+" `"+n+"` supplied to `"+s+"`"+(l.length>0?", expected one of type ["+l.join(", ")+"]":"")+".")})},shape:function(e){return b(function(a,i,r,s,o){var n=a[i],l=x(n);if("object"!==l)return new p("Invalid "+s+" `"+o+"` of type `"+l+"` supplied to `"+r+"`, expected `object`.");for(var c in e){var u=e[c];if("function"!=typeof u)return g(r,s,o,c,h(u));var d=u(n,c,r,s,o+"."+c,t);if(d)return d}return null})},exact:function(e){return b(function(a,s,o,n,l){var c=a[s],u=x(c);if("object"!==u)return new p("Invalid "+n+" `"+l+"` of type `"+u+"` supplied to `"+o+"`, expected `object`.");var d=i({},a[s],e);for(var m in d){var b=e[m];if(r(e,m)&&"function"!=typeof b)return g(o,n,l,m,h(b));if(!b)return new p("Invalid "+n+" `"+l+"` key `"+m+"` supplied to `"+o+"`.\nBad object: "+JSON.stringify(a[s],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(e),null,"  "));var f=b(c,m,o,n,l+"."+m,t);if(f)return f}return null})}};function m(e,a){return e===a?0!==e||1/e==1/a:e!=e&&a!=a}function p(e,a){this.message=e,this.data=a&&"object"==typeof a?a:{},this.stack=""}function b(a){var i={},r=0;function s(s,o,n,c,d,m,b){if(c=c||u,m=m||n,b!==t){if(l){var f=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw f.name="Invariant Violation",f}if("undefined"!=typeof console){var g=c+":"+n;!i[g]&&r<3&&(e("You are manually calling a React.PropTypes validation function for the `"+m+"` prop on `"+c+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),i[g]=!0,r++)}}return null==o[n]?s?null===o[n]?new p("The "+d+" `"+m+"` is marked as required in `"+c+"`, but its value is `null`."):new p("The "+d+" `"+m+"` is marked as required in `"+c+"`, but its value is `undefined`."):null:a(o,n,c,d,m)}var o=s.bind(null,!1);return o.isRequired=s.bind(null,!0),o}function f(e){return b(function(a,i,t,r,s,o){var n=a[i];return x(n)!==e?new p("Invalid "+r+" `"+s+"` of type `"+h(n)+"` supplied to `"+t+"`, expected `"+e+"`.",{expectedType:e}):null})}function g(e,a,i,t,r){return new p((e||"React class")+": "+a+" type `"+i+"."+t+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+r+"`.")}function N(e){switch(typeof e){case"number":case"string":case"undefined":return!0;case"boolean":return!e;case"object":if(Array.isArray(e))return e.every(N);if(null===e||n(e))return!0;var a=function(e){var a=e&&(c&&e[c]||e["@@iterator"]);if("function"==typeof a)return a}(e);if(!a)return!1;var i,t=a.call(e);if(a!==e.entries){for(;!(i=t.next()).done;)if(!N(i.value))return!1}else for(;!(i=t.next()).done;){var r=i.value;if(r&&!N(r[1]))return!1}return!0;default:return!1}}function x(e){var a=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":function(e,a){return"symbol"===e||!!a&&("Symbol"===a["@@toStringTag"]||"function"==typeof Symbol&&a instanceof Symbol)}(a,e)?"symbol":a}function h(e){if(null==e)return""+e;var a=x(e);if("object"===a){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return a}function v(e){var a=h(e);switch(a){case"array":case"object":return"an "+a;case"boolean":case"date":case"regexp":return"a "+a;default:return a}}return p.prototype=Error.prototype,d.checkPropTypes=s,d.resetWarningCache=s.resetWarningCache,d.PropTypes=d,d},sr}function Nr(){if(nr)return lr.exports;nr=1;var e=dr();return lr.exports=gr()(e.isElement,true),lr.exports}const xr=z(Nr());xr.number.isRequired,xr.number.isRequired,xr.object.isRequired,xr.func.isRequired,xr.number,xr.number.isRequired,xr.func.isRequired;const hr=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(0),[s,o]=P.useState({}),[c,u]=P.useState(!1),{timeRemaining:p,startTimer:b,stopTimer:f,formatTime:g}=(e=>{const[a,i]=P.useState(e),[t,r]=P.useState(!1),s=P.useCallback(()=>{r(!0)},[]),o=P.useCallback(()=>{r(!1)},[]),n=P.useCallback(()=>{i(e),r(!1)},[e]),l=P.useCallback(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`},[]);return P.useEffect(()=>{let e;return t&&a>0?e=setInterval(()=>{i(e=>e-1)},1e3):0===a&&r(!1),()=>{e&&clearInterval(e)}},[t,a]),{timeRemaining:a,isRunning:t,startTimer:s,stopTimer:o,resetTimer:n,formatTime:l}})(600),N=null==(e=i.state)?void 0:e.patientId;P.useEffect(()=>(b(),()=>{f()}),[b,f]),P.useEffect(()=>{0===p&&h()},[p]);const x=[{id:1,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"reloj"},{id:"B",text:"reciclaje"},{id:"C",text:"reyna"},{id:"D",text:"nube"}],correctAnswer:"C"},{id:2,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hola"},{id:"B",text:"Zoo"},{id:"C",text:"ambos"},{id:"D",text:"vallena"}],correctAnswer:"D"},{id:3,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"adibinar"},{id:"B",text:"inmediato"},{id:"C",text:"gestar"},{id:"D",text:"anchoa"}],correctAnswer:"A"},{id:4,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"herrero"},{id:"B",text:"saver"},{id:"C",text:"cerrar"},{id:"D",text:"honrado"}],correctAnswer:"B"},{id:5,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"éxtasis"},{id:"B",text:"cesta"},{id:"C",text:"ademas"},{id:"D",text:"llevar"}],correctAnswer:"C"},{id:6,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"avión"},{id:"B",text:"abrir"},{id:"C",text:"favor"},{id:"D",text:"espionage"}],correctAnswer:"D"},{id:7,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"insecto"},{id:"B",text:"jota"},{id:"C",text:"habrigo"},{id:"D",text:"extraño"}],correctAnswer:"C"},{id:8,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hacha"},{id:"B",text:"oler"},{id:"C",text:"polbo"},{id:"D",text:"abril"}],correctAnswer:"C"},{id:9,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"amartillar"},{id:"B",text:"desacer"},{id:"C",text:"exageración"},{id:"D",text:"humildad"}],correctAnswer:"B"},{id:10,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"bendige"},{id:"B",text:"bifurcación"},{id:"C",text:"amarrar"},{id:"D",text:"país"}],correctAnswer:"A"},{id:11,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"horrible"},{id:"B",text:"llacimiento"},{id:"C",text:"inmóvil"},{id:"D",text:"enredar"}],correctAnswer:"B"},{id:12,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"zebra"},{id:"B",text:"impaciente"},{id:"C",text:"alrededor"},{id:"D",text:"mayor"}],correctAnswer:"A"},{id:13,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hormona"},{id:"B",text:"jirafa"},{id:"C",text:"desván"},{id:"D",text:"enpañar"}],correctAnswer:"D"},{id:14,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"abdicar"},{id:"B",text:"area"},{id:"C",text:"ombligo"},{id:"D",text:"extinguir"}],correctAnswer:"B"},{id:15,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"júbilo"},{id:"B",text:"lúz"},{id:"C",text:"quince"},{id:"D",text:"hilera"}],correctAnswer:"B"},{id:16,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"inexorable"},{id:"B",text:"coraje"},{id:"C",text:"ingerir"},{id:"D",text:"hunir"}],correctAnswer:"D"},{id:17,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"aereo"},{id:"B",text:"conserje"},{id:"C",text:"drástico"},{id:"D",text:"ataviar"}],correctAnswer:"A"},{id:18,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"grave"},{id:"B",text:"abrumar"},{id:"C",text:"contración"},{id:"D",text:"enmienda"}],correctAnswer:"C"},{id:19,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"hay"},{id:"B",text:"gemido"},{id:"C",text:"carácter"},{id:"D",text:"harpón"}],correctAnswer:"D"},{id:20,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"embarcar"},{id:"B",text:"ambiguo"},{id:"C",text:"arroyo"},{id:"D",text:"esotérico"}],correctAnswer:"D"},{id:21,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"léntamente"},{id:"B",text:"utopía"},{id:"C",text:"aprensivo"},{id:"D",text:"irascible"}],correctAnswer:"A"},{id:22,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"desahogar"},{id:"B",text:"córnea"},{id:"C",text:"convenido"},{id:"D",text:"azúl"}],correctAnswer:"D"},{id:23,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"próspero"},{id:"B",text:"fué"},{id:"C",text:"regencia"},{id:"D",text:"pelaje"}],correctAnswer:"B"},{id:24,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"savia"},{id:"B",text:"ciénaga"},{id:"C",text:"andamiage"},{id:"D",text:"inmediatamente"}],correctAnswer:"C"},{id:25,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"traspié"},{id:"B",text:"urón"},{id:"C",text:"embellecer"},{id:"D",text:"vasija"}],correctAnswer:"B"},{id:26,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"río"},{id:"B",text:"barar"},{id:"C",text:"hiena"},{id:"D",text:"buhardilla"}],correctAnswer:"B"},{id:27,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"sátira"},{id:"B",text:"crujir"},{id:"C",text:"subrayar"},{id:"D",text:"extrategia"}],correctAnswer:"D"},{id:28,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"dátil"},{id:"B",text:"imágen"},{id:"C",text:"geranio"},{id:"D",text:"anteojo"}],correctAnswer:"B"},{id:29,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"incisivo"},{id:"B",text:"baya"},{id:"C",text:"impío"},{id:"D",text:"arnes"}],correctAnswer:"D"},{id:30,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"jersey"},{id:"B",text:"berengena"},{id:"C",text:"exhibir"},{id:"D",text:"atestar"}],correctAnswer:"B"},{id:31,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"versátil"},{id:"B",text:"hogaza"},{id:"C",text:"vadear"},{id:"D",text:"hurraca"}],correctAnswer:"D"},{id:32,text:"Identifica la palabra mal escrita:",options:[{id:"A",text:"exacerbar"},{id:"B",text:"leído"},{id:"C",text:"hayar"},{id:"D",text:"hostil"}],correctAnswer:"C"}],h=()=>d(null,null,function*(){try{f(),u(!0);const i=Object.keys(s).length,t=x.length,r=t-i;let o=0;Object.entries(s).forEach(([e,a])=>{const i=x.find(a=>a.id===parseInt(e));i&&i.correctAnswer===a&&o++});const n=i-o,l=600-p,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"ortografia"};if(N)try{yield Wt.saveTestResult({patientId:N,testType:"ortografia",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:s,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/ortografia",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}});Object.keys(s).length,x.length;const v=x[t],j=void 0!==s[v.id];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-spell-check mr-2 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:508,columnNumber:13},void 0),"Test de Ortografía"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:507,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Identificación de palabras con errores ortográficos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:511,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:506,columnNumber:9},void 0),m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:g(p)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:514,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:513,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:505,columnNumber:7},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV("div",{className:"bg-white rounded-lg shadow border border-gray-200",children:[m.jsxDEV("div",{className:"p-4 border-b border-gray-200",children:m.jsxDEV("div",{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800",children:["Pregunta ",t+1," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:526,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:"Ortografía"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:527,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:525,columnNumber:15},void 0),m.jsxDEV("div",{className:"text-sm font-medium text-gray-500",children:j?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:529,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:524,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:523,columnNumber:11},void 0),m.jsxDEV("div",{className:"p-6",children:[m.jsxDEV("p",{className:"text-lg font-medium text-gray-800 mb-6",children:v.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:536,columnNumber:13},void 0),m.jsxDEV("div",{className:"space-y-3",children:v.options.map(e=>m.jsxDEV("button",{className:"w-full text-left p-4 rounded-lg border "+(s[v.id]===e.id?"bg-blue-50 border-blue-500":"bg-white border-gray-200 hover:bg-gray-50"),onClick:()=>{return a=v.id,i=e.id,void o(e=>l(n({},e),{[a]:i}));var a,i},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-8 h-8 flex items-center justify-center rounded-full border mr-3 "+(s[v.id]===e.id?"bg-blue-500 text-white border-blue-500":"text-gray-500 border-gray-300"),children:e.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:552,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-gray-800 font-medium",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:559,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:551,columnNumber:19},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:542,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:540,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:535,columnNumber:11},void 0),m.jsxDEV("div",{className:"px-6 py-4 border-t border-gray-200 flex justify-between",children:[m.jsxDEV("button",{onClick:()=>{t>0&&r(t-1)},disabled:0===t,className:"px-4 py-2 rounded-md "+(0===t?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"),children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:567,columnNumber:13},void 0),m.jsxDEV("button",{onClick:t<x.length-1?()=>{t<x.length-1&&r(t+1)}:h,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:t<x.length-1?"Siguiente":"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:578,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:566,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:522,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:521,columnNumber:9},void 0),m.jsxDEV("div",{children:m.jsxDEV("div",{className:"bg-white rounded-lg shadow border border-gray-200 sticky top-6",children:[m.jsxDEV("div",{className:"p-4 border-b border-gray-200",children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:592,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:591,columnNumber:13},void 0),m.jsxDEV("div",{className:"p-4",children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:x.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(t===a?"bg-blue-500 text-white":void 0!==s[x[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>r(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:597,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:595,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:616,columnNumber:19},void 0),m.jsxDEV("span",{children:[Object.keys(s).length," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:617,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:615,columnNumber:17},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(s).length/x.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:620,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:619,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:614,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:629,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:g(p)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:631,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:630,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:633,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:628,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:627,columnNumber:15},void 0),m.jsxDEV("button",{onClick:h,className:"w-full mt-2 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium",children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:639,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:594,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:590,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:589,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:520,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Ortografia.jsx",lineNumber:504,columnNumber:5},void 0)},vr=Object.freeze(Object.defineProperty({__proto__:null,default:hr},Symbol.toStringTag,{value:"Module"})),jr=e=>{let a=e.startsWith("/")?e.slice(1):e;return a.startsWith("assets/images/")?a=a.replace("assets/images/",""):a.startsWith("images/")&&(a=a.replace("images/","")),(e=>{const a=e.startsWith("/")?e.slice(1):e,i="/Bat-7-Version-fial-para-github/";return a.startsWith("assets/")?`${i}${a}`:`${i}assets/${a}`})(`images/${a}`)},Vr=(e,a)=>jr(`${e}/${a}`),yr=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(!0),[s,o]=P.useState([]),[c,u]=P.useState(0),[p,b]=P.useState({}),[f,g]=P.useState(1200),[N,x]=P.useState(!1),h=null==(e=i.state)?void 0:e.patientId,v={1:"4",2:"4",3:"4",4:"3",5:"2",6:"4",7:"3",8:"3",9:"1",10:"4",11:"3",12:"1",13:"2",14:"2",15:"3",16:"2",17:"1",18:"3",19:"3",20:"4",21:"3",22:"2",23:"2",24:"1",25:"3",26:"1",27:"1",28:"1",29:"3",30:"4",31:"3",32:"2"},j={1:"a",2:"b",3:"c",4:"d"},V={a:"1",b:"2",c:"3",d:"4"};P.useEffect(()=>{d(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:32},(e,a)=>({id:a+1,type:"series",imagePath:Vr("razonamiento",`Racionamiento${a+1}.png`),options:[{id:"a",text:"Opción A"},{id:"b",text:"Opción B"},{id:"c",text:"Opción C"},{id:"d",text:"Opción D"}],correctAnswer:j[v[a+1]]}));o(e),r(!1)}catch(e){J.error("Error al cargar las preguntas del test"),r(!1)}})},[]),P.useEffect(()=>{if(!N||f<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),y(),0):a-1)},1e3);return()=>clearInterval(e)},[N,f]);const y=()=>d(null,null,function*(){try{const i=Object.keys(p).length,t=s.length,r=t-i;let o=0;Object.entries(p).forEach(([e,a])=>{v[e]===V[a]&&o++});const n=i-o,l=1200-f,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"razonamiento"};if(h)try{yield Wt.saveTestResult({patientId:h,testType:"razonamiento",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/razonamiento",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}}),E=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},C=s[c],D=!!C&&p[C.id];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-puzzle-piece mr-2 text-amber-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:208,columnNumber:13},void 0),"Test de Razonamiento"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:207,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Continuar series lógicas de figuras"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:211,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:206,columnNumber:9},void 0),N&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:E(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:215,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:214,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:205,columnNumber:7},void 0),t?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:225,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:226,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:224,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:223,columnNumber:9},void 0):N?m.jsxDEV(m.Fragment,{children:m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:319,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:C?(B=C.type,"series"===B?"Series":B):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:322,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:318,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:D?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:326,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:317,columnNumber:17},void 0),m.jsxDEV(Z,{children:C&&m.jsxDEV(m.Fragment,{children:[m.jsxDEV("div",{className:"flex justify-center mb-6",children:m.jsxDEV("img",{src:C.imagePath,alt:`Pregunta ${C.id}`,className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:334,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:333,columnNumber:23},void 0),m.jsxDEV("div",{className:"space-y-3",children:C.options.map(e=>m.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[C.id]===e.id?"bg-amber-50 border-amber-500":"hover:bg-gray-50"),onClick:()=>{return a=C.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[C.id]===e.id?"bg-amber-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:352,columnNumber:31},void 0),m.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:359,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:351,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:342,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:340,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:332,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:330,columnNumber:17},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:368,columnNumber:19},void 0),c<s.length-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>{c<s.length-1&&u(c+1)},className:"bg-amber-600 hover:bg-amber-700",children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:376,columnNumber:21},void 0):m.jsxDEV(ae,{variant:"primary",onClick:y,className:"bg-amber-600 hover:bg-amber-700",children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:384,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:367,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:316,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:315,columnNumber:13},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:399,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:398,columnNumber:17},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:s.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-amber-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:404,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:402,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:423,columnNumber:23},void 0),m.jsxDEV("span",{children:[Object.keys(p).length," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:424,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:422,columnNumber:21},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/s.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:427,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:426,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:421,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-amber-50 p-3 rounded-lg border border-amber-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-amber-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:436,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:E(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:438,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:437,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:440,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:435,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:434,columnNumber:19},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full mt-2 bg-amber-600 hover:bg-amber-700",onClick:y,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:446,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:401,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:397,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:396,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:314,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:313,columnNumber:9},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:232,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:231,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:237,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento es la capacidad para identificar patrones, relaciones y reglas lógicas en series de figuras o dibujos. Esta habilidad es fundamental para resolver problemas, tomar decisiones y aprender nuevos conceptos."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:238,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:236,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:244,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En esta prueba se trabaja con series de figuras o dibujos, ordenados de acuerdo con una ley. Tu tarea consistirá en averiguar la ley que ordena las figuras y elegir entre las opciones de respuesta la que continúa la serie."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:245,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En todos los ejercicios se presenta la serie en la parte superior y las opciones de respuesta en la parte inferior. Cuando hayas decidido qué opción es la única correcta, selecciona la letra correspondiente (A, B, C o D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:248,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:243,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-orange-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:254,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:m.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo R1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:258,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:257,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-center mb-4",children:m.jsxDEV("img",{src:"/assets/images/razonamiento/R1.png",alt:"Ejemplo R1",className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:261,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:260,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:"En este ejemplo se presenta una figura que va girando 90 grados hacia la derecha de una casilla a otra. ¿Cuál debería ser la próxima figura de la serie?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:263,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es la ",m.jsxDEV("strong",{children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:267,columnNumber:49},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:266,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:256,columnNumber:17},void 0),m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:m.jsxDEV("strong",{className:"text-blue-600",children:"Ejemplo R2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:273,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:272,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-center mb-4",children:m.jsxDEV("img",{src:"/assets/images/razonamiento/R2.png",alt:"Ejemplo R2",className:"max-w-full h-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:276,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:275,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:271,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:253,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:282,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[m.jsxDEV("li",{children:"El test consta de 32 preguntas de series lógicas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:284,columnNumber:19},void 0),m.jsxDEV("li",{children:["Dispondrás de ",m.jsxDEV("span",{className:"font-medium",children:"20 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:285,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:285,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:286,columnNumber:19},void 0),m.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:287,columnNumber:19},void 0),m.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:288,columnNumber:19},void 0),m.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:289,columnNumber:19},void 0),m.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:290,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:283,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:281,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:295,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:296,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:294,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:235,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:234,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{x(!0),J.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2 bg-amber-600 hover:bg-amber-700",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:303,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:302,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:230,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Razonamiento.jsx",lineNumber:204,columnNumber:5},void 0);var B},Er=Object.freeze(Object.defineProperty({__proto__:null,default:yr},Symbol.toStringTag,{value:"Module"})),Cr=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(!0),[s,o]=P.useState([]),[c,u]=P.useState(0),[p,b]=P.useState({}),[f,g]=P.useState(480),[N,x]=P.useState(!1),h=null==(e=i.state)?void 0:e.patientId,v=10,j={1:3,2:3,3:2,4:1,5:2,6:3,7:2,8:2,9:4,10:2,11:4,12:1,13:4,14:2,15:4,16:2,17:2,18:3,19:2,20:3,21:4,22:2,23:3,24:2,25:3,26:3,27:1,28:2,29:1,30:2,31:3,32:3,33:4,34:1,35:4,36:3,37:1,38:2,39:4,40:1,41:1,42:4,43:2,44:3,45:2,46:1,47:2,48:3,49:1,50:3,51:1,52:4,53:1,54:1,55:1,56:3,57:3,58:2,59:1,60:4,61:4,62:3,63:2,64:3,65:2,66:4,67:3,68:1,69:2,70:4,71:3,72:3,73:3,74:1,75:1,76:2,77:2,78:4,79:1,80:1};P.useEffect(()=>{d(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:80},(e,a)=>({id:a+1,type:"attention",imageUrl:Vr("atencion",`Atencion${a+1}.png`),options:[{id:"0",text:"0 veces"},{id:"1",text:"1 vez"},{id:"2",text:"2 veces"},{id:"3",text:"3 veces"},{id:"4",text:"4 veces"}],correctAnswer:j[a+1]?j[a+1].toString():"0"}));o(e),r(!1)}catch(e){J.error("Error al cargar las preguntas del test"),r(!1)}})},[]),P.useEffect(()=>{if(!N||f<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),V(),0):a-1)},1e3);return()=>clearInterval(e)},[N,f]);const V=()=>d(null,null,function*(){try{const i=Object.keys(p).length,t=s.length,r=t-i;let o=0;Object.entries(p).forEach(([e,a])=>{const i=s.find(a=>a.id.toString()===e);i&&a===i.correctAnswer&&o++});const n=i-o,l=480-f,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"atencion"};if(h)try{yield Wt.saveTestResult({patientId:h,testType:"atencion",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/atencion",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}}),y=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},E=Math.ceil(s.length/v);return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-eye mr-2 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:204,columnNumber:13},void 0),"Test de Atención y Concentración"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:203,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Localización de símbolos específicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:207,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:202,columnNumber:9},void 0),N&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:y(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:211,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:210,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:201,columnNumber:7},void 0),t?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:221,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de atención..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:222,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:220,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:219,columnNumber:9},void 0):N?s.length>0?m.jsxDEV(m.Fragment,{children:m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:[m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Página ",c+1," de ",E]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:320,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:["Preguntas ",c*v+1," - ",Math.min((c+1)*v,s.length)," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:323,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:319,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:["Tiempo restante: ",y(f)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:327,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:318,columnNumber:17},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-8",children:(()=>{const e=c*v,a=e+v;return s.slice(e,a)})().map(e=>m.jsxDEV("div",{className:"border-b pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0",children:m.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-start gap-4",children:[m.jsxDEV("div",{className:"md:w-3/4",children:[m.jsxDEV("h3",{className:"text-md font-medium mb-3",children:["Pregunta ",e.id]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:337,columnNumber:25},void 0),m.jsxDEV("div",{className:"mb-4",children:m.jsxDEV("img",{src:e.imageUrl,alt:`Pregunta ${e.id}`,className:"w-full max-w-2xl h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x100?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:339,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:338,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"¿Cuántas veces aparece el símbolo modelo en esta fila?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:349,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:336,columnNumber:23},void 0),m.jsxDEV("div",{className:"md:w-1/4",children:m.jsxDEV("div",{className:"space-y-2",children:e.options.map(a=>m.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[e.id]===a.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return i=e.id,t=a.id,void b(l(n({},p),{[i]:t}));var i,t},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[e.id]===a.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:a.id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:364,columnNumber:33},void 0),m.jsxDEV("div",{className:"text-gray-700",children:a.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:371,columnNumber:33},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:363,columnNumber:31},void 0)},a.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:354,columnNumber:29},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:352,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:351,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:335,columnNumber:21},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:334,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:332,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:331,columnNumber:13},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>{c>0&&(u(c-1),window.scrollTo(0,0))},disabled:0===c,children:"Página Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:383,columnNumber:15},void 0),m.jsxDEV("div",{className:"flex space-x-2",children:[Array.from({length:E},(e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full text-sm "+(c===a?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),onClick:()=>u(a),children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:392,columnNumber:19},void 0)).slice(Math.max(0,c-2),Math.min(E,c+3)),c+3<E&&m.jsxDEV("span",{className:"self-center",children:"..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:404,columnNumber:50},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:390,columnNumber:15},void 0),c<E-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>{(c+1)*v<s.length&&(u(c+1),window.scrollTo(0,0))},children:"Página Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:407,columnNumber:17},void 0):m.jsxDEV(ae,{variant:"primary",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:414,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:382,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:317,columnNumber:15},void 0),m.jsxDEV("div",{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"text-sm text-gray-600",children:["Has respondido ",Object.keys(p).length," de ",s.length," preguntas (",Math.round(Object.keys(p).length/s.length*100),"%)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:426,columnNumber:15},void 0),m.jsxDEV("div",{className:"w-64 bg-gray-200 rounded-full h-2 mt-1",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/s.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:430,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:429,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:425,columnNumber:13},void 0),m.jsxDEV(ae,{variant:"primary",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:436,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:424,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:316,columnNumber:13},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:449,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:448,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:s.map((e,a)=>{const i=Math.floor(a/v),t=i===c;return m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(p[e.id]?"bg-green-100 text-green-800 border border-green-300":t?"bg-blue-100 text-blue-800 border border-blue-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>{const e=Math.floor(a/v);u(e)},title:`Pregunta ${a+1} - Página ${i+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:457,columnNumber:21},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:452,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[m.jsxDEV("div",{className:"text-sm text-gray-600 mb-2",children:["Progreso: ",Object.keys(p).length,"/",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:479,columnNumber:17},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-3",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:Object.keys(p).length/s.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:483,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:482,columnNumber:17},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full",onClick:V,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:489,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:478,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:451,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:447,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:446,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:315,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:314,columnNumber:9},void 0):m.jsxDEV(K,{children:m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"py-8 text-center",children:[m.jsxDEV("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:506,columnNumber:15},void 0),m.jsxDEV(ae,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:507,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:505,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:504,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:503,columnNumber:9},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Test de Atención: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:228,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:227,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Test de Atención?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:233,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El test de atención evalúa tu capacidad para mantener la concentración y detectar estímulos específicos entre un conjunto de elementos similares. Esta habilidad es fundamental para el aprendizaje, el trabajo y muchas actividades cotidianas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:234,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Esta prueba mide específicamente tu atención selectiva, velocidad perceptiva, discriminación visual y capacidad de concentración sostenida."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:237,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:232,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:243,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"Esta prueba trata de evaluar tu rapidez y tu precisión trabajando con símbolos. En cada ejercicio aparece una fila con diferentes símbolos y tu tarea consistirá en localizar cuántas veces aparece uno determinado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:244,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"El símbolo que tienes que localizar es siempre el mismo y se presenta en la parte superior de la página; en cada ejercicio puede aparecer 0, 1, 2, 3 o 4 veces."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:247,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Deberás seleccionar cuántas veces aparece el símbolo en cada fila (0, 1, 2, 3 o 4) asegurándote de que tu respuesta se corresponda con el número del ejercicio que estás contestando."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:250,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:242,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:256,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"flex justify-center mb-3",children:m.jsxDEV("img",{src:Vr("atencion","Atencion.png"),alt:"Ejemplos de atención",className:"max-w-md h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/300x150?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:260,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:259,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:[m.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:271,columnNumber:21},void 0)," El símbolo del óvalo aparece una única vez, y es el tercer símbolo de la fila. Por eso la respuesta correcta es ",m.jsxDEV("strong",{children:"1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:271,columnNumber:190},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:270,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-2",children:[m.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:274,columnNumber:21},void 0)," En esta ocasión no hay ningún símbolo que coincida exactamente con el modelo; por tanto la respuesta correcta es ",m.jsxDEV("strong",{children:"0"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:274,columnNumber:191},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:273,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-2",children:[m.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo A3:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:277,columnNumber:21},void 0)," El símbolo del óvalo aparece en dos ocasiones, en primera y quinta posición. Por eso, la respuesta correcta es ",m.jsxDEV("strong",{children:"2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:277,columnNumber:189},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:276,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:258,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:255,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:283,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[m.jsxDEV("li",{children:"El test consta de 80 preguntas de atención."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:285,columnNumber:19},void 0),m.jsxDEV("li",{children:["Dispondrás de ",m.jsxDEV("span",{className:"font-medium",children:"8 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:286,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:286,columnNumber:19},void 0),m.jsxDEV("li",{children:"Las preguntas se presentan en páginas de 10 preguntas cada una."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:287,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las páginas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:288,columnNumber:19},void 0),m.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:289,columnNumber:19},void 0),m.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:290,columnNumber:19},void 0),m.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:291,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:284,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:282,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:296,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:297,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:295,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:231,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:230,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{x(!0),J.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:304,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:303,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:226,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Atencion.jsx",lineNumber:200,columnNumber:5},void 0)},Dr=Object.freeze(Object.defineProperty({__proto__:null,default:Cr},Symbol.toStringTag,{value:"Module"})),Br=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(!0),[s,o]=P.useState([]),[c,u]=P.useState(0),[p,b]=P.useState({}),[f,g]=P.useState(900),[N,x]=P.useState(!1),h=null==(e=i.state)?void 0:e.patientId,v={1:"C",2:"D",3:"B",4:"A",5:"A",6:"A",7:"D",8:"B",9:"D",10:"D",11:"C",12:"A",13:"D",14:"A",15:"A",16:"B",17:"C",18:"A",19:"C",20:"D",21:"D",22:"C",23:"D",24:"B",25:"C",26:"C",27:"D",28:"C"};P.useEffect(()=>{d(null,null,function*(){try{yield new Promise(e=>setTimeout(e,800));const e=Array.from({length:28},(e,a)=>({id:a+1,type:"spatial",imageUrl:Vr("espacial",`Espacial${a+1}.png`),options:[{id:"A",text:"Opción A"},{id:"B",text:"Opción B"},{id:"C",text:"Opción C"},{id:"D",text:"Opción D"}],correctAnswer:v[a+1]}));o(e),r(!1)}catch(e){J.error("Error al cargar las preguntas del test"),r(!1)}})},[]),P.useEffect(()=>{if(!N||f<=0)return;const e=setInterval(()=>{g(a=>a<=1?(clearInterval(e),j(),0):a-1)},1e3);return()=>clearInterval(e)},[N,f]);const j=()=>d(null,null,function*(){try{const i=Object.keys(p).length,t=s.length,r=t-i;let o=0;Object.entries(p).forEach(([e,a])=>{const i=s.find(a=>a.id.toString()===e);i&&a===i.correctAnswer&&o++});const n=i-o,l=900-f,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"espacial"};if(h)try{yield Wt.saveTestResult({patientId:h,testType:"espacial",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:p,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/espacial",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}}),V=e=>{const a=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`},y=s[c],E=!!y&&p[y.id];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-cube mr-2 text-indigo-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:189,columnNumber:13},void 0),"Test de Aptitud Espacial"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:188,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Razonamiento espacial con cubos y redes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:192,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:187,columnNumber:9},void 0),N&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:V(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:196,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:195,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:186,columnNumber:7},void 0),t?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:206,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando test de razonamiento espacial..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:207,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:205,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:204,columnNumber:9},void 0):N?s.length>0?m.jsxDEV(m.Fragment,{children:m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",c+1," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:322,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:y?(C=y.type,"spatial"===C?"Razonamiento Espacial":C):""},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:325,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:321,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:E?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:329,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:320,columnNumber:17},void 0),m.jsxDEV(Z,{children:y&&m.jsxDEV(m.Fragment,{children:[m.jsxDEV("div",{className:"flex justify-center mb-6",children:m.jsxDEV("img",{src:y.imageUrl,alt:`Pregunta ${c+1}`,className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/600x300?text=Imagen+no+disponible"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:337,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:336,columnNumber:23},void 0),m.jsxDEV("div",{className:"space-y-3",children:y.options.map(e=>m.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(p[y.id]===e.id?"bg-indigo-50 border-indigo-500":"hover:bg-gray-50"),onClick:()=>{return a=y.id,i=e.id,void b(l(n({},p),{[a]:i}));var a,i},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(p[y.id]===e.id?"bg-indigo-500 text-white":"bg-gray-200 text-gray-700"),children:e.id.toUpperCase()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:359,columnNumber:31},void 0),m.jsxDEV("div",{className:"text-gray-700",children:e.text},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:366,columnNumber:31},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:358,columnNumber:29},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:349,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:347,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:335,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:333,columnNumber:17},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>{c>0&&u(c-1)},disabled:0===c,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:375,columnNumber:19},void 0),c<s.length-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>{c<s.length-1&&u(c+1)},children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:383,columnNumber:21},void 0):m.jsxDEV(ae,{variant:"primary",onClick:j,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:390,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:374,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:319,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:318,columnNumber:13},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:404,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:403,columnNumber:17},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2 max-h-64 overflow-y-auto p-1",children:s.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(c===a?"bg-indigo-500 text-white":p[e.id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>u(a),title:`Pregunta ${a+1}`,children:a+1},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:409,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:407,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:428,columnNumber:23},void 0),m.jsxDEV("span",{children:[Object.keys(p).length," de ",s.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:429,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:427,columnNumber:21},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(p).length/s.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:432,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:431,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:426,columnNumber:19},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-indigo-50 p-3 rounded-lg border border-indigo-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-indigo-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:441,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:V(f)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:443,columnNumber:42},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:442,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:445,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:440,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:439,columnNumber:19},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full mt-2",onClick:j,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:451,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:406,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:402,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:401,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:317,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:316,columnNumber:9},void 0):m.jsxDEV(K,{children:m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"py-8 text-center",children:[m.jsxDEV("p",{className:"text-gray-500",children:"No se encontraron preguntas para este test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:467,columnNumber:15},void 0),m.jsxDEV(ae,{variant:"outline",className:"mt-4",onClick:()=>a("/student/tests"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:468,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:466,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:465,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:464,columnNumber:9},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Razonamiento Espacial: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:213,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:212,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"¿Qué es el Razonamiento Espacial?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:218,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-2",children:"El razonamiento espacial es la capacidad para visualizar y manipular objetos mentalmente en el espacio tridimensional. Implica entender cómo se relacionan las formas y los objetos entre sí, y cómo se transforman cuando cambian de posición o perspectiva."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:219,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Esta habilidad es fundamental en campos como la arquitectura, ingeniería, diseño, matemáticas y ciencias, siendo especialmente relevante para carreras que requieren visualización y manipulación de objetos en el espacio."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:222,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:217,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Instrucciones del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:228,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"En este test encontrarás un cubo junto con un modelo desplegado del mismo cubo. En el modelo desplegado falta una cara, marcada con un signo de interrogación (?)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:229,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-3",children:"Tu tarea consistirá en averiguar qué opción (A, B, C o D) debería aparecer en lugar del interrogante para que el modelo desplegado corresponda al cubo cuando se pliega."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:232,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Para facilitar tu tarea, en el cubo se han representado en color gris los números o letras que se encuentran en las caras de atrás (las que no se ven directamente)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:235,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:227,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-slate-200 p-4 rounded-lg border border-slate-300",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-indigo-700 mb-2",children:"Ejemplos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:241,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:m.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo 1:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:245,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:244,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-center mb-3",children:m.jsxDEV("img",{src:Vr("espacial","Modelo Espacial.png"),alt:"Ejemplo 1",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+1"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:248,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:247,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",m.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:259,columnNumber:46},void 0),". Si se sustituye el interrogante por la letra «h» y se pliegan las caras del modelo hasta formar el cubo, este se corresponde con el que aparece a la izquierda."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:258,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:243,columnNumber:17},void 0),m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"text-gray-600 mb-3",children:m.jsxDEV("strong",{className:"text-indigo-600",children:"Ejemplo 2:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:265,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:264,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-center mb-3",children:m.jsxDEV("img",{src:Vr("espacial","Espacial1.png"),alt:"Ejemplo 2",className:"max-w-full h-auto border rounded shadow-sm",onError:e=>{e.target.onerror=null,e.target.src="https://via.placeholder.com/400x200?text=Ejemplo+2"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:268,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:267,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-3",children:["La respuesta correcta es ",m.jsxDEV("strong",{children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:279,columnNumber:46},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:278,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:263,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:240,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Detalles del Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:285,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-600",children:[m.jsxDEV("li",{children:"El test consta de 28 preguntas de razonamiento espacial."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:287,columnNumber:19},void 0),m.jsxDEV("li",{children:["Dispondrás de ",m.jsxDEV("span",{className:"font-medium",children:"15 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:288,columnNumber:37},void 0)," para completar todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:288,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas y modificar tus respuestas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:289,columnNumber:19},void 0),m.jsxDEV("li",{children:'Al finalizar el tiempo o al presionar "Finalizar Test", se enviará automáticamente y no podrás realizar más cambios.'},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:290,columnNumber:19},void 0),m.jsxDEV("li",{children:"Cada pregunta tiene el mismo valor, por lo que te recomendamos responder a todas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:291,columnNumber:19},void 0),m.jsxDEV("li",{children:"No se penalizan las respuestas incorrectas, así que intenta responder todas las preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:292,columnNumber:19},void 0),m.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:293,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:286,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:284,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:298,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:299,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:297,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:216,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:215,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{x(!0),J.info("Test iniciado. ¡Buena suerte!")},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:306,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:305,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:211,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Espacial.jsx",lineNumber:185,columnNumber:5},void 0);var C},wr=Object.freeze(Object.defineProperty({__proto__:null,default:Br},Symbol.toStringTag,{value:"Module"})),Ar=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(!1),s=null==(e=i.state)?void 0:e.patientId,[o,c]=P.useState(0),[u,p]=P.useState({}),[b,f]=P.useState(720),[g,N]=P.useState(!1),x=[{id:1,question:"¿Qué tipo de polea podrá subir MÁS peso sin vencerse?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico1.png",options:["A","B","C","D"],correctAnswer:0},{id:2,question:"¿Qué estante es MENOS resistente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico2.png",options:["A","B","C","D"],correctAnswer:2},{id:3,question:"¿Qué tipo de listones permite mover la carga con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico3.png",options:["A","B","C","D"],correctAnswer:0},{id:4,question:"Si el viento sopla en la dirección indicada, ¿hacia dónde tendríamos que golpear la bola para acercarla MÁS al hoyo?",subtitle:"",image:"/assets/images/mecanico/mecanico4.png",options:["A","B","C","D"],correctAnswer:1},{id:5,question:"¿En qué zona (A, B o C) es MÁS probable que se rompan las cuerdas al colocar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico5.png",options:["A","B","C","D"],correctAnswer:1},{id:6,question:"¿De qué recipiente saldrá el líquido con MÁS fuerza?",subtitle:"",image:"/assets/images/mecanico/mecanico6.png",options:["A","B","C","D"],correctAnswer:2},{id:7,question:"¿Cuál de estos tres recipientes llenos de agua pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico7.png",options:["A","B","C","D"],correctAnswer:3},{id:8,question:"¿Qué torno deberá dar MÁS vueltas para enrollar los mismos metros de cuerda?",subtitle:"",image:"/assets/images/mecanico/mecanico8.png",options:["A","B","C","D"],correctAnswer:3},{id:9,question:"¿Hacia qué dirección (A, B, C o D) está soplando el viento?",subtitle:"",image:"/assets/images/mecanico/mecanico9.png",options:["A","B","C","D"],correctAnswer:1},{id:10,question:"¿Cuál de estos tres tejados es MÁS probable que se rompa en caso de nevada?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico10.png",options:["A","B","C","D"],correctAnswer:0},{id:11,question:"¿A cuál de estas personas le costará MÁS esfuerzo trasladar la carga?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico11.png",options:["A","B","C","D"],correctAnswer:2},{id:12,question:"¿Con qué bomba se inflará MÁS lentamente un colchón flotador?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico12.png",options:["A","B","C","D"],correctAnswer:2},{id:13,question:"¿En qué caso se debe ejercer MENOS fuerza en el punto indicado por la flecha para sujetar el mismo peso?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico13.png",options:["A","B","C","D"],correctAnswer:0},{id:14,question:"Si al frenar la bicicleta solo se usan los frenos delanteros, ¿hacia qué dirección será impulsado el ciclista?",subtitle:"",image:"/assets/images/mecanico/mecanico14.png",options:["A","B","C","D"],correctAnswer:3},{id:15,question:"¿Cuál de estos tres pesos (A, B o C) pesa MENOS?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico15.png",options:["A","B","C","D"],correctAnswer:3},{id:16,question:"¿Qué columna será MÁS resistente en caso de terremoto?",subtitle:"",image:"/assets/images/mecanico/mecanico16.png",options:["A","B","C","D"],correctAnswer:1},{id:17,question:"¿Qué micrófono tiene MENOS probabilidad de caerse ante un golpe?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico17.png",options:["A","B","C","D"],correctAnswer:0},{id:18,question:"¿Qué trayectoria (A, B o C) debe seguir el nadador para cruzar el río con MENOS esfuerzo?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico18.png",options:["A","B","C","D"],correctAnswer:1},{id:19,question:"¿En qué punto es necesario ejercer MÁS fuerza para cerrar la puerta?",subtitle:"",image:"/assets/images/mecanico/mecanico19.png",options:["A","B","C","D"],correctAnswer:0},{id:20,question:"¿En qué caso habrá que ejercer MENOS fuerza para levantar las ruedas delanteras del carro?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico20.png",options:["A","B","C","D"],correctAnswer:2},{id:21,question:"¿Qué coche ofrece MENOS resistencia al aire?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico21.png",options:["A","B","C","D"],correctAnswer:2},{id:22,question:"¿Cómo debe agarrarse la persona a la roca para que no la arrastre la corriente?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico22.png",options:["A","B","C","D"],correctAnswer:2},{id:23,question:"Si tenemos estas tres linternas, ¿cuál iluminará un área MAYOR?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico23.png",options:["A","B","C","D"],correctAnswer:2},{id:24,question:"¿Qué coche es MENOS probable que vuelque?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico24.png",options:["A","B","C","D"],correctAnswer:2},{id:25,question:"¿En qué punto alcanzará MÁS velocidad el paracaidista?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico25.png",options:["A","B","C","D"],correctAnswer:2},{id:26,question:"Si dejáramos tan solo UNO de los bloques (A, B, C o D), ¿con cuál se mantendría la estructura en equilibrio?",subtitle:"",image:"/assets/images/mecanico/mecanico26.png",options:["A","B","C","D"],correctAnswer:2},{id:27,question:"¿Hacia qué zona de la cápsula será impulsado el astronauta cuando la máquina gire en el sentido indicado por la flecha?",subtitle:"",image:"/assets/images/mecanico/mecanico27.png",options:["A","B","C","D"],correctAnswer:1},{id:28,question:"Si colgamos el peso de esta forma, ¿por cuál de los puntos (A, B o C) es MENOS probable que se rompa la madera?",subtitle:"(Si no hay diferencia, marca D).",image:"/assets/images/mecanico/mecanico28.png",options:["A","B","C","D"],correctAnswer:2}];P.useEffect(()=>{let e;return t&&!g&&b>0&&(e=setInterval(()=>{f(e=>e<=1?(j(),0):e-1)},1e3)),()=>clearInterval(e)},[t,g,b]);const h=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,v=e=>{c(e)},j=()=>d(null,null,function*(){try{N(!0);const i=Object.keys(u).length,t=x.length,r=t-i;let o=0;x.forEach(e=>{u[e.id]===e.correctAnswer&&o++});const n=i-o,l=720-b,c={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"mecanico"};if(s)try{yield Wt.saveTestResult({patientId:s,testType:"mecanico",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:u,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/mecanico",{state:c})}catch(e){J.error("Error al procesar los resultados del test")}}),V=x[o];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-cogs mr-2 text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:361,columnNumber:13},void 0),"Test de Aptitud Mecánica"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:360,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Evalúa tu comprensión de principios mecánicos y físicos básicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:364,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:359,columnNumber:9},void 0),t&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:h(b)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:368,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:367,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:358,columnNumber:7},void 0),t?m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",o+1," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:514,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:"Aptitud Mecánica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:517,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:513,columnNumber:15},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:void 0!==u[V.id]?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:521,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:512,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("h4",{className:"text-lg font-medium text-gray-800 mb-2",children:V.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:528,columnNumber:17},void 0),V.subtitle&&m.jsxDEV("p",{className:"text-sm text-gray-600 mb-4",children:V.subtitle},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:532,columnNumber:19},void 0),m.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 text-center",children:[m.jsxDEV("img",{src:V.image,alt:`Pregunta ${V.id}`,className:"max-w-full h-auto mx-auto",style:{maxHeight:"400px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:537,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:["[Imagen no disponible: ",V.image,"]"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:547,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:536,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:527,columnNumber:15},void 0),m.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:V.options.map((e,a)=>m.jsxDEV("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors text-center "+(u[V.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=V.id,i=a,void p(a=>l(n({},a),{[e]:i}));var e,i},children:[m.jsxDEV("div",{className:"w-8 h-8 flex items-center justify-center rounded-full mx-auto mb-2 "+(u[V.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:565,columnNumber:21},void 0),m.jsxDEV("div",{className:"text-sm text-gray-600",children:["Opción ",e]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:572,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:556,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:554,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:525,columnNumber:13},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>v(Math.max(0,o-1)),disabled:0===o,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:579,columnNumber:15},void 0),o<x.length-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>v(Math.min(x.length-1,o+1)),children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:587,columnNumber:17},void 0):m.jsxDEV(ae,{variant:"primary",onClick:j,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:594,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:578,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:511,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:510,columnNumber:9},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:609,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:608,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:x.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(o===a?"bg-blue-500 text-white":void 0!==u[x[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>v(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:614,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:612,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:633,columnNumber:19},void 0),m.jsxDEV("span",{children:[Object.keys(u).length," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:634,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:632,columnNumber:17},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(u).length/x.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:637,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:636,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:631,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:646,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:h(b)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:648,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:647,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Observa cuidadosamente cada imagen antes de responder. Puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:650,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:645,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:644,columnNumber:15},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full mt-2",onClick:j,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:656,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:611,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:607,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:606,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:509,columnNumber:7},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Mecánica: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:378,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:377,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-blue-800 mb-3",children:"Confirmación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:384,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:"He leído y acepto las instrucciones. Entiendo que una vez iniciado el test no podré pausarlo y deberé completarlo en su totalidad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:385,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:383,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:391,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-4",children:"En esta prueba aparecen varios tipos de situaciones sobre las cuales se te harán algunas preguntas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:392,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-4",children:"Lee atentamente cada pregunta, observa el dibujo y elige cuál de las opciones es la más adecuada."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:395,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-4",children:["Recuerda que solo existe ",m.jsxDEV("strong",{children:"UNA opción correcta"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:399,columnNumber:44},void 0),". Cuando hayas decidido qué opción es, marca la letra correspondiente (A, B, C o D), asegurándote de que coincida con el número del ejercicio que estás contestando."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:398,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:390,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Ejemplo M1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:405,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4 font-medium",children:["¿Cuál de las tres botellas podría quitarse sin que se cayera la bandeja?",m.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:407,columnNumber:91},void 0),m.jsxDEV("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:408,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:406,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[m.jsxDEV("img",{src:"/assets/images/mecanico/m1.png",alt:"Ejemplo M1 - Bandeja en equilibrio",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:413,columnNumber:21},void 0),m.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Bandeja en equilibrio con 3 botellas]"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:423,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:412,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:428,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:429,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:430,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:431,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:427,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:434,columnNumber:21},void 0)," En este ejemplo se presenta una bandeja en equilibrio sobre una mesa y encima de ella 3 botellas. Si se quitase la botella A o la botella B, la bandeja perdería el equilibrio y caería al suelo; si quitáramos la botella C la bandeja se mantendría en equilibrio. Por lo tanto, la solución al ejemplo M1 es ",m.jsxDEV("strong",{children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:434,columnNumber:352},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:433,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:411,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:404,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Ejemplo M2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:441,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4 font-medium",children:["Si los tres vehículos se están desplazando a 70 km/h, ¿cuál va MÁS rápido?",m.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:443,columnNumber:93},void 0),m.jsxDEV("span",{className:"text-sm text-gray-600",children:"(Si no hay diferencia, marca D)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:444,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:442,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-3 text-center",children:[m.jsxDEV("img",{src:"/assets/images/mecanico/m2.png",alt:"Ejemplo M2 - Tres vehículos a 70 km/h",className:"max-w-full h-auto mx-auto",style:{maxHeight:"300px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:449,columnNumber:21},void 0),m.jsxDEV("div",{className:"text-gray-500 text-sm",style:{display:"none"},children:"[Imagen no disponible: Tres vehículos a 70 km/h]"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:459,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:448,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:464,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:465,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:466,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:467,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:463,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:470,columnNumber:21},void 0)," Al desplazarse los tres vehículos a la misma velocidad (70 km/h), los tres van igual de rápido. Por lo tanto, la solución a este ejemplo es la opción ",m.jsxDEV("strong",{children:"D"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:470,columnNumber:198},void 0)," (no hay diferencia)."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:469,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:447,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:440,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:477,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:["El tiempo máximo para la realización de esta prueba es de ",m.jsxDEV("strong",{children:"12 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:479,columnNumber:77},void 0),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:478,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[m.jsxDEV("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las que aparecen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:482,columnNumber:19},void 0),m.jsxDEV("li",{children:[m.jsxDEV("strong",{children:"No se penalizará el error"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:483,columnNumber:23},void 0),", así que intenta responder todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:483,columnNumber:19},void 0),m.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:484,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:485,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:481,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:476,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:490,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:491,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:489,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:381,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:380,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{r(!0)},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:498,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:497,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:376,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Mecanico.jsx",lineNumber:357,columnNumber:5},void 0)},Rr=Object.freeze(Object.defineProperty({__proto__:null,default:Ar},Symbol.toStringTag,{value:"Module"})),Sr=[{id:1,type:"equality",question:"6 + 22 = 30 - ?",options:["2","8","10","12","28"],correct:0},{id:2,type:"equality",question:"18 - 6 = 16 - ?",options:["2","3","4","6","10"],correct:2},{id:3,type:"equality",question:"7² - 9 = ? x 2",options:["2","7","10","20","40"],correct:3},{id:4,type:"equality",question:"(6 + 8) x ? = 4 x 7",options:["2","3","4","7","10"],correct:0},{id:5,type:"equality",question:"(3 + 9) x 3 = (? x 2) x 6",options:["1","2","3","4","6"],correct:2},{id:6,type:"series",question:"23 • 18 • 14 • 11 • ?",options:["5","6","7","8","9"],correct:4},{id:7,type:"series",question:"9 • 11 • 10 • 12 • 11 • 13 • ?",options:["11","12","13","14","15"],correct:1},{id:8,type:"series",question:"2 • 6 • 11 • 17 • 24 • 32 • ?",options:["36","37","40","41","42"],correct:3},{id:9,type:"series",question:"21 • 23 • 20 • 24 • 19 • 25 • 18 • ?",options:["16","20","21","22","26"],correct:4},{id:10,type:"series",question:"16 • 8 • 16 • 20 • 10 • 20 • 24 • 12 • ?",options:["4","6","14","24","25"],correct:3},{id:11,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Café","55","15","825"],["Galletas","?","6","240"],["Sal","20","5","100"],["","","","1.165"]]},questionText:"El interrogante (?) está en las Unidades de Galletas.",options:["4","40","60","75","234"],correct:1},{id:12,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","?","(dato borrado)","35","85"],["Febrero","45","(dato borrado)","80","175"],["Marzo","60","45","(dato borrado)","(dato borrado)"],["Total","125","(dato borrado)","155","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Enero.",options:["10","20","25","30","50"],correct:1},{id:13,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Secadoras","Lavadoras","Frigoríficos","Total"],rows:[["Enero","(dato borrado)","(dato borrado)","30","90"],["Febrero","5","40","25","70"],["Marzo","(dato borrado)","30","35","105"],["Abril","50","45","?","(dato borrado)"],["Total","(dato borrado)","155","145","(dato borrado)"]]},questionText:"El interrogante (?) está en Frigoríficos de Abril.",options:["30","45","55","65","90"],correct:2},{id:14,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Abril","5","8","(dato borrado)","33"],["Mayo","(dato borrado)","15","5","30"],["Junio","10","(dato borrado)","(dato borrado)","(dato borrado)"],["Total","?","38","32","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Televisión.",options:["10","15","20","25","30"],correct:3},{id:15,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Televisión","Altavoces","Auriculares","Total"],rows:[["Enero","20","(dato borrado)","15","(dato borrado)"],["Febrero","?","(dato borrado)","30","70"],["Marzo","20","(dato borrado)","30","(dato borrado)"],["Abril","(dato borrado)","15","10","55"],["Total","85","(dato borrado)","80","(dato borrado)"]]},questionText:"El interrogante (?) está en Televisión de Febrero.",options:["10","15","25","40","45"],correct:1},{id:16,type:"equality",question:"(30 : 5) : (14 : 7) = [(? x 5) + 3] : 11",options:["1","2","3","4","6"],correct:4},{id:17,type:"equality",question:"[(23 - 9) - 4] x 2 = [(? : 6) - 3] x 5",options:["7","20","24","30","42"],correct:4},{id:18,type:"equality",question:"20 + 35 - 14 = (? x 2) - 19",options:["11","25","30","35","60"],correct:2},{id:19,type:"equality",question:"(9 x 7) : (? - 2) = 9 + 7 + 5",options:["3","4","5","6","12"],correct:2},{id:20,type:"equality",question:"[(? : 7) - 3] : 2 = 21 : 7",options:["2","9","42","49","63"],correct:4},{id:21,type:"series",question:"14 • 11 • 15 • 12 • 17 • 14 • 20 • 17 • 24 • 21 • ?",options:["18","25","26","27","29"],correct:4},{id:22,type:"series",question:"2 • 8 • 4 • 16 • 8 • ?",options:["4","14","24","26","32"],correct:4},{id:23,type:"series",question:"5 • 6 • 8 • 7 • 10 • 14 • 13 • 18 • 24 • 23 • ?",options:["22","24","26","28","30"],correct:4},{id:24,type:"series",question:"11 • 13 • 16 • 15 • 19 • 24 • 22 • ?",options:["23","24","25","26","28"],correct:4},{id:25,type:"series",question:"3 • 6 • 4 • 8 • 6 • ?",options:["4","9","10","11","12"],correct:4},{id:26,type:"series",question:"3 • 2 • 6 • 4 • 12 • 8 • 24 • 16 • 48 • 32 • 96 • ?",options:["64","80","89","92","95"],correct:0},{id:27,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Plancha","Depiladora","Afeitadora","Total"],rows:[["Mayo","20","5","(dato borrado)","40"],["Junio","(dato borrado)","(dato borrado)","10","(dato borrado)"],["Abril","(dato borrado)","5","(dato borrado)","25"],["Total","40","20","?","(dato borrado)"]]},questionText:"El interrogante (?) está en el Total de Afeitadora.",options:["60","65","75","90","95"],correct:3},{id:28,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Septiembre","25","40","5","70"],["Octubre","(dato borrado)","45","50","(dato borrado)"],["Noviembre","30","(dato borrado)","?","90"],["Diciembre","35","30","(dato borrado)","105"],["Total","145","155","(dato borrado)","(dato borrado)"]]},questionText:"El interrogante (?) está en Vitrocerámica de Noviembre.",options:["10","15","20","30","60"],correct:2},{id:29,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Cafetera","Tostadora","Freidora","Total"],rows:[["Enero","(dato borrado)","5","20","35"],["Febrero","(dato borrado)","(dato borrado)","5","30"],["Marzo","15","30","?","(dato borrado)"],["Total","(dato borrado)","55","40","(dato borrado)"]]},questionText:"El interrogante (?) está en Freidora de Marzo.",options:["5","10","15","20","25"],correct:2},{id:30,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Chocolate","5","225","1.125"],["Harina","6","?","(dato borrado)"],["Nueces","8","140","(dato borrado)"],["","","","3.925"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Harina.",options:["26","265","270","280","1.680"],correct:3},{id:31,type:"table",question:'Tabla: "Venta de productos por meses"',tableData:{headers:["Meses","Hornos","Microondas","Vitrocerámica","Total"],rows:[["Mayo","(dato borrado)","15","20","45"],["Junio","15","10","(dato borrado)","55"],["Julio","(dato borrado)","5","20","(dato borrado)"],["Agosto","10","(dato borrado)","10","25"],["Total","?","(dato borrado)","80","155"]]},questionText:"El interrogante (?) está en el Total de Hornos.",options:["25","35","40","45","50"],correct:2},{id:32,type:"table",question:'Tabla: "Puntos obtenidos en la compra"',tableData:{headers:["Artículo","Unidades","Puntos/Unidad","Total puntos"],rows:[["Grapa","2.500","0,05","125"],["Chincheta","3.000","?","(dato borrado)"],["Tornillo","1.200","0,1","(dato borrado)"],["","","","845"]]},questionText:"El interrogante (?) está en Puntos/Unidad de Chincheta.",options:["0,03","0,1","0,2","0,5","5"],correct:2}];function Tr({children:e="dato borrado"}){return m.jsxDEV("span",{className:"line-through text-red-600 font-medium",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/TextoTachado.jsx",lineNumber:9,columnNumber:5},this)}const Ir=()=>{var e;const a=M(),i=L(),[t,r]=P.useState(0),[s,o]=P.useState({}),[c,u]=P.useState(1200),[p,b]=P.useState(!1),[f,g]=P.useState(!1),N=null==(e=i.state)?void 0:e.patientId,x=Sr,h=P.useCallback(()=>d(null,null,function*(){try{const i=Object.keys(s).length,t=x.length,r=t-i;let o=0;Object.entries(s).forEach(([e,a])=>{const i=x.find(a=>a.id.toString()===e);i&&parseInt(a)===i.correct&&o++});const n=i-o,l=1200-c,u={correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,testType:"numerico"};if(N)try{yield Wt.saveTestResult({patientId:N,testType:"numerico",correctCount:o,incorrectCount:n,unansweredCount:r,timeUsed:l,totalQuestions:t,answers:s,errores:n})}catch(e){}J.success(`Test completado. Has respondido ${i} de ${t} preguntas. Respuestas correctas: ${o}`),a("/test/results/numerico",{state:u})}catch(e){J.error("Error al procesar los resultados del test")}}),[s,x,c,a,N]);P.useEffect(()=>{if(f&&c>0&&!p){const e=setTimeout(()=>u(c-1),1e3);return()=>clearTimeout(e)}0===c&&setTimeout(()=>h(),0)},[c,p,f,h]);const v=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`,j=e=>{r(e)},V=x[t];return m.jsxDEV("div",{className:"container mx-auto py-6 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-center mb-4",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-calculator mr-2 text-teal-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:140,columnNumber:13},void 0),"Test de Aptitud Numérica"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:139,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Resolución de igualdades, series numéricas y análisis de tablas de datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:143,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:138,columnNumber:9},void 0),f&&m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"text-xl font-mono font-bold text-red-600",children:v(c)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:147,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:146,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:137,columnNumber:7},void 0),f?m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxDEV("div",{className:"md:col-span-3",children:m.jsxDEV(K,{className:"mb-6",children:[m.jsxDEV(X,{className:"flex justify-between items-center",children:[m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-lg font-medium",children:["Pregunta ",t+1," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:320,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:"equality"===V.type?"Igualdades Numéricas":"series"===V.type?"Series Numéricas":"Tablas de Datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:323,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:319,columnNumber:15},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:void 0!==s[V.id]?"Respondida":"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:328,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:318,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("h4",{className:"text-lg font-medium text-gray-800 mb-4",children:["equality"===V.type&&"¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?","series"===V.type&&"¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?","table"===V.type&&"¿Qué número debe aparecer en lugar del interrogante (?) a partir de los datos de la tabla?"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:335,columnNumber:17},void 0),"table"===V.type?m.jsxDEV("div",{children:[m.jsxDEV("h5",{className:"font-medium mb-3",children:V.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:343,columnNumber:21},void 0),m.jsxDEV("div",{className:"overflow-x-auto mb-4",children:m.jsxDEV("table",{className:"min-w-full border border-gray-300",children:[m.jsxDEV("thead",{children:m.jsxDEV("tr",{className:"bg-gray-50",children:V.tableData.headers.map((e,a)=>m.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center font-medium",children:e},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:349,columnNumber:31},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:347,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:346,columnNumber:25},void 0),m.jsxDEV("tbody",{children:V.tableData.rows.map((e,a)=>m.jsxDEV("tr",{className:a%2==0?"bg-white":"bg-gray-50",children:e.map((e,a)=>m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"(dato borrado)"===e?m.jsxDEV(Tr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:360,columnNumber:64},void 0):e},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:359,columnNumber:33},void 0))},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:357,columnNumber:29},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:355,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:345,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:344,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:V.questionText},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:368,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:342,columnNumber:19},void 0):m.jsxDEV("div",{className:"bg-gray-50 p-4 rounded-lg text-center",children:m.jsxDEV("span",{className:"text-xl font-mono",children:V.question},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:372,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:371,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:334,columnNumber:15},void 0),m.jsxDEV("div",{className:"space-y-3",children:V.options.map((e,a)=>m.jsxDEV("div",{className:"border rounded-lg p-3 cursor-pointer transition-colors "+(s[V.id]===a?"bg-blue-50 border-blue-500":"hover:bg-gray-50"),onClick:()=>{return e=V.id,i=a,void o(a=>l(n({},a),{[e]:i}));var e,i},children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-6 h-6 flex items-center justify-center rounded-full mr-3 "+(s[V.id]===a?"bg-blue-500 text-white":"bg-gray-200 text-gray-700"),children:String.fromCharCode(65+a)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:390,columnNumber:23},void 0),m.jsxDEV("div",{className:"text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:397,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:389,columnNumber:21},void 0)},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:380,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:378,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:332,columnNumber:13},void 0),m.jsxDEV(ee,{className:"flex justify-between",children:[m.jsxDEV(ae,{variant:"outline",onClick:()=>j(Math.max(0,t-1)),disabled:0===t,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:405,columnNumber:15},void 0),t<x.length-1?m.jsxDEV(ae,{variant:"primary",onClick:()=>j(Math.min(x.length-1,t+1)),children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:413,columnNumber:17},void 0):m.jsxDEV(ae,{variant:"primary",onClick:h,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:420,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:404,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:317,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:316,columnNumber:9},void 0),m.jsxDEV("div",{children:m.jsxDEV(K,{className:"sticky top-6",children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-md font-medium",children:"Navegación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:435,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:434,columnNumber:13},void 0),m.jsxDEV(Z,{children:[m.jsxDEV("div",{className:"grid grid-cols-4 gap-2",children:x.map((e,a)=>m.jsxDEV("button",{className:"w-8 h-8 rounded-full font-medium text-sm "+(t===a?"bg-blue-500 text-white":void 0!==s[x[a].id]?"bg-green-100 text-green-800 border border-green-300":"bg-gray-100 text-gray-800 hover:bg-gray-200"),onClick:()=>j(a),title:`Pregunta ${a+1}`,children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:440,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:438,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-2 text-sm",children:[m.jsxDEV("span",{children:"Progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:459,columnNumber:19},void 0),m.jsxDEV("span",{children:[Object.keys(s).length," de ",x.length]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:460,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:458,columnNumber:17},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-2",children:m.jsxDEV("div",{className:"bg-green-500 h-2 rounded-full",style:{width:Object.keys(s).length/x.length*100+"%"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:463,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:462,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:457,columnNumber:15},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4",children:[m.jsxDEV("h3",{className:"text-sm font-medium text-blue-700 mb-1",children:"Información"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:472,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600 mb-2",children:["Tiempo restante: ",m.jsxDEV("span",{className:"font-medium",children:v(c)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:474,columnNumber:38},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:473,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-600",children:"Recuerda que al responder una pregunta, puedes cambiar tu respuesta antes de finalizar el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:476,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:471,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:470,columnNumber:15},void 0),m.jsxDEV(ae,{variant:"primary",className:"w-full mt-2",onClick:h,children:"Finalizar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:482,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:437,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:433,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:432,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:315,columnNumber:7},void 0):m.jsxDEV(K,{children:[m.jsxDEV(X,{children:m.jsxDEV("h2",{className:"text-xl font-semibold text-gray-800",children:"Aptitud Numérica: Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:157,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:156,columnNumber:11},void 0),m.jsxDEV(Z,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-gray-700 mb-3",children:"Instrucciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:162,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-4",children:"En esta prueba encontrarás distintos ejercicios numéricos que tendrás que resolver. Para ello tendrás que analizar la información que se presenta y determinar qué debe aparecer en lugar del interrogante. Cuando lo hayas decidido, deberás marcar la letra de la opción correspondiente (A, B, C, D o E), asegurándote de que coincida con el ejercicio que estás contestando. Ten en cuenta que en este ejercicio hay 5 posibles opciones de respuesta."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:163,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:161,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-blue-800 mb-3",children:"Tipo 1: Igualdades Numéricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:170,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:"En un primer tipo de ejercicios aparecerá una igualdad numérica en la que se ha sustituido uno de los elementos por un interrogante (?). Tu tarea consistirá en averiguar qué valor numérico debe aparecer en lugar del interrogante para que se cumpla la igualdad."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:171,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[m.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N1: ¿Qué número debe aparecer en lugar del interrogante (?) para que se cumpla la igualdad?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:176,columnNumber:19},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:m.jsxDEV("span",{className:"text-xl font-mono",children:"16 - 4 = ? + 2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:178,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:177,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 8"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:181,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:182,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 12"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:183,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 14"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:184,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:185,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:180,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:188,columnNumber:21},void 0)," La primera parte de la igualdad, 16 – 4, da lugar a 12. Para que en la segunda parte se obtenga el mismo resultado sería necesario sustituir el interrogante por 10, quedando la igualdad como 16 – 4 = 10 + 2. Por tanto, la respuesta correcta es ",m.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:188,columnNumber:292},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:187,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:175,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:169,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-green-800 mb-3",children:"Tipo 2: Series Numéricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:195,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:"En otros ejercicios tendrás que observar una serie de números ordenados de acuerdo con una ley y determinar cuál debe continuar la serie ocupando el lugar del interrogante."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:196,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[m.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N2: ¿Qué número debe aparecer en lugar del interrogante (?) de modo que continúe la serie?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:201,columnNumber:19},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-3 rounded text-center mb-3",children:m.jsxDEV("span",{className:"text-xl font-mono",children:"3 • 5 • 6 • 8 • 9 • 11 • 12 • 14 • ?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:203,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:202,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"A. 13"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:206,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"B. 15"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:207,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:208,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 18"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:209,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 20"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:210,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:205,columnNumber:19},void 0),m.jsxDEV("div",{className:"bg-gray-50 p-3 rounded mb-3",children:m.jsxDEV("div",{className:"text-center text-sm font-mono",children:["3 → 5 → 6 → 8 → 9 → 11 → 12 → 14 → ?",m.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:214,columnNumber:59},void 0),m.jsxDEV("span",{className:"text-blue-600",children:"+2 +1 +2 +1 +2 +1 +2 +1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:215,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:213,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:212,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:219,columnNumber:21},void 0)," En este ejemplo la serie combina aumentos de 2 unidades y de 1 unidad (+2, +1, +2, +1...). Como puede observarse, en el lugar del interrogante debe aumentarse 1 unidad con respecto al número anterior, por lo que el número que continuaría la serie sería el 15. Por tanto, la respuesta correcta es ",m.jsxDEV("strong",{children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:219,columnNumber:344},void 0),"."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:218,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:200,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:194,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-purple-800 mb-3",children:"Tipo 3: Tablas de Datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:226,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:"Finalmente, en un tercer tipo de ejercicios, aparecen tablas en las que un valor se ha sustituido intencionadamente por un interrogante (?) y otros valores han sido borrados (<<>>). Tu tarea consistirá en averiguar el número que debería aparecer en lugar del interrogante."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:227,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white border border-gray-300 rounded p-4 mb-3",children:[m.jsxDEV("h5",{className:"font-medium text-gray-800 mb-2",children:"Ejemplo N3: De acuerdo con los datos de la tabla, ¿qué número debe aparecer en lugar del interrogante (?)?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:232,columnNumber:19},void 0),m.jsxDEV("div",{className:"mb-4",children:[m.jsxDEV("h6",{className:"text-center font-medium mb-3",children:"Puntos obtenidos en la compra"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:235,columnNumber:21},void 0),m.jsxDEV("div",{className:"overflow-x-auto",children:m.jsxDEV("table",{className:"w-full border border-gray-300",children:[m.jsxDEV("thead",{children:m.jsxDEV("tr",{className:"bg-gray-100",children:[m.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Artículo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:240,columnNumber:29},void 0),m.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Unidades"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:241,columnNumber:29},void 0),m.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Puntos/Unidad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:242,columnNumber:29},void 0),m.jsxDEV("th",{className:"border border-gray-300 px-3 py-2 text-center",children:"Total puntos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:243,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:239,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:238,columnNumber:25},void 0),m.jsxDEV("tbody",{children:[m.jsxDEV("tr",{children:[m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2",children:"Jabón"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:248,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:249,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold text-red-600",children:"?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:250,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"30"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:251,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:247,columnNumber:27},void 0),m.jsxDEV("tr",{className:"bg-gray-50",children:[m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2",children:"Aceite"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:254,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"20"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:255,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:"2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:256,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center",children:m.jsxDEV("span",{className:"line-through",children:"40"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:257,columnNumber:90},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:257,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:253,columnNumber:27},void 0),m.jsxDEV("tr",{children:[m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 font-bold",colSpan:"3",children:"Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:260,columnNumber:29},void 0),m.jsxDEV("td",{className:"border border-gray-300 px-3 py-2 text-center font-bold",children:"70"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:261,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:259,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:246,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:237,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:236,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:234,columnNumber:19},void 0),m.jsxDEV("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[m.jsxDEV("div",{className:"bg-green-100 border-2 border-green-500 p-2 rounded text-center font-medium",children:"A. 3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:269,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"B. 5"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:270,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"C. 10"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:271,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"D. 40"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:272,columnNumber:21},void 0),m.jsxDEV("div",{className:"bg-gray-100 p-2 rounded text-center",children:"E. 60"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:273,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:268,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Solución:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:276,columnNumber:21},void 0)," A partir de los datos de la tabla sabemos que se han comprado 10 unidades de jabón y que se han obtenido 30 puntos, por lo que se puede deducir que el valor del interrogante es igual a 3 (10 unidades × 3 puntos/unidad = 30 puntos). Por tanto, la respuesta correcta es ",m.jsxDEV("strong",{children:"A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:276,columnNumber:316},void 0),". Fíjate que en este ejemplo no es necesario calcular el valor que ha sido borrado para obtener el valor del interrogante, pero en otros ejercicios sí será necesario calcular todos o algunos de estos valores para alcanzar la solución."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:275,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:231,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:225,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[m.jsxDEV("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Instrucciones para el Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:283,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-700 mb-4",children:["Cuando comience la prueba encontrarás más ejercicios como estos. El tiempo máximo para su realización es de ",m.jsxDEV("strong",{children:"20 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:285,columnNumber:127},void 0),", por lo que deberás trabajar rápidamente, esforzándote al máximo en encontrar la respuesta correcta."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:284,columnNumber:17},void 0),m.jsxDEV("ul",{className:"list-disc pl-5 space-y-2 text-gray-700 mb-4",children:[m.jsxDEV("li",{children:"Si en algún ejercicio no estás completamente seguro de cuál puede ser, elige la opción que creas que es más correcta de las cinco que aparecen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:288,columnNumber:19},void 0),m.jsxDEV("li",{children:[m.jsxDEV("strong",{children:"No se penalizará el error"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:289,columnNumber:23},void 0),", así que intenta responder todas las preguntas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:289,columnNumber:19},void 0),m.jsxDEV("li",{children:"Si terminas antes del tiempo concedido, repasa tus respuestas, pero NO continúes con las demás pruebas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:290,columnNumber:19},void 0),m.jsxDEV("li",{children:"Puedes navegar libremente entre las preguntas durante el tiempo disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:291,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:287,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:282,columnNumber:15},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-4",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-yellow-800 mb-1",children:"Importante"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:296,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-yellow-700",children:"Una vez iniciado el test, el temporizador no se detendrá. Asegúrate de disponer del tiempo necesario para completarlo sin interrupciones. Encuentra un lugar tranquilo y asegúrate de tener una buena conexión a internet."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:297,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:295,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:160,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:159,columnNumber:11},void 0),m.jsxDEV(ee,{className:"flex justify-end",children:m.jsxDEV(ae,{variant:"primary",onClick:()=>{g(!0)},className:"px-6 py-2",children:"Comenzar Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:304,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:303,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:155,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Numerico.jsx",lineNumber:136,columnNumber:5},void 0)},_r=Object.freeze(Object.defineProperty({__proto__:null,default:Ir},Symbol.toStringTag,{value:"Module"})),zr=({data:e})=>{var a;const i=e.reduce((e,a)=>e+a.value,0);if(!e.length||0===i)return m.jsxDEV("div",{className:"flex items-center justify-center h-full",children:m.jsxDEV("p",{className:"text-gray-500",children:"No hay datos disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:15,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:14,columnNumber:7},void 0);let t=0;const r=e.map(e=>{const a=e.value/i*100,r=a/100*360,s=l(n({},e),{percentage:a,startAngle:t,endAngle:t+r});return t+=r,s}),s=(e,a=50)=>{const i=(e-90)*Math.PI/180;return{x:50+a*Math.cos(i),y:50+a*Math.sin(i)}},o=e=>{const a=s(e.startAngle),i=s(e.endAngle),t=e.endAngle-e.startAngle<=180?"0":"1";return`M 50 50 L ${a.x} ${a.y} A 50 50 0 ${t} 1 ${i.x} ${i.y} Z`},c=1===r.length||r.some(e=>100===e.percentage);return m.jsxDEV("div",{className:"flex flex-col items-center h-full",children:[m.jsxDEV("div",{className:"w-full max-w-xs",children:m.jsxDEV("svg",{viewBox:"0 0 100 100",className:"w-48 h-48 mx-auto mb-4",children:c?m.jsxDEV("circle",{cx:"50",cy:"50",r:"50",fill:(null==(a=r.find(e=>100===e.percentage))?void 0:a.color)||r[0].color},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:62,columnNumber:13},void 0):r.map((e,a)=>m.jsxDEV("path",{d:o(e),fill:e.color,stroke:"#fff",strokeWidth:"0.5"},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:71,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:59,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:58,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex flex-col items-center w-full",children:r.map((e,a)=>m.jsxDEV("div",{className:"flex items-center mb-2 w-full justify-between max-w-xs",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-4 h-4 mr-2",style:{backgroundColor:e.color}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:87,columnNumber:15},void 0),m.jsxDEV("span",{className:"text-sm text-gray-700",children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:91,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:86,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("span",{className:"font-medium text-sm mr-2",children:e.value},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:94,columnNumber:15},void 0),m.jsxDEV("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:95,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:93,columnNumber:13},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:85,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:83,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/PieChart.jsx",lineNumber:57,columnNumber:5},void 0)},Or=()=>{const e=L().state||{correctCount:0,incorrectCount:0,unansweredCount:0,timeUsed:0,totalQuestions:32,testType:"unknown"},{correctCount:a,incorrectCount:i,unansweredCount:t,timeUsed:r,totalQuestions:s,testType:o}=e,n=[{name:"Correctas",value:a,color:"#10B981"},{name:"Incorrectas",value:i,color:"#EF4444"},{name:"Sin responder",value:t,color:"#9CA3AF"}].filter(e=>e.value>0);0===n.length&&n.push({name:"Correctas",value:a,color:"#10B981"});const l=Math.round(a/s*100),c=((e,a)=>{let i=[];return i="ortografia"===e?a<60?["Repasa las reglas básicas de ortografía, especialmente las reglas de acentuación","Practica la identificación de palabras correctas e incorrectas con ejercicios diarios","Presta especial atención a las letras que suelen causar confusión (b/v, g/j, h)"]:a<80?["Refuerza tu conocimiento en acentuación, especialmente en palabras agudas, llanas y esdrújulas","Practica con palabras que contengan h, b/v, g/j para mejorar tu precisión","Dedica tiempo a la lectura para familiarizarte con la escritura correcta de las palabras"]:["Continúa practicando con palabras poco comunes para expandir tu dominio ortográfico","Profundiza en las excepciones a las reglas de acentuación","Mantén el hábito de lectura para reforzar tu ortografía"]:"espacial"===e?100===a?["¡Felicidades! Has demostrado una capacidad excepcional de razonamiento espacial","Considera explorar campos profesionales como arquitectura, ingeniería, diseño 3D o ciencias que requieran esta habilidad","Tu capacidad para visualizar y manipular objetos mentalmente es extraordinaria","Podrías compartir tus técnicas y estrategias con otros para ayudarles a mejorar sus habilidades espaciales"]:a<60?["Practica con rompecabezas tridimensionales y juegos de construcción para mejorar tu visualización espacial","Realiza ejercicios de rotación mental, como imaginar objetos desde diferentes ángulos","Intenta dibujar objetos tridimensionales desde diferentes perspectivas","Utiliza aplicaciones o juegos que ejerciten el razonamiento espacial"]:a<80?["Continúa practicando con ejercicios de visualización espacial más complejos","Intenta resolver problemas de plegado de papel (origami) para mejorar tu comprensión de transformaciones espaciales","Practica con juegos de construcción y ensamblaje que requieran visualización tridimensional","Analiza las preguntas que te resultaron más difíciles para identificar patrones específicos"]:["Desafíate con problemas de visualización espacial más avanzados","Explora campos como la geometría tridimensional, el diseño 3D o la arquitectura","Comparte tus conocimientos y estrategias con otros para reforzar tu comprensión","Considera carreras o actividades que aprovechen tu excelente capacidad de razonamiento espacial"]:"mecanico"===e?100===a?["¡Excelente! Has demostrado una comprensión excepcional de principios mecánicos y físicos","Considera carreras en ingeniería mecánica, física aplicada, o diseño industrial","Tu capacidad para analizar sistemas mecánicos y predecir comportamientos es sobresaliente","Podrías explorar campos como robótica, automatización o diseño de maquinaria"]:a<60?["Repasa los principios básicos de física: fuerzas, palancas, poleas y equilibrio","Practica con ejercicios de mecánica básica y análisis de sistemas simples","Observa cómo funcionan las máquinas simples en la vida cotidiana","Dedica tiempo a entender conceptos como centro de gravedad, resistencia y fricción"]:a<80?["Profundiza en el estudio de máquinas simples y compuestas","Practica con problemas de equilibrio de fuerzas y análisis de estructuras","Estudia casos prácticos de aplicaciones mecánicas en la industria","Refuerza tu comprensión de principios como ventaja mecánica y eficiencia"]:["Explora conceptos avanzados de mecánica y termodinámica","Considera estudiar ingeniería mecánica o campos relacionados","Practica con simulaciones y modelado de sistemas mecánicos complejos","Mantén tu conocimiento actualizado con las últimas tecnologías mecánicas"]:"numerico"===e?100===a?["¡Excelente! Has demostrado una capacidad excepcional en razonamiento numérico","Considera carreras en matemáticas, estadística, ingeniería, economía o ciencias actuariales","Tu habilidad para resolver problemas numéricos complejos es sobresaliente","Podrías explorar campos como análisis de datos, investigación operativa o finanzas cuantitativas"]:a<60?["Repasa las operaciones básicas: suma, resta, multiplicación y división","Practica con ejercicios de igualdades numéricas y resolución de ecuaciones simples","Dedica tiempo a entender patrones en series numéricas","Refuerza tu comprensión de fracciones, decimales y porcentajes"]:a<80?["Practica con series numéricas más complejas para identificar patrones","Mejora tu velocidad en cálculo mental y operaciones aritméticas","Estudia problemas de proporcionalidad y regla de tres","Analiza tablas de datos y practica la interpretación de información numérica"]:["Desafíate con problemas de matemáticas más avanzados","Explora áreas como álgebra, estadística y análisis de datos","Considera estudiar carreras que requieran fuerte razonamiento cuantitativo","Mantén tu agilidad mental practicando regularmente con ejercicios numéricos"]:["Continúa practicando ejercicios similares para mejorar tu desempeño","Revisa los conceptos básicos relacionados con este tipo de prueba","Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"],i})(o,l);return m.jsxDEV("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:(e=>{switch(e){case"verbal":return"Test de Aptitud Verbal";case"ortografia":return"Test de Ortografía";case"razonamiento":return"Test de Razonamiento";case"atencion":return"Test de Atención";case"espacial":return"Test de Visualización Espacial";case"mecanico":return"Test de Razonamiento Mecánico";case"numerico":return"Test de Razonamiento Numérico";default:return"Resultados del Test"}})(o)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:220,columnNumber:9},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Resumen de tu desempeño en el test."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:221,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:219,columnNumber:7},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[m.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6",children:[m.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:227,columnNumber:11},void 0),m.jsxDEV("div",{className:"h-64",children:m.jsxDEV(zr,{data:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:229,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:228,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:226,columnNumber:9},void 0),m.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6",children:[m.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:235,columnNumber:11},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("div",{className:"text-3xl font-bold mb-1 "+(u=l,100===u?"text-green-600 animate-pulse":u>=90?"text-green-600":u>=75?"text-green-500":u>=60?"text-blue-500":u>=50?"text-yellow-500":"text-red-500"),children:[l,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:238,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-700 font-medium",children:(e=>100===e||e>=90?"Excelente desempeño":e>=75?"Muy buen desempeño":e>=60?"Buen desempeño":e>=50?"Desempeño aceptable":"Necesita mejorar")(l)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:241,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:237,columnNumber:11},void 0),m.jsxDEV("div",{className:"space-y-3",children:[m.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Respuestas correctas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:246,columnNumber:15},void 0),m.jsxDEV("span",{className:"font-medium text-gray-800",children:[a," de ",s]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:247,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:245,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Respuestas incorrectas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:250,columnNumber:15},void 0),m.jsxDEV("span",{className:"font-medium text-gray-800",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:251,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:249,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex justify-between py-2 border-b border-gray-100",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Sin responder:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:254,columnNumber:15},void 0),m.jsxDEV("span",{className:"font-medium text-gray-800",children:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:255,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:253,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex justify-between py-2",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Tiempo utilizado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:258,columnNumber:15},void 0),m.jsxDEV("span",{className:"font-medium text-gray-800",children:(e=>{const a=e%60;return`${Math.floor(e/60)}:${a<10?"0":""}${a}`})(r)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:259,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:257,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:244,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:234,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:224,columnNumber:7},void 0),m.jsxDEV("div",{className:"bg-white shadow-md rounded-lg p-6 mb-6",children:[m.jsxDEV("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Recomendaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:267,columnNumber:9},void 0),m.jsxDEV("ul",{className:"space-y-2",children:c.map((e,a)=>m.jsxDEV("li",{className:"flex items-start",children:[m.jsxDEV("div",{className:"flex-shrink-0 h-5 w-5 mt-0.5",children:m.jsxDEV("svg",{className:"h-5 w-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:m.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:273,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:272,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:271,columnNumber:15},void 0),m.jsxDEV("span",{className:"ml-2 text-gray-700",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:276,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:270,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:268,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:266,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4",children:[m.jsxDEV(k,{to:`/test/instructions/${o}`,className:"flex-1 bg-blue-600 text-white text-center py-3 px-4 rounded-md hover:bg-blue-700 transition-colors",children:"Realizar el Test Nuevamente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:284,columnNumber:9},void 0),m.jsxDEV(k,{to:"/student/questionnaire",className:"flex-1 bg-gray-100 text-gray-800 text-center py-3 px-4 rounded-md hover:bg-gray-200 transition-colors",children:"Volver a la Lista de Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:290,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:283,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Results.jsx",lineNumber:218,columnNumber:5},void 0);var u},Pr=Object.freeze(Object.defineProperty({__proto__:null,default:Or},Symbol.toStringTag,{value:"Module"})),Mr=P.createContext();function kr(){return kr=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var i=arguments[a];for(var t in i)({}).hasOwnProperty.call(i,t)&&(e[t]=i[t])}return e},kr.apply(null,arguments)}function qr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Lr(e,a){return(Lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,a){return e.__proto__=a,e})(e,a)}var $r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function Fr(e,a){return e===a||!(!$r(e)||!$r(a))}function Ur(e,a){if(e.length!==a.length)return!1;for(var i=0;i<e.length;i++)if(!Fr(e[i],a[i]))return!1;return!0}function Qr(e,a){var i;void 0===a&&(a=Ur);var t,r=[],s=!1;return function(){for(var o=[],n=0;n<arguments.length;n++)o[n]=arguments[n];return s&&i===this&&a(o,r)||(t=e.apply(this,o),s=!0,i=this,r=o),t}}var Wr="object"==typeof performance&&"function"==typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function Hr(e){cancelAnimationFrame(e.id)}var Gr=-1;function Jr(e){if(void 0===e&&(e=!1),-1===Gr||e){var a=document.createElement("div"),i=a.style;i.width="50px",i.height="50px",i.overflow="scroll",document.body.appendChild(a),Gr=a.offsetWidth-a.clientWidth,document.body.removeChild(a)}return Gr}var Yr=null;function Kr(e){if(void 0===e&&(e=!1),null===Yr||e){var a=document.createElement("div"),i=a.style;i.width="50px",i.height="50px",i.overflow="scroll",i.direction="rtl";var t=document.createElement("div"),r=t.style;return r.width="100px",r.height="100px",a.appendChild(t),document.body.appendChild(a),a.scrollLeft>0?Yr="positive-descending":(a.scrollLeft=1,Yr=0===a.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(a),Yr}return Yr}var Xr=function(e,a){return e},Zr=null,es=null;function as(e){var a,i=e.getItemOffset,t=e.getEstimatedTotalSize,r=e.getItemSize,s=e.getOffsetForIndexAndAlignment,o=e.getStartIndexForOffset,n=e.getStopIndexForStartIndex,l=e.initInstanceProps,c=e.shouldResetStyleCacheOnItemSizeChange,u=e.validateProps;return(a=function(e){var a,d;function m(a){var t;return(t=e.call(this,a)||this)._instanceProps=l(t.props,qr(t)),t._outerRef=void 0,t._resetIsScrollingTimeoutId=null,t.state={instance:qr(t),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"==typeof t.props.initialScrollOffset?t.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},t._callOnItemsRendered=void 0,t._callOnItemsRendered=Qr(function(e,a,i,r){return t.props.onItemsRendered({overscanStartIndex:e,overscanStopIndex:a,visibleStartIndex:i,visibleStopIndex:r})}),t._callOnScroll=void 0,t._callOnScroll=Qr(function(e,a,i){return t.props.onScroll({scrollDirection:e,scrollOffset:a,scrollUpdateWasRequested:i})}),t._getItemStyle=void 0,t._getItemStyle=function(e){var a,s=t.props,o=s.direction,n=s.itemSize,l=s.layout,u=t._getItemStyleCache(c&&n,c&&l,c&&o);if(u.hasOwnProperty(e))a=u[e];else{var d=i(t.props,e,t._instanceProps),m=r(t.props,e,t._instanceProps),p="horizontal"===o||"horizontal"===l,b="rtl"===o,f=p?d:0;u[e]=a={position:"absolute",left:b?void 0:f,right:b?f:void 0,top:p?0:d,height:p?"100%":m,width:p?m:"100%"}}return a},t._getItemStyleCache=void 0,t._getItemStyleCache=Qr(function(e,a,i){return{}}),t._onScrollHorizontal=function(e){var a=e.currentTarget,i=a.clientWidth,r=a.scrollLeft,s=a.scrollWidth;t.setState(function(e){if(e.scrollOffset===r)return null;var a=t.props.direction,o=r;if("rtl"===a)switch(Kr()){case"negative":o=-r;break;case"positive-descending":o=s-i-r}return o=Math.max(0,Math.min(o,s-i)),{isScrolling:!0,scrollDirection:e.scrollOffset<o?"forward":"backward",scrollOffset:o,scrollUpdateWasRequested:!1}},t._resetIsScrollingDebounced)},t._onScrollVertical=function(e){var a=e.currentTarget,i=a.clientHeight,r=a.scrollHeight,s=a.scrollTop;t.setState(function(e){if(e.scrollOffset===s)return null;var a=Math.max(0,Math.min(s,r-i));return{isScrolling:!0,scrollDirection:e.scrollOffset<a?"forward":"backward",scrollOffset:a,scrollUpdateWasRequested:!1}},t._resetIsScrollingDebounced)},t._outerRefSetter=function(e){var a=t.props.outerRef;t._outerRef=e,"function"==typeof a?a(e):null!=a&&"object"==typeof a&&a.hasOwnProperty("current")&&(a.current=e)},t._resetIsScrollingDebounced=function(){var e,a,i,r;null!==t._resetIsScrollingTimeoutId&&Hr(t._resetIsScrollingTimeoutId),t._resetIsScrollingTimeoutId=(e=t._resetIsScrolling,a=150,i=Wr(),r={id:requestAnimationFrame(function t(){Wr()-i>=a?e.call(null):r.id=requestAnimationFrame(t)})})},t._resetIsScrolling=function(){t._resetIsScrollingTimeoutId=null,t.setState({isScrolling:!1},function(){t._getItemStyleCache(-1,null)})},t}d=e,(a=m).prototype=Object.create(d.prototype),a.prototype.constructor=a,Lr(a,d),m.getDerivedStateFromProps=function(e,a){return is(e,a),u(e),null};var p=m.prototype;return p.scrollTo=function(e){e=Math.max(0,e),this.setState(function(a){return a.scrollOffset===e?null:{scrollDirection:a.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},p.scrollToItem=function(e,a){void 0===a&&(a="auto");var i=this.props,t=i.itemCount,r=i.layout,o=this.state.scrollOffset;e=Math.max(0,Math.min(e,t-1));var n=0;if(this._outerRef){var l=this._outerRef;n="vertical"===r?l.scrollWidth>l.clientWidth?Jr():0:l.scrollHeight>l.clientHeight?Jr():0}this.scrollTo(s(this.props,e,a,o,this._instanceProps,n))},p.componentDidMount=function(){var e=this.props,a=e.direction,i=e.initialScrollOffset,t=e.layout;if("number"==typeof i&&null!=this._outerRef){var r=this._outerRef;"horizontal"===a||"horizontal"===t?r.scrollLeft=i:r.scrollTop=i}this._callPropsCallbacks()},p.componentDidUpdate=function(){var e=this.props,a=e.direction,i=e.layout,t=this.state,r=t.scrollOffset;if(t.scrollUpdateWasRequested&&null!=this._outerRef){var s=this._outerRef;if("horizontal"===a||"horizontal"===i)if("rtl"===a)switch(Kr()){case"negative":s.scrollLeft=-r;break;case"positive-ascending":s.scrollLeft=r;break;default:var o=s.clientWidth,n=s.scrollWidth;s.scrollLeft=n-o-r}else s.scrollLeft=r;else s.scrollTop=r}this._callPropsCallbacks()},p.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&Hr(this._resetIsScrollingTimeoutId)},p.render=function(){var e=this.props,a=e.children,i=e.className,r=e.direction,s=e.height,o=e.innerRef,n=e.innerElementType,l=e.innerTagName,c=e.itemCount,u=e.itemData,d=e.itemKey,m=void 0===d?Xr:d,p=e.layout,b=e.outerElementType,f=e.outerTagName,g=e.style,N=e.useIsScrolling,x=e.width,h=this.state.isScrolling,v="horizontal"===r||"horizontal"===p,j=v?this._onScrollHorizontal:this._onScrollVertical,V=this._getRangeToRender(),y=V[0],E=V[1],C=[];if(c>0)for(var D=y;D<=E;D++)C.push(P.createElement(a,{data:u,key:m(D,u),index:D,isScrolling:N?h:void 0,style:this._getItemStyle(D)}));var B=t(this.props,this._instanceProps);return P.createElement(b||f||"div",{className:i,onScroll:j,ref:this._outerRefSetter,style:kr({position:"relative",height:s,width:x,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:r},g)},P.createElement(n||l||"div",{children:C,ref:o,style:{height:v?"100%":B,pointerEvents:h?"none":void 0,width:v?B:"100%"}}))},p._callPropsCallbacks=function(){if("function"==typeof this.props.onItemsRendered&&this.props.itemCount>0){var e=this._getRangeToRender(),a=e[0],i=e[1],t=e[2],r=e[3];this._callOnItemsRendered(a,i,t,r)}if("function"==typeof this.props.onScroll){var s=this.state,o=s.scrollDirection,n=s.scrollOffset,l=s.scrollUpdateWasRequested;this._callOnScroll(o,n,l)}},p._getRangeToRender=function(){var e=this.props,a=e.itemCount,i=e.overscanCount,t=this.state,r=t.isScrolling,s=t.scrollDirection,l=t.scrollOffset;if(0===a)return[0,0,0,0];var c=o(this.props,l,this._instanceProps),u=n(this.props,c,l,this._instanceProps),d=r&&"backward"!==s?1:Math.max(1,i),m=r&&"forward"!==s?1:Math.max(1,i);return[Math.max(0,c-d),Math.max(0,Math.min(a-1,u+m)),c,u]},m}(P.PureComponent)).defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},a}"undefined"!=typeof window&&void 0!==window.WeakSet&&(Zr=new WeakSet,es=new WeakSet);var is=function(e,a){var i=e.children,t=e.direction,r=e.height,s=e.layout,o=e.innerTagName,n=e.outerTagName,l=e.width,c=a.instance;null==o&&null==n||es&&!es.has(c)&&es.add(c);var u="horizontal"===t||"horizontal"===s;switch(t){case"horizontal":case"vertical":Zr&&!Zr.has(c)&&Zr.add(c);break;case"ltr":case"rtl":break;default:throw Error('An invalid "direction" prop has been specified. Value should be either "ltr" or "rtl". "'+t+'" was specified.')}switch(s){case"horizontal":case"vertical":break;default:throw Error('An invalid "layout" prop has been specified. Value should be either "horizontal" or "vertical". "'+s+'" was specified.')}if(null==i)throw Error('An invalid "children" prop has been specified. Value should be a React component. "'+(null===i?"null":typeof i)+'" was specified.');if(u&&"number"!=typeof l)throw Error('An invalid "width" prop has been specified. Horizontal lists must specify a number for width. "'+(null===l?"null":typeof l)+'" was specified.');if(!u&&"number"!=typeof r)throw Error('An invalid "height" prop has been specified. Vertical lists must specify a number for height. "'+(null===r?"null":typeof r)+'" was specified.')},ts=as({getItemOffset:function(e,a){return a*e.itemSize},getItemSize:function(e,a){return e.itemSize},getEstimatedTotalSize:function(e){var a=e.itemCount;return e.itemSize*a},getOffsetForIndexAndAlignment:function(e,a,i,t,r,s){var o=e.direction,n=e.height,l=e.itemCount,c=e.itemSize,u=e.layout,d=e.width,m="horizontal"===o||"horizontal"===u?d:n,p=Math.max(0,l*c-m),b=Math.min(p,a*c),f=Math.max(0,a*c-m+c+s);switch("smart"===i&&(i=t>=f-m&&t<=b+m?"auto":"center"),i){case"start":return b;case"end":return f;case"center":var g=Math.round(f+(b-f)/2);return g<Math.ceil(m/2)?0:g>p+Math.floor(m/2)?p:g;default:return t>=f&&t<=b?t:t<f?f:b}},getStartIndexForOffset:function(e,a){var i=e.itemCount,t=e.itemSize;return Math.max(0,Math.min(i-1,Math.floor(a/t)))},getStopIndexForStartIndex:function(e,a,i){var t=e.direction,r=e.height,s=e.itemCount,o=e.itemSize,n=e.layout,l=e.width,c=a*o,u="horizontal"===t||"horizontal"===n?l:r,d=Math.ceil((u+i-c)/o);return Math.max(0,Math.min(s-1,a+d-1))},initInstanceProps:function(e){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(e){var a=e.itemSize;if("number"!=typeof a)throw Error('An invalid "itemSize" prop has been specified. Value should be a number. "'+(null===a?"null":typeof a)+'" was specified.')}});const rs=e=>{if(!e)return null;const a=new Date,i=new Date(e);let t=a.getFullYear()-i.getFullYear();const r=a.getMonth()-i.getMonth();return(r<0||0===r&&a.getDate()<i.getDate())&&t--,t},ss=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"2-digit",day:"2-digit"})},os=e=>{if(!e)return"";return new Date(e).toLocaleDateString("es-ES",{weekday:"long",year:"numeric",month:"long",day:"numeric"})},ns=e=>{if("number"==typeof e){if(e<0)return"N/A";const a=Math.floor(e/3600),i=Math.floor(e%3600/60),t=Math.floor(e%60);return a>0?`${a}:${i.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`:`${i}:${t.toString().padStart(2,"0")}`}if(!e)return"";return new Date(e).toLocaleTimeString("es-ES",{hour:"2-digit",minute:"2-digit"})},ls=P.forwardRef((e,a)=>{var i=e,{checked:t=!1,indeterminate:r=!1,onChange:s,disabled:o=!1,className:l="",children:u,id:d,"aria-label":p,"aria-describedby":b}=i,f=c(i,["checked","indeterminate","onChange","disabled","className","children","id","aria-label","aria-describedby"]);const g=P.useRef(null),N=a||g;P.useEffect(()=>{N.current&&(N.current.indeterminate=r)},[r,N]);const v=`\n    relative inline-flex items-center justify-center w-5 h-5 border-2 rounded transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\n    ${o?"cursor-not-allowed opacity-50":"cursor-pointer"}\n  `,j="border-gray-300 bg-white hover:border-gray-400",V="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700",y="border-blue-600 bg-blue-600 hover:border-blue-700 hover:bg-blue-700";return m.jsxDEV("label",{className:`inline-flex items-center ${o?"cursor-not-allowed":"cursor-pointer"} ${l}`,htmlFor:d,children:[m.jsxDEV("div",{className:"relative",children:[m.jsxDEV("input",n({ref:N,type:"checkbox",id:d,checked:t,onChange:e=>{s&&!o&&s(e)},disabled:o,className:"sr-only","aria-label":p,"aria-describedby":b},f),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:71,columnNumber:9},void 0),m.jsxDEV("div",{className:`${v} ${r?y:t?V:j}`,children:r?m.jsxDEV(x,{className:"w-3 h-3 text-white","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:57,columnNumber:14},void 0):t?m.jsxDEV(h,{className:"w-3 h-3 text-white","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:60,columnNumber:14},void 0):null},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:83,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:70,columnNumber:7},void 0),u&&m.jsxDEV("span",{className:"ml-2 text-sm "+(o?"text-gray-400":"text-gray-700"),children:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:88,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Checkbox.jsx",lineNumber:66,columnNumber:5},void 0)});ls.displayName="Checkbox";const cs=e=>{var a=e,{children:i,content:t,position:r="top",delay:s=300,disabled:o=!1,className:l="",contentClassName:u="",arrow:d=!0}=a,p=c(a,["children","content","position","delay","disabled","className","contentClassName","arrow"]);const[b,f]=P.useState(!1),[g,N]=P.useState({top:0,left:0}),x=P.useRef(null),h=P.useRef(null),v=P.useRef(null),j=()=>{if(!x.current||!h.current)return;const e=x.current.getBoundingClientRect(),a=h.current.getBoundingClientRect(),i=window.pageYOffset||document.documentElement.scrollTop,t=window.pageXOffset||document.documentElement.scrollLeft;let s,o;switch(r){case"top":default:s=e.top+i-a.height-8,o=e.left+t+(e.width-a.width)/2;break;case"bottom":s=e.bottom+i+8,o=e.left+t+(e.width-a.width)/2;break;case"left":s=e.top+i+(e.height-a.height)/2,o=e.left+t-a.width-8;break;case"right":s=e.top+i+(e.height-a.height)/2,o=e.right+t+8}const n=window.innerWidth-a.width-8,l=window.innerHeight-a.height-8;o=Math.max(8,Math.min(o,n)),s=Math.max(8,Math.min(s,l)),N({top:s,left:o})},V=()=>{!o&&t&&(v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{f(!0)},s))},y=()=>{v.current&&clearTimeout(v.current),f(!1)};P.useEffect(()=>{if(b){j();const e=()=>j();return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}},[b,r]),P.useEffect(()=>()=>{v.current&&clearTimeout(v.current)},[]);const E=P.cloneElement(i,n({ref:x,onMouseEnter:e=>{V(),i.props.onMouseEnter&&i.props.onMouseEnter(e)},onMouseLeave:e=>{y(),i.props.onMouseLeave&&i.props.onMouseLeave(e)},onFocus:e=>{V(),i.props.onFocus&&i.props.onFocus(e)},onBlur:e=>{y(),i.props.onBlur&&i.props.onBlur(e)},className:`${i.props.className||""} ${l}`.trim()},p)),C=b&&t&&m.jsxDEV("div",{ref:h,className:`\n        fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg\n        pointer-events-none transition-opacity duration-200\n        ${b?"opacity-100":"opacity-0"}\n        ${u}\n      `,style:{top:g.top,left:g.left},role:"tooltip","aria-hidden":!b,children:[t,d&&m.jsxDEV("div",{className:(()=>{const e="absolute w-2 h-2 bg-gray-900 transform rotate-45";switch(r){case"top":default:return`${e} -bottom-1 left-1/2 -translate-x-1/2`;case"bottom":return`${e} -top-1 left-1/2 -translate-x-1/2`;case"left":return`${e} -right-1 top-1/2 -translate-y-1/2`;case"right":return`${e} -left-1 top-1/2 -translate-y-1/2`}})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:182,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:166,columnNumber:5},void 0);return m.jsxDEV(m.Fragment,{children:[E,"undefined"!=typeof document&&U.createPortal(C,document.body)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Tooltip.jsx",lineNumber:187,columnNumber:5},void 0)},us=e=>{var a=e,{className:i="",width:t,height:r,variant:s="rectangular",animation:o="pulse",lines:u=1}=a,d=c(a,["className","width","height","variant","animation","lines"]);const p={pulse:"animate-pulse",wave:"animate-wave",none:""},b={rectangular:"rounded",circular:"rounded-full",text:"rounded h-4",avatar:"rounded-full w-10 h-10"},f=()=>`\n      bg-gray-200\n      ${p[o]||p.pulse}\n      ${b[s]||b.rectangular}\n      ${i}\n    `.trim(),g=()=>{const e={};return t&&(e.width="number"==typeof t?`${t}px`:t),r&&(e.height="number"==typeof r?`${r}px`:r),e};return"text"===s&&u>1?m.jsxDEV("div",l(n({className:`space-y-2 ${i}`},d),{children:Array.from({length:u},(e,a)=>m.jsxDEV("div",{className:f(),style:l(n({},g()),{width:a===u-1?"75%":"100%"})},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:53,columnNumber:11},void 0))}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:51,columnNumber:7},void 0):m.jsxDEV("div",n({className:f(),style:g()},d),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/LoadingSkeleton.jsx",lineNumber:67,columnNumber:5},void 0)},ds=P.memo(({index:e,style:a,data:i})=>{var t,r,s,o,n;const{informes:l,selectedInformes:c,onToggleSelection:u,onViewInforme:d,onDeleteInforme:p,onViewChart:b,isLoading:f}=i,g=l[e],N=c.has(null==g?void 0:g.id),x=e%2==0,h=P.useCallback(()=>{u(g.id)},[null==g?void 0:g.id,u]),D=P.useCallback(()=>{d(g)},[g,d]),B=P.useCallback(()=>{p(g.id)},[null==g?void 0:g.id,p]),w=P.useCallback(()=>{b(g)},[g,b]);return f||!g?m.jsxDEV("div",{style:a,className:"flex items-center px-4 py-3",children:m.jsxDEV(us,{className:"w-full h-16"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:53,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:52,columnNumber:7},void 0):m.jsxDEV("div",{style:a,className:`\n        flex items-center px-4 py-3 border-b border-gray-200 hover:bg-gray-50 transition-colors\n        ${x?"bg-white":"bg-gray-25"}\n        ${N?"bg-blue-50 border-blue-200":""}\n      `,role:"row","aria-selected":N,children:[m.jsxDEV("div",{className:"flex-shrink-0 w-12",children:m.jsxDEV(ls,{checked:N,onChange:h,"aria-label":`Seleccionar informe ${g.titulo}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:71,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:70,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-1 min-w-0 px-3",children:[m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV(v,{className:"w-4 h-4 text-gray-400 flex-shrink-0","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:81,columnNumber:11},void 0),m.jsxDEV("span",{className:"font-medium text-gray-900 truncate",children:(null==(t=g.resultados)?void 0:t.nombre_paciente)||"Paciente no especificado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:82,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:80,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center space-x-2 mt-1",children:[m.jsxDEV(j,{className:"w-3 h-3 text-gray-400 flex-shrink-0","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:87,columnNumber:11},void 0),m.jsxDEV("span",{className:"text-sm text-gray-600 truncate",children:g.titulo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:88,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:86,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:79,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV(V,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:97,columnNumber:11},void 0),m.jsxDEV("div",{className:"text-sm",children:[m.jsxDEV("div",{className:"text-gray-900",children:ss(g.fecha_generacion)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:99,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-gray-500",children:ns(g.fecha_generacion)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:102,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:98,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:96,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:95,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV(V,{className:"w-4 h-4 text-gray-400","aria-hidden":"true"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:112,columnNumber:11},void 0),m.jsxDEV("div",{className:"text-sm",children:m.jsxDEV("div",{className:"text-gray-900",children:(null==(r=g.resultados)?void 0:r.fecha_evaluacion)?ss(g.resultados.fecha_evaluacion):"No especificada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:114,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:113,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:111,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:110,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-32 px-3",children:m.jsxDEV("div",{className:"flex items-center space-x-1",children:[m.jsxDEV(cs,{content:"Ver informe",children:m.jsxDEV(ae,{variant:"ghost",size:"sm",onClick:D,className:"p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100","aria-label":`Ver informe de ${null==(s=g.resultados)?void 0:s.nombre_paciente}`,children:m.jsxDEV(y,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:135,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:128,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:127,columnNumber:11},void 0),m.jsxDEV(cs,{content:"Ver gráfico",children:m.jsxDEV(ae,{variant:"ghost",size:"sm",onClick:w,className:"p-2 text-green-600 hover:text-green-800 hover:bg-green-100","aria-label":`Ver gráfico de ${null==(o=g.resultados)?void 0:o.nombre_paciente}`,children:m.jsxDEV(E,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:147,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:140,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:139,columnNumber:11},void 0),m.jsxDEV(cs,{content:"Eliminar informe",children:m.jsxDEV(ae,{variant:"ghost",size:"sm",onClick:B,className:"p-2 text-red-600 hover:text-red-800 hover:bg-red-100","aria-label":`Eliminar informe de ${null==(n=g.resultados)?void 0:n.nombre_paciente}`,children:m.jsxDEV(C,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:159,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:152,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:151,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:126,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:125,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:59,columnNumber:5},void 0)});ds.displayName="InformeRow";const ms=P.memo(({selectedCount:e,totalCount:a,onSelectAll:i,onDeselectAll:t,allSelected:r,someSelected:s})=>{const o=P.useCallback(()=>{r||s?t():i()},[r,s,i,t]);return m.jsxDEV("div",{className:"flex items-center px-4 py-3 bg-gray-50 border-b border-gray-200 font-medium text-gray-700",role:"row",children:[m.jsxDEV("div",{className:"flex-shrink-0 w-12",children:m.jsxDEV(ls,{checked:r,indeterminate:s&&!r,onChange:o,"aria-label":"Seleccionar todos los informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:196,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:195,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-1 px-3",children:[m.jsxDEV("span",{children:"Paciente / Título"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:206,columnNumber:9},void 0),e>0&&m.jsxDEV("span",{className:"ml-2 text-sm text-blue-600",children:["(",e," seleccionados)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:208,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:205,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:m.jsxDEV("span",{children:"Fecha Generación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:215,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:214,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-40 px-3",children:m.jsxDEV("span",{children:"Fecha Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:219,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:218,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex-shrink-0 w-32 px-3",children:m.jsxDEV("span",{children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:223,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:222,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:190,columnNumber:5},void 0)});ms.displayName="TableHeader";const ps=P.memo(({searchTerm:e,onClearSearch:a})=>m.jsxDEV("div",{className:"flex flex-col items-center justify-center py-12 px-4",children:[m.jsxDEV(j,{className:"w-16 h-16 text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:236,columnNumber:5},void 0),m.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:e?"No se encontraron informes":"No hay informes generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:237,columnNumber:5},void 0),m.jsxDEV("p",{className:"text-gray-600 text-center max-w-md",children:e?`No se encontraron informes que coincidan con "${e}"`:"Aún no se han generado informes. Los informes aparecerán aquí una vez que se generen."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:240,columnNumber:5},void 0),e&&a&&m.jsxDEV(ae,{onClick:a,variant:"outline",className:"mt-4",children:"Limpiar búsqueda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:247,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:235,columnNumber:3},void 0));ps.displayName="EmptyState";const bs=P.memo(({informes:e=[],selectedInformes:a=new Set,onToggleSelection:i,onSelectAll:t,onDeselectAll:r,onViewInforme:s,onDeleteInforme:o,onViewChart:n,isLoading:l=!1,searchTerm:c="",onClearSearch:u,height:d=600})=>{const{allSelected:p,someSelected:b,selectedCount:f}=P.useMemo(()=>{const i=a.size,t=e.length;return{allSelected:t>0&&i===t,someSelected:i>0&&i<t,selectedCount:i}},[a.size,e.length]),g=P.useMemo(()=>({informes:e,selectedInformes:a,onToggleSelection:i,onViewInforme:s,onDeleteInforme:o,onViewChart:n,isLoading:l}),[e,a,i,s,o,n,l]);return l||0!==e.length?m.jsxDEV("div",{className:"bg-white rounded-lg border border-gray-200 overflow-hidden",role:"table","aria-label":"Tabla de informes generados",children:[m.jsxDEV(ms,{selectedCount:f,totalCount:e.length,onSelectAll:t,onDeselectAll:r,allSelected:p,someSelected:b},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:334,columnNumber:7},void 0),m.jsxDEV("div",{className:"relative",children:[m.jsxDEV(ts,{height:d-60,itemCount:l?10:e.length,itemSize:80,itemData:g,overscanCount:5,children:ds},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:345,columnNumber:9},void 0),l&&m.jsxDEV("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center",children:m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:359,columnNumber:15},void 0),m.jsxDEV("span",{className:"text-gray-600",children:"Cargando informes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:360,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:358,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:357,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:344,columnNumber:7},void 0),m.jsxDEV("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-200 text-sm text-gray-600",children:l?m.jsxDEV("span",{children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:369,columnNumber:11},void 0):m.jsxDEV("span",{children:["Mostrando ",e.length," informe",1!==e.length?"s":"",f>0&&m.jsxDEV("span",{className:"ml-2 text-blue-600",children:["• ",f," seleccionado",1!==f?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:374,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:371,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:367,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:329,columnNumber:5},void 0):m.jsxDEV("div",{className:"bg-white rounded-lg border border-gray-200",children:[m.jsxDEV(ms,{selectedCount:f,totalCount:e.length,onSelectAll:t,onDeselectAll:r,allSelected:p,someSelected:b},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:312,columnNumber:9},void 0),m.jsxDEV(ps,{searchTerm:c,onClearSearch:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:320,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/InformesTable.jsx",lineNumber:311,columnNumber:7},void 0)});bs.displayName="InformesTable";let fs,gs,Ns,xs={data:""},hs=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,vs=/\/\*[^]*?\*\/|  +/g,js=/\n+/g,Vs=(e,a)=>{let i="",t="",r="";for(let s in e){let o=e[s];"@"==s[0]?"i"==s[1]?i=s+" "+o+";":t+="f"==s[1]?Vs(o,s):s+"{"+Vs(o,"k"==s[1]?"":a)+"}":"object"==typeof o?t+=Vs(o,a?a.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,a=>/&/.test(a)?a.replace(/&/g,e):e?e+" "+a:a)):s):null!=o&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=Vs.p?Vs.p(s,o):s+":"+o+";")}return i+(a&&r?a+"{"+r+"}":r)+t},ys={},Es=e=>{if("object"==typeof e){let a="";for(let i in e)a+=i+Es(e[i]);return a}return e};function Cs(e){let a=this||{},i=e.call?e(a.p):e;return((e,a,i,t,r)=>{let s=Es(e),o=ys[s]||(ys[s]=(e=>{let a=0,i=11;for(;a<e.length;)i=101*i+e.charCodeAt(a++)>>>0;return"go"+i})(s));if(!ys[o]){let a=s!==e?e:(e=>{let a,i,t=[{}];for(;a=hs.exec(e.replace(vs,""));)a[4]?t.shift():a[3]?(i=a[3].replace(js," ").trim(),t.unshift(t[0][i]=t[0][i]||{})):t[0][a[1]]=a[2].replace(js," ").trim();return t[0]})(e);ys[o]=Vs(r?{["@keyframes "+o]:a}:a,i?"":"."+o)}let n=i&&ys.g?ys.g:null;return i&&(ys.g=ys[o]),l=ys[o],c=a,u=t,(d=n)?c.data=c.data.replace(d,l):-1===c.data.indexOf(l)&&(c.data=u?l+c.data:c.data+l),o;var l,c,u,d})(i.unshift?i.raw?((e,a,i)=>e.reduce((e,t,r)=>{let s=a[r];if(s&&s.call){let e=s(i),a=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=a?"."+a:e&&"object"==typeof e?e.props?"":Vs(e,""):!1===e?"":e}return e+t+(null==s?"":s)},""))(i,[].slice.call(arguments,1),a.p):i.reduce((e,i)=>Object.assign(e,i&&i.call?i(a.p):i),{}):i,(t=a.target,"object"==typeof window?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||xs),a.g,a.o,a.k);var t}Cs.bind({g:1});let Ds=Cs.bind({k:1});function Bs(e,a){let i=this||{};return function(){let a=arguments;return function t(r,s){let o=Object.assign({},r),n=o.className||t.className;i.p=Object.assign({theme:gs&&gs()},o),i.o=/ *go\d+/.test(n),o.className=Cs.apply(i,a)+(n?" "+n:"");let l=e;return e[0]&&(l=o.as||e,delete o.as),Ns&&l[0]&&Ns(o),fs(l,o)}}}var ws=(e,a)=>(e=>"function"==typeof e)(e)?e(a):e,As=(()=>{let e=0;return()=>(++e).toString()})(),Rs=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let a=matchMedia("(prefers-reduced-motion: reduce)");e=!a||a.matches}return e}})(),Ss="default",Ts=(e,a)=>{let{toastLimit:i}=e.settings;switch(a.type){case 0:return l(n({},e),{toasts:[a.toast,...e.toasts].slice(0,i)});case 1:return l(n({},e),{toasts:e.toasts.map(e=>e.id===a.toast.id?n(n({},e),a.toast):e)});case 2:let{toast:t}=a;return Ts(e,{type:e.toasts.find(e=>e.id===t.id)?1:0,toast:t});case 3:let{toastId:r}=a;return l(n({},e),{toasts:e.toasts.map(e=>e.id===r||void 0===r?l(n({},e),{dismissed:!0,visible:!1}):e)});case 4:return void 0===a.toastId?l(n({},e),{toasts:[]}):l(n({},e),{toasts:e.toasts.filter(e=>e.id!==a.toastId)});case 5:return l(n({},e),{pausedAt:a.time});case 6:let s=a.time-(e.pausedAt||0);return l(n({},e),{pausedAt:void 0,toasts:e.toasts.map(e=>l(n({},e),{pauseDuration:e.pauseDuration+s}))})}},Is=[],_s={toasts:[],pausedAt:void 0,settings:{toastLimit:20}},zs={},Os=(e,a=Ss)=>{zs[a]=Ts(zs[a]||_s,e),Is.forEach(([e,i])=>{e===a&&i(zs[a])})},Ps=e=>Object.keys(zs).forEach(a=>Os(e,a)),Ms=(e=Ss)=>a=>{Os(a,e)},ks=e=>(a,i)=>{let t=((e,a="blank",i)=>l(n({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0},i),{id:(null==i?void 0:i.id)||As()}))(a,e,i);return Ms(t.toasterId||(e=>Object.keys(zs).find(a=>zs[a].toasts.some(a=>a.id===e)))(t.id))({type:2,toast:t}),t.id},qs=(e,a)=>ks("blank")(e,a);qs.error=ks("error"),qs.success=ks("success"),qs.loading=ks("loading"),qs.custom=ks("custom"),qs.dismiss=(e,a)=>{let i={type:3,toastId:e};a?Ms(a)(i):Ps(i)},qs.dismissAll=e=>qs.dismiss(void 0,e),qs.remove=(e,a)=>{let i={type:4,toastId:e};a?Ms(a)(i):Ps(i)},qs.removeAll=e=>qs.remove(void 0,e),qs.promise=(e,a,i)=>{let t=qs.loading(a.loading,n(n({},i),null==i?void 0:i.loading));return"function"==typeof e&&(e=e()),e.then(e=>{let r=a.success?ws(a.success,e):void 0;return r?qs.success(r,n(n({id:t},i),null==i?void 0:i.success)):qs.dismiss(t),e}).catch(e=>{let r=a.error?ws(a.error,e):void 0;r?qs.error(r,n(n({id:t},i),null==i?void 0:i.error)):qs.dismiss(t)}),e};var Ls,$s,Fs,Us,Qs=Ds`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Ws=Ds`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Hs=Ds`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Gs=Bs("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Qs} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Ws} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Hs} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Js=Ds`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Ys=Bs("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Js} 1s linear infinite;
`,Ks=Ds`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Xs=Ds`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Zs=Bs("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ks} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Xs} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,eo=Bs("div")`
  position: absolute;
`,ao=Bs("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,io=Ds`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,to=Bs("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${io} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ro=({toast:e})=>{let{icon:a,type:i,iconTheme:t}=e;return void 0!==a?"string"==typeof a?P.createElement(to,null,a):a:"blank"===i?null:P.createElement(ao,null,P.createElement(Ys,n({},t)),"loading"!==i&&P.createElement(eo,null,"error"===i?P.createElement(Gs,n({},t)):P.createElement(Zs,n({},t))))},so=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,oo=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,no=Bs("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,lo=Bs("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`;P.memo(({toast:e,position:a,style:i,children:t})=>{let r=e.height?((e,a)=>{let i=e.includes("top")?1:-1,[t,r]=Rs()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[so(i),oo(i)];return{animation:a?`${Ds(t)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ds(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||a||"top-center",e.visible):{opacity:0},s=P.createElement(ro,{toast:e}),o=P.createElement(lo,n({},e.ariaProps),ws(e.message,e));return P.createElement(no,{className:e.className,style:n(n(n({},r),i),e.style)},"function"==typeof t?t({icon:s,message:o}):P.createElement(P.Fragment,null,s,o))}),Ls=P.createElement,Vs.p=$s,fs=Ls,gs=Fs,Ns=Us,Cs`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`;const co=({searchTerm:e,onSearchChange:a,onClearSearch:i,totalCount:t,selectedCount:r})=>m.jsxDEV("div",{className:"bg-white p-4 rounded-lg border border-gray-200 mb-6",children:m.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[m.jsxDEV("div",{className:"flex-1 max-w-md",children:m.jsxDEV("div",{className:"relative",children:[m.jsxDEV(B,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:32,columnNumber:13},void 0),m.jsxDEV("input",{type:"text",placeholder:"Buscar por paciente o título...",value:e,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500","aria-label":"Buscar informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:33,columnNumber:13},void 0),e&&m.jsxDEV("button",{onClick:i,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600","aria-label":"Limpiar búsqueda",children:"×"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:42,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:31,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:30,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center gap-4",children:[m.jsxDEV("div",{className:"text-sm text-gray-600",children:[t," informe",1!==t?"s":"",r>0&&m.jsxDEV("span",{className:"ml-2 text-blue-600 font-medium",children:["• ",r," seleccionado",1!==r?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:58,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:55,columnNumber:11},void 0),m.jsxDEV(ae,{variant:"outline",size:"sm",className:"flex items-center gap-2",children:[m.jsxDEV(w,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:69,columnNumber:13},void 0),"Filtros"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:64,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:54,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:28,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:27,columnNumber:5},void 0),uo=({selectedCount:e,onBulkDelete:a,onBulkExport:i,disabled:t})=>0===e?null:m.jsxDEV("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("div",{className:"flex items-center gap-2",children:m.jsxDEV("span",{className:"text-sm font-medium text-blue-900",children:[e," informe",1!==e?"s":""," seleccionado",1!==e?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:88,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:87,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center gap-2",children:[m.jsxDEV(ae,{variant:"outline",size:"sm",onClick:i,disabled:t,className:"flex items-center gap-2",children:[m.jsxDEV(A,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:101,columnNumber:13},void 0),"Exportar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:94,columnNumber:11},void 0),m.jsxDEV(ae,{variant:"destructive",size:"sm",onClick:a,disabled:t,className:"flex items-center gap-2",children:[m.jsxDEV(C,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:112,columnNumber:13},void 0),"Eliminar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:105,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:93,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:86,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:85,columnNumber:5},void 0),mo=()=>{const[e,a]=P.useState(""),[i,t]=P.useState(new Set),[r,s]=P.useState(1),o=((e,a)=>{const[i,t]=P.useState(e);return P.useEffect(()=>{const i=setTimeout(()=>{t(e)},a);return()=>{clearTimeout(i)}},[e,a]),i})(e,300),{informes:c,loading:u,error:p,totalCount:b,totalPages:f,refetch:N,deleteInforme:x,deleteBulkInformes:h}=(({itemsPerPage:e=10,autoRefresh:a=!1,refreshInterval:i=3e4}={})=>{const[t,r]=P.useState({informes:[],loading:!1,error:null,totalCount:0,currentPage:1,hasNextPage:!1,hasPrevPage:!1}),s=P.useRef(null),o=P.useRef(null),c=P.useRef(new Map),u=P.useCallback(e=>{r(a=>n(n({},a),e))},[]),m=P.useCallback((...a)=>d(null,[...a],function*(a=1,i={}){s.current&&s.current.abort(),s.current=new AbortController;const{signal:t}=s.current,r=`page-${a}-${e}`;if(c.current.has(r)&&!i.forceRefresh){const e=c.current.get(r);return void u(l(n({},e),{currentPage:a,loading:!1}))}try{u({loading:!0,error:null});const i=(a-1)*e,{data:s,error:o,count:m}=yield g.from("informes_generados").select("\n          id,\n          titulo,\n          descripcion,\n          fecha_generacion,\n          metadatos,\n          contenido,\n          pacientes:paciente_id (\n            id,\n            nombre,\n            apellido,\n            documento,\n            genero\n          )\n        ",{count:"exact"}).eq("tipo_informe","completo").eq("estado","generado").range(i,i+e-1).order("fecha_generacion",{ascending:!1}).abortSignal(t);if(o)throw o;const b={informes:yield Promise.all((s||[]).map(e=>d(null,null,function*(){var a;try{const{data:i,error:r}=yield g.from("resultados").select("\n                id,\n                puntaje_directo,\n                percentil,\n                errores,\n                tiempo_segundos,\n                concentracion,\n                created_at,\n                aptitudes:aptitud_id (\n                  codigo,\n                  nombre,\n                  descripcion\n                )\n              ").eq("paciente_id",null==(a=e.pacientes)?void 0:a.id).not("puntaje_directo","is",null).not("percentil","is",null).order("created_at",{ascending:!1}).limit(20).abortSignal(t);return l(n({},e),r?{resultados:[]}:{resultados:i||[]})}catch(p){if("AbortError"===p.name)throw p;return l(n({},e),{resultados:[]})}}))),totalCount:m||0,hasNextPage:i+e<(m||0),hasPrevPage:a>1,loading:!1,error:null};if(c.current.set(r,b),c.current.size>10){const e=c.current.keys().next().value;c.current.delete(e)}u(l(n({},b),{currentPage:a}))}catch(p){if("AbortError"===p.name)return;u({loading:!1,error:p.message||"Error al cargar los informes"})}}),[e,u]),b=P.useCallback(a=>{a>=1&&a<=Math.ceil(t.totalCount/e)&&m(a)},[m,t.totalCount,e]),f=P.useCallback(()=>{t.hasNextPage&&b(t.currentPage+1)},[b,t.hasNextPage,t.currentPage]),N=P.useCallback(()=>{t.hasPrevPage&&b(t.currentPage-1)},[b,t.hasPrevPage,t.currentPage]),x=P.useCallback(()=>{const a=`page-${t.currentPage}-${e}`;c.current.delete(a),m(t.currentPage,{forceRefresh:!0})},[m,t.currentPage,e]),h=P.useCallback(()=>{c.current.clear()},[]),v=P.useCallback(e=>d(null,null,function*(){try{u({loading:!0});const{error:a}=yield g.from("informes_generados").delete().eq("id",e);if(a)throw a;return h(),yield m(t.currentPage,{forceRefresh:!0}),{success:!0}}catch(p){return u({loading:!1,error:p.message||"Error al eliminar el informe"}),{success:!1,error:p.message}}}),[m,t.currentPage,h,u]),j=P.useCallback(e=>d(null,null,function*(){try{u({loading:!0});const{error:a}=yield g.from("informes_generados").delete().in("id",e);if(a)throw a;return h(),yield m(t.currentPage,{forceRefresh:!0}),{success:!0,deletedCount:e.length}}catch(p){return u({loading:!1,error:p.message||"Error al eliminar los informes"}),{success:!1,error:p.message}}}),[m,t.currentPage,h,u]);return P.useEffect(()=>{if(a&&i>0)return o.current=setInterval(()=>{x()},i),()=>{o.current&&clearInterval(o.current)}},[a,i,x]),P.useEffect(()=>()=>{s.current&&s.current.abort(),o.current&&clearInterval(o.current)},[]),P.useEffect(()=>{m(1)},[m]),{informes:t.informes,loading:t.loading,error:t.error,currentPage:t.currentPage,totalCount:t.totalCount,totalPages:Math.ceil(t.totalCount/e),hasNextPage:t.hasNextPage,hasPrevPage:t.hasPrevPage,fetchInformes:m,goToPage:b,nextPage:f,prevPage:N,refresh:x,clearCache:h,deleteInforme:v,bulkDeleteInformes:j}})({}),v=i.size;c.length>0&&c.length,v>0&&c.length;const j=P.useCallback(e=>{a(e),s(1)},[]),V=P.useCallback(()=>{a(""),s(1)},[]),y=P.useCallback(e=>{t(a=>{const i=new Set(a);return i.has(e)?i.delete(e):i.add(e),i})},[]),E=P.useCallback(()=>{t(new Set(c.map(e=>e.id)))},[c]),C=P.useCallback(()=>{t(new Set)},[]),B=P.useCallback(e=>{qs.success(`Abriendo informe: ${e.titulo}`)},[]),w=P.useCallback(e=>d(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este informe?"))try{yield x(e),t(a=>{const i=new Set(a);return i.delete(e),i}),qs.success("Informe eliminado correctamente")}catch(a){qs.error("Error al eliminar el informe")}}),[x]),A=P.useCallback(e=>{qs.success(`Abriendo gráfico: ${e.titulo}`)},[]),R=P.useCallback(()=>d(null,null,function*(){const e=v;if(window.confirm(`¿Está seguro de que desea eliminar ${e} informe${1!==e?"s":""}?`))try{yield h(Array.from(i)),t(new Set),qs.success(`${e} informe${1!==e?"s":""} eliminado${1!==e?"s":""} correctamente`)}catch(a){qs.error("Error al eliminar los informes")}}),[v,i,h]),S=P.useCallback(()=>{qs.success(`Exportando ${v} informe${1!==v?"s":""}...`)},[i,v]);return p?m.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-red-900 mb-2",children:"Error al cargar informes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:236,columnNumber:9},void 0),m.jsxDEV("p",{className:"text-red-700 mb-4",children:p.message},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:237,columnNumber:9},void 0),m.jsxDEV(ae,{onClick:N,variant:"outline",children:"Reintentar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:238,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:235,columnNumber:7},void 0):m.jsxDEV(_t,{children:m.jsxDEV("div",{className:"space-y-6",children:[m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("div",{children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900",children:"Informes Generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:251,columnNumber:13},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-1",children:"Gestiona y visualiza los informes de evaluación generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:252,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:250,columnNumber:11},void 0),m.jsxDEV(ae,{className:"flex items-center gap-2",children:[m.jsxDEV(D,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:258,columnNumber:13},void 0),"Nuevo Informe"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:257,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:249,columnNumber:9},void 0),m.jsxDEV(co,{searchTerm:e,onSearchChange:j,onClearSearch:V,totalCount:b,selectedCount:v},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:264,columnNumber:9},void 0),m.jsxDEV(uo,{selectedCount:v,onBulkDelete:R,onBulkExport:S,disabled:u},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:273,columnNumber:9},void 0),m.jsxDEV(bs,{informes:c,selectedInformes:i,onToggleSelection:y,onSelectAll:E,onDeselectAll:C,onViewInforme:B,onDeleteInforme:w,onViewChart:A,isLoading:u,searchTerm:o,onClearSearch:V,height:600},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:281,columnNumber:9},void 0),f>1&&m.jsxDEV("div",{className:"flex items-center justify-between bg-white p-4 rounded-lg border border-gray-200",children:[m.jsxDEV("div",{className:"text-sm text-gray-600",children:["Página ",r," de ",f]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:299,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex items-center gap-2",children:[m.jsxDEV(ae,{variant:"outline",size:"sm",onClick:()=>s(e=>Math.max(1,e-1)),disabled:1===r||u,children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:304,columnNumber:15},void 0),m.jsxDEV(ae,{variant:"outline",size:"sm",onClick:()=>s(e=>Math.min(f,e+1)),disabled:r===f||u,children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:313,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:303,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:298,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:247,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/reports/RefactoredInformesExample.jsx",lineNumber:246,columnNumber:5},void 0)},po=()=>{const e=M(),{resultados:a,aplicacionId:i,testCompletado:t,cargarResultados:r,cargando:s}=P.useContext(Mr),[o,n]=P.useState(!0),[l,c]=P.useState(null),[u,p]=P.useState({fortalezas:[],areas_mejora:[],recomendaciones:[]});P.useEffect(()=>{d(null,null,function*(){try{if(n(!0),!a&&i&&(yield r(i)),i){const e={fecha_evaluacion:"2025-06-12T10:00:00Z",candidatos:{id_candidato:"367894512",nombre:"Camila",apellido:"Vargas Vargas",sexo:"Femenino"}};e&&e.candidatos&&c({nombreCompleto:`${e.candidatos.nombre} ${e.candidatos.apellido}`,id_paciente:e.candidatos.id_candidato,sexo:e.candidatos.sexo,fecha_evaluacion:new Date(e.fecha_evaluacion).toLocaleDateString("es-ES",{day:"numeric",month:"long",year:"numeric"})})}a&&b(a)}catch(e){}finally{n(!1)}})},[i,a,r]);const b=e=>{const a=[],i=[],t=[];Object.entries(e).forEach(([e,t])=>{const{codigo:r,nombre:s,puntuacionCentil:o,interpretacion:n}=t;o>=70?a.push({codigo:r,nombre:s,interpretacion:`${n} (PC: ${o})`,descripcion:f(r,!0)}):o<=30&&i.push({codigo:r,nombre:s,interpretacion:`${n} (PC: ${o})`,descripcion:f(r,!1)})}),i.forEach(e=>{t.push({codigo:e.codigo,recomendacion:g(e.codigo)})}),p({fortalezas:a,areas_mejora:i,recomendaciones:t})},f=(e,a)=>{const i={V:{fortaleza:"Alta capacidad para comprender, utilizar y analizar el lenguaje escrito y hablado.",debilidad:"Dificultades para comprender conceptos expresados a través de palabras."},E:{fortaleza:"Excelente capacidad para visualizar y manipular mentalmente formas y patrones espaciales.",debilidad:"Dificultades para comprender relaciones espaciales y visualizar objetos en diferentes dimensiones."},A:{fortaleza:"Gran capacidad para mantener el foco en tareas específicas, detectando detalles con precisión.",debilidad:"Dificultad para mantener la concentración y detectar detalles específicos en tareas que requieren atención sostenida."},R:{fortaleza:"Destacada habilidad para identificar patrones lógicos y resolver problemas mediante el razonamiento.",debilidad:"Dificultades para identificar reglas lógicas y establecer inferencias en situaciones nuevas."},N:{fortaleza:"Excelente capacidad para comprender y manipular conceptos numéricos y resolver problemas matemáticos.",debilidad:"Dificultades en el manejo de conceptos numéricos y operaciones matemáticas básicas."},M:{fortaleza:"Buena comprensión de principios físicos y mecánicos básicos aplicados a situaciones cotidianas.",debilidad:"Dificultades para comprender el funcionamiento de dispositivos mecánicos y principios físicos básicos."},O:{fortaleza:"Excelente dominio de las reglas ortográficas y alta precisión en la escritura.",debilidad:"Dificultades con las reglas ortográficas y tendencia a cometer errores en la escritura."}};return i[e]?a?i[e].fortaleza:i[e].debilidad:"No hay descripción disponible."},g=e=>({V:"Fomentar la lectura diaria y realizar actividades que enriquezcan el vocabulario como juegos de palabras, debates y redacción.",E:"Practicar con rompecabezas, ejercicios de rotación mental, dibujo técnico y actividades que involucren navegación espacial.",A:"Realizar ejercicios de mindfulness, practicar tareas que requieran concentración por períodos cortos e ir aumentando gradualmente el tiempo.",R:"Resolver acertijos lógicos, participar en juegos de estrategia y analizar problemas complejos dividiéndolos en partes más sencillas.",N:"Practicar operaciones matemáticas diariamente, resolver problemas aplicados a la vida real y utilizar juegos que involucren cálculos.",M:"Construir modelos, experimentar con el funcionamiento de objetos cotidianos y estudiar los principios básicos de la física.",O:"Realizar ejercicios de dictado, revisión de textos y practicar la escritura consciente prestando atención a las reglas ortográficas."}[e]||"No hay recomendaciones específicas disponibles.");return o||s?m.jsxDEV("div",{className:"flex items-center justify-center h-screen",children:m.jsxDEV(It,{fullScreen:!0,message:"Cargando resultados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:219,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:218,columnNumber:7},void 0):a&&0!==Object.keys(a).length?m.jsxDEV("div",{className:"min-h-screen bg-gray-100 py-8 px-4 sm:px-6 lg:px-8",children:m.jsxDEV("div",{className:"max-w-5xl mx-auto",children:[m.jsxDEV("h1",{className:"text-3xl font-bold text-gray-800 mb-6",children:"Resultados Detallados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:255,columnNumber:9},void 0),l&&m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 flex items-center justify-between",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"mr-4",children:"Femenino"===l.sexo?m.jsxDEV(R,{className:"text-pink-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:263,columnNumber:19},void 0):"Masculino"===l.sexo?m.jsxDEV(S,{className:"text-blue-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:265,columnNumber:19},void 0):m.jsxDEV(v,{className:"text-gray-500 text-5xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:266,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:261,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:l.nombreCompleto},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:270,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:["ID: ",l.id_paciente]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:271,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:269,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:260,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-right",children:[m.jsxDEV("p",{className:"text-sm text-gray-700",children:"Fecha de Evaluación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:275,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-md font-semibold text-gray-800",children:l.fecha_evaluacion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:276,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:274,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:259,columnNumber:11},void 0),a&&Object.keys(a).length>0?m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg overflow-hidden mb-8",children:[m.jsxDEV("div",{className:"overflow-x-auto",children:m.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[m.jsxDEV("thead",{className:"bg-gray-50",children:m.jsxDEV("tr",{children:[m.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:292,columnNumber:21},void 0),m.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntaje PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:295,columnNumber:21},void 0),m.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Puntuación T"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:298,columnNumber:21},void 0),m.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Errores"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:301,columnNumber:21},void 0),m.jsxDEV("th",{scope:"col",className:"px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tiempo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:304,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:291,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:290,columnNumber:17},void 0),m.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:Object.entries(a).map(([e,a])=>m.jsxDEV("tr",{children:[m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:m.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:a.nombre||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:317,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:316,columnNumber:23},void 0),m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:m.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionDirecta?a.puntuacionDirecta:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:320,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:319,columnNumber:23},void 0),m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:m.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.puntuacionCentil?a.puntuacionCentil:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:324,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:322,columnNumber:23},void 0),m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:m.jsxDEV("div",{className:"text-sm text-gray-900",children:void 0!==a.errores?a.errores:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:328,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:326,columnNumber:23},void 0),m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:m.jsxDEV("div",{className:"text-sm text-gray-900",children:a.tiempo||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:332,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:330,columnNumber:23},void 0)]},e,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:315,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:313,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:289,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:288,columnNumber:13},void 0),m.jsxDEV("div",{className:"p-4 text-xs text-gray-500 bg-gray-50 border-t",children:[m.jsxDEV("p",{children:[m.jsxDEV("span",{className:"font-medium",children:"Puntaje PD:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:349,columnNumber:18},void 0)," Puntuación Directa - Número de respuestas correctas."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:349,columnNumber:15},void 0),m.jsxDEV("p",{children:[m.jsxDEV("span",{className:"font-medium",children:"Puntuación T:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:350,columnNumber:18},void 0)," Puntuación Transformada (ej. Percentil) - Posición relativa respecto a la población de referencia."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:350,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:348,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:287,columnNumber:11},void 0):m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg p-6 mb-8 text-center",children:m.jsxDEV("p",{className:"text-gray-600",children:"No hay resultados de pruebas para mostrar para este paciente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:355,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:354,columnNumber:11},void 0),(u.fortalezas.length>0||u.areas_mejora.length>0||u.recomendaciones.length>0)&&m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[m.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informe Cualitativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:362,columnNumber:13},void 0),m.jsxDEV("div",{className:"p-6",children:[u.fortalezas.length>0&&m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-green-700 mb-2",children:"Fortalezas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:367,columnNumber:19},void 0),m.jsxDEV("div",{className:"space-y-3",children:u.fortalezas.map((e,a)=>m.jsxDEV("div",{className:"bg-green-50 p-3 rounded-md",children:[m.jsxDEV("div",{className:"font-semibold text-green-800",children:[e.nombre,": ",e.interpretacion]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:371,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-sm text-green-700 mt-1",children:e.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:374,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:370,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:368,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:366,columnNumber:17},void 0),u.areas_mejora.length>0&&m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("h3",{className:"text-lg font-medium text-red-700 mb-2",children:"Áreas de Mejora"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:383,columnNumber:19},void 0),m.jsxDEV("div",{className:"space-y-3",children:u.areas_mejora.map((e,a)=>m.jsxDEV("div",{className:"bg-red-50 p-3 rounded-md",children:[m.jsxDEV("div",{className:"font-semibold text-red-800",children:[e.nombre,": ",e.interpretacion]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:387,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-sm text-red-700 mt-1",children:e.descripcion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:390,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:386,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:384,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:382,columnNumber:17},void 0),u.recomendaciones.length>0&&m.jsxDEV("div",{children:[m.jsxDEV("h3",{className:"text-lg font-medium text-blue-700 mb-2",children:"Recomendaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:399,columnNumber:19},void 0),m.jsxDEV("div",{className:"space-y-3",children:u.recomendaciones.map((e,i)=>{var t;return m.jsxDEV("div",{className:"bg-blue-50 p-3 rounded-md",children:[m.jsxDEV("div",{className:"font-semibold text-blue-800",children:[e.codigo," - ",null==(t=a[Object.keys(a).find(i=>a[i].codigo===e.codigo)])?void 0:t.nombre]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:403,columnNumber:25},void 0),m.jsxDEV("p",{className:"text-sm text-blue-700 mt-1",children:e.recomendacion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:404,columnNumber:25},void 0)]},i,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:402,columnNumber:23},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:400,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:398,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:363,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:361,columnNumber:12},void 0),m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[m.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Test de Conexión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:416,columnNumber:11},void 0),m.jsxDEV("div",{className:"p-6"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:417,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:415,columnNumber:9},void 0),m.jsxDEV("div",{className:"bg-white shadow-lg rounded-lg mb-8",children:[m.jsxDEV("h2",{className:"text-xl font-semibold p-4 bg-gray-50 border-b text-gray-800",children:"Informes Generados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:424,columnNumber:11},void 0),m.jsxDEV("div",{className:"p-6",children:m.jsxDEV(mo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:426,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:425,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:423,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 mt-8",children:[m.jsxDEV("button",{className:"px-6 py-3 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition duration-150",onClick:()=>e("/test"),children:"Volver"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:432,columnNumber:11},void 0),m.jsxDEV("div",{className:"flex space-x-3",children:[m.jsxDEV("button",{className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-150",onClick:()=>window.print(),children:"Imprimir Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:439,columnNumber:13},void 0),m.jsxDEV("button",{className:"px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition duration-150",onClick:()=>alert("Función de exportar a PDF en desarrollo."),children:"Exportar a PDF"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:445,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:438,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:431,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:254,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:253,columnNumber:5},void 0):m.jsxDEV("div",{className:"flex items-center justify-center h-screen bg-blue-50",children:m.jsxDEV("div",{className:"bg-white p-8 rounded-lg shadow-md max-w-md",children:m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("svg",{className:"mx-auto h-16 w-16 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:m.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:231,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:230,columnNumber:13},void 0),m.jsxDEV("h2",{className:"mt-4 text-xl font-bold text-gray-800",children:"No hay resultados disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:233,columnNumber:13},void 0),m.jsxDEV("p",{className:"mt-2 text-gray-600",children:"No se han encontrado resultados para mostrar. Es posible que aún no hayas completado el test o que haya ocurrido un error."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:234,columnNumber:13},void 0),m.jsxDEV("div",{className:"mt-6",children:m.jsxDEV("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",onClick:()=>e("/test"),children:"Volver a Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:238,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:237,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:229,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:228,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/Resultados.jsx",lineNumber:227,columnNumber:7},void 0)},bo=()=>m.jsxDEV(Q,{children:[m.jsxDEV(W,{path:"/instructions/:testId",element:m.jsxDEV(Lt,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:24,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:24,columnNumber:7},void 0),m.jsxDEV(W,{path:"/verbal",element:m.jsxDEV(Ht,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:27,columnNumber:38},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:27,columnNumber:7},void 0),m.jsxDEV(W,{path:"/ortografia",element:m.jsxDEV(hr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:28,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:28,columnNumber:7},void 0),m.jsxDEV(W,{path:"/razonamiento",element:m.jsxDEV(yr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:29,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:29,columnNumber:7},void 0),m.jsxDEV(W,{path:"/atencion",element:m.jsxDEV(Cr,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:30,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:30,columnNumber:7},void 0),m.jsxDEV(W,{path:"/espacial",element:m.jsxDEV(Br,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:31,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:31,columnNumber:7},void 0),m.jsxDEV(W,{path:"/mecanico",element:m.jsxDEV(Ar,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:32,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:32,columnNumber:7},void 0),m.jsxDEV(W,{path:"/numerico",element:m.jsxDEV(Ir,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:33,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:33,columnNumber:7},void 0),m.jsxDEV(W,{path:"/results/:resultId",element:m.jsxDEV(Or,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:36,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:36,columnNumber:7},void 0),m.jsxDEV(W,{path:"/resultados/:resultId",element:m.jsxDEV(po,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:37,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:37,columnNumber:7},void 0),m.jsxDEV(W,{path:"/",element:m.jsxDEV(H,{to:"/student/tests",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:40,columnNumber:32},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:40,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/test/testRoutes.jsx",lineNumber:22,columnNumber:5},void 0),fo=()=>m.jsxDEV("div",{className:"flex justify-center items-center min-h-screen",children:m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV(T,{className:"animate-spin text-blue-600 mx-auto mb-4 text-4xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:17,columnNumber:7},void 0),m.jsxDEV("p",{className:"text-gray-600 font-medium",children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:18,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:16,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:15,columnNumber:3},void 0),go=e=>a=>m.jsxDEV(_t,{children:m.jsxDEV(e,n({},a),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:26,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:25,columnNumber:3},void 0),No=e=>a=>m.jsxDEV(_t,{children:m.jsxDEV(P.Suspense,{fallback:m.jsxDEV(fo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:33,columnNumber:25},void 0),children:m.jsxDEV(e,n({},a),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:34,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:33,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:32,columnNumber:3},void 0),xo=No(P.lazy(()=>N(()=>import("./Dashboard-CzmZ2YPh.js"),__vite__mapDeps([7,1,2,3,8,4])))),ho=No(P.lazy(()=>N(()=>import("./Profile-CG8sshPl.js"),__vite__mapDeps([9,1,2,3])))),vo=No(P.lazy(()=>N(()=>import("./Settings-BtnAXd6r.js"),__vite__mapDeps([10,1,2,3])))),jo=No(P.lazy(()=>N(()=>import("./Home-KEGZC7aD.js"),__vite__mapDeps([11,1,2,3,5,6])))),Vo=No(P.lazy(()=>N(()=>import("./Help-zJ-JGg7W.js"),__vite__mapDeps([12,1,2,3,5,6])))),yo=No(P.lazy(()=>N(()=>import("./Configuracion-DHIDHWnV.js"),__vite__mapDeps([13,1,2,3,5,6,14,4])))),Eo=go(P.lazy(()=>N(()=>import("./Candidates-DEdWuYpf.js"),__vite__mapDeps([15,1,2,3,5,6,16])))),Co=go(P.lazy(()=>N(()=>import("./VerbalInfo-DMr_XPf4.js"),__vite__mapDeps([17,1,2,3,5,6])))),Do=No(P.lazy(()=>N(()=>import("./Users-76G2iQen.js"),__vite__mapDeps([18,1,2,3,5,6])))),Bo=No(P.lazy(()=>N(()=>import("./admin-COBm5LhQ.js").then(e=>e.I),__vite__mapDeps([5,1,2,3,6])))),wo=No(P.lazy(()=>N(()=>import("./Reports-D9-TYKru.js"),__vite__mapDeps([19,1,2,3,5,6,0,4,20])))),Ao=No(P.lazy(()=>N(()=>import("./Patients-B0GIPyGn.js"),__vite__mapDeps([21,1,2,3,5,6])))),Ro=No(P.lazy(()=>N(()=>import("./admin-COBm5LhQ.js").then(e=>e.A),__vite__mapDeps([5,1,2,3,6])))),So=No(P.lazy(()=>N(()=>import("./TestPage-DwpF-uOp.js"),__vite__mapDeps([22,1,2,3,5,6,8])))),To=No(P.lazy(()=>N(()=>import("./CompleteReport-DrjCEflC.js"),__vite__mapDeps([23,1,2,3,5,6,24])))),Io=No(P.lazy(()=>N(()=>import("./SavedReports-C8OVLklp.js"),__vite__mapDeps([25,1,2,3,5,6])))),_o=No(P.lazy(()=>N(()=>import("./ViewSavedReport-BVZoAplE.js"),__vite__mapDeps([26,1,2,3,5,6,24])))),zo=No(P.lazy(()=>N(()=>import("./PinAssignmentPanel-BTvLVMrV.js"),__vite__mapDeps([27,1,2,3,14])))),Oo=No(P.lazy(()=>N(()=>import("./Students-D9E4Btb8.js"),__vite__mapDeps([28,1,2,3,5,6])))),Po=No(P.lazy(()=>N(()=>import("./Tests-B0YJWWYQ.js"),__vite__mapDeps([29,1,2,3,5,6])))),Mo=No(P.lazy(()=>N(()=>import("./Reports-V6zzM1ZL.js"),__vite__mapDeps([30,1,2,3,5,6])))),ko=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>sn),void 0))),qo=No(P.lazy(()=>N(()=>import("./Tests-CBZ8QDe0.js"),__vite__mapDeps([31,1,2,3,32,33,5,6])))),Lo=No(P.lazy(()=>N(()=>import("./Results-Cl5RXDBq.js"),__vite__mapDeps([34,1,2,3,5,6,16])))),$o=No(P.lazy(()=>N(()=>import("./Patients-DMJlJz6N.js"),__vite__mapDeps([35,1,2,3,5,6,36,37])))),Fo=No(P.lazy(()=>N(()=>import("./Questionnaire-B_GPDx2Z.js"),__vite__mapDeps([38,1,2,3,32,33,5,6])))),Uo=No(P.lazy(()=>N(()=>import("./InformePaciente-CaWt0M_-.js"),__vite__mapDeps([39,1,2,3,5,6])))),Qo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>$t),void 0))),Wo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>Gt),void 0))),Ho=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>wr),void 0))),Go=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>Dr),void 0))),Jo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>Er),void 0))),Yo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>_r),void 0))),Ko=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>Rr),void 0))),Xo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>vr),void 0))),Zo=No(P.lazy(()=>N(()=>Promise.resolve().then(()=>Pr),void 0))),en=No(P.lazy(()=>N(()=>import("./BasicLogin-O_elgI_M.js"),__vite__mapDeps([40,1,2,3])))),an=()=>{const e=L();return P.useEffect(()=>{},[e.pathname]),m.jsxDEV(_t,{children:m.jsxDEV(P.Suspense,{fallback:m.jsxDEV(fo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:106,columnNumber:27},void 0),children:m.jsxDEV(Q,{children:[m.jsxDEV(W,{path:"/",element:m.jsxDEV(H,{to:"/home",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:109,columnNumber:36},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:109,columnNumber:11},void 0),m.jsxDEV(W,{path:"/login",element:m.jsxDEV(en,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:112,columnNumber:41},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:112,columnNumber:11},void 0),m.jsxDEV(W,{path:"/register",element:m.jsxDEV(H,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:113,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:113,columnNumber:11},void 0),m.jsxDEV(W,{path:"/auth/troubleshooting",element:m.jsxDEV(H,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:114,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:114,columnNumber:11},void 0),m.jsxDEV(W,{path:"/auth",element:m.jsxDEV(H,{to:"/login",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:115,columnNumber:40},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:115,columnNumber:11},void 0),m.jsxDEV(W,{path:"/force-admin",element:m.jsxDEV(H,{to:"/admin/administration",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:117,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:117,columnNumber:11},void 0),m.jsxDEV(W,{path:"/info/verbal",element:m.jsxDEV(Co,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:118,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:118,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/instructions/:testId",element:m.jsxDEV(Qo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:121,columnNumber:61},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:121,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/verbal",element:m.jsxDEV(Wo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:122,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:122,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/espacial",element:m.jsxDEV(Ho,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:123,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:123,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/atencion",element:m.jsxDEV(Go,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:124,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:124,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/razonamiento",element:m.jsxDEV(Jo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:125,columnNumber:53},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:125,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/numerico",element:m.jsxDEV(Yo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:126,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:126,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/mecanico",element:m.jsxDEV(Ko,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:127,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:127,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/ortografia",element:m.jsxDEV(Xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:128,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:128,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/results/:applicationId",element:m.jsxDEV(Zo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:129,columnNumber:63},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:129,columnNumber:11},void 0),m.jsxDEV(W,{path:"/test/*",element:m.jsxDEV(bo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:130,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:130,columnNumber:11},void 0),m.jsxDEV(W,{element:m.jsxDEV(Tt,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:133,columnNumber:27},void 0),children:[m.jsxDEV(W,{path:"/dashboard",element:m.jsxDEV(H,{to:"/admin/administration",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:135,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:135,columnNumber:13},void 0),m.jsxDEV(W,{path:"/profile",element:m.jsxDEV(ho,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:136,columnNumber:45},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:136,columnNumber:13},void 0),m.jsxDEV(W,{path:"/settings",element:m.jsxDEV(vo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:137,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:137,columnNumber:13},void 0),m.jsxDEV(W,{path:"/home",element:m.jsxDEV(jo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:138,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:138,columnNumber:13},void 0),m.jsxDEV(W,{path:"/help",element:m.jsxDEV(Vo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:139,columnNumber:42},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:139,columnNumber:13},void 0),m.jsxDEV(W,{path:"/configuracion",element:m.jsxDEV(yo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:140,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:140,columnNumber:13},void 0),m.jsxDEV(W,{path:"/admin",children:[m.jsxDEV(W,{index:!0,element:m.jsxDEV(Ro,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:144,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:144,columnNumber:15},void 0),m.jsxDEV(W,{path:"dashboard",element:m.jsxDEV(xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:145,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:145,columnNumber:15},void 0),m.jsxDEV(W,{path:"users",element:m.jsxDEV(Do,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:146,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:146,columnNumber:15},void 0),m.jsxDEV(W,{path:"institutions",element:m.jsxDEV(Bo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:147,columnNumber:51},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:147,columnNumber:15},void 0),m.jsxDEV(W,{path:"reports",element:m.jsxDEV(wo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:148,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:148,columnNumber:15},void 0),m.jsxDEV(W,{path:"patients",element:m.jsxDEV(Ao,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:149,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:149,columnNumber:15},void 0),m.jsxDEV(W,{path:"administration",element:m.jsxDEV(Ro,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:150,columnNumber:53},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:150,columnNumber:15},void 0),m.jsxDEV(W,{path:"configuracion",element:m.jsxDEV(yo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:151,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:151,columnNumber:15},void 0),m.jsxDEV(W,{path:"tests",element:m.jsxDEV(So,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:152,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:152,columnNumber:15},void 0),m.jsxDEV(W,{path:"pines",element:m.jsxDEV(zo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:153,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:153,columnNumber:15},void 0),m.jsxDEV(W,{path:"informe-completo/:patientId",element:m.jsxDEV(To,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:154,columnNumber:66},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:154,columnNumber:15},void 0),m.jsxDEV(W,{path:"informes-guardados",element:m.jsxDEV(Io,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:155,columnNumber:57},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:155,columnNumber:15},void 0),m.jsxDEV(W,{path:"informe-guardado/:reportId",element:m.jsxDEV(_o,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:156,columnNumber:65},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:156,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:143,columnNumber:13},void 0),m.jsxDEV(W,{path:"/professional",children:[m.jsxDEV(W,{index:!0,element:m.jsxDEV(xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:161,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:161,columnNumber:15},void 0),m.jsxDEV(W,{path:"dashboard",element:m.jsxDEV(xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:162,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:162,columnNumber:15},void 0),m.jsxDEV(W,{path:"students",element:m.jsxDEV(Oo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:163,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:163,columnNumber:15},void 0),m.jsxDEV(W,{path:"tests",element:m.jsxDEV(Po,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:164,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:164,columnNumber:15},void 0),m.jsxDEV(W,{path:"reports",element:m.jsxDEV(Mo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:165,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:165,columnNumber:15},void 0),m.jsxDEV(W,{path:"candidates",element:m.jsxDEV(Eo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:166,columnNumber:49},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:166,columnNumber:15},void 0),m.jsxDEV(W,{path:"patients",element:m.jsxDEV(ko,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:167,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:167,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:160,columnNumber:13},void 0),m.jsxDEV(W,{path:"/student",children:[m.jsxDEV(W,{index:!0,element:m.jsxDEV(xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:172,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:172,columnNumber:15},void 0),m.jsxDEV(W,{path:"dashboard",element:m.jsxDEV(xo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:173,columnNumber:48},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:173,columnNumber:15},void 0),m.jsxDEV(W,{path:"tests",element:m.jsxDEV(qo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:174,columnNumber:44},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:174,columnNumber:15},void 0),m.jsxDEV(W,{path:"questionnaire",element:m.jsxDEV(Fo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:175,columnNumber:52},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:175,columnNumber:15},void 0),m.jsxDEV(W,{path:"results",element:m.jsxDEV(Lo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:176,columnNumber:46},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:176,columnNumber:15},void 0),m.jsxDEV(W,{path:"informe/:pacienteId",element:m.jsxDEV(Uo,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:177,columnNumber:58},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:177,columnNumber:15},void 0),m.jsxDEV(W,{path:"patients",element:m.jsxDEV($o,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:178,columnNumber:47},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:178,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:171,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:133,columnNumber:11},void 0),m.jsxDEV(W,{path:"*",element:m.jsxDEV(H,{to:"/home",replace:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:183,columnNumber:36},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:183,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:107,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:106,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:105,columnNumber:5},void 0)},tn=()=>m.jsxDEV(G,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:m.jsxDEV(Mt,{children:m.jsxDEV(an,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:202,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:201,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/routes/AppRoutes.jsx",lineNumber:195,columnNumber:5},void 0);function rn(){return P.useEffect(()=>{const e=localStorage.getItem("userTheme");("dark"===e||"system"===e&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&document.body.classList.add("dark-mode");const a=window.matchMedia("(prefers-color-scheme: dark)"),i=e=>{"system"===localStorage.getItem("userTheme")&&(e.matches?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode"))};return a.addEventListener("change",i),()=>{a.removeEventListener("change",i)}},[]),m.jsxDEV("div",{className:"app",children:[m.jsxDEV(Y,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:48,columnNumber:7},this),m.jsxDEV(tn,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:61,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/App.jsx",lineNumber:46,columnNumber:5},this)}se.createRoot(document.getElementById("root")).render(m.jsxDEV($.StrictMode,{children:m.jsxDEV(xe,{store:Bt,children:m.jsxDEV(I,{children:m.jsxDEV(rn,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:17,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:16,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:15,columnNumber:5},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/main.jsx",lineNumber:14,columnNumber:3},void 0));const sn=Object.freeze(Object.defineProperty({__proto__:null},Symbol.toStringTag,{value:"Module"}));export{Qt as B,xr as P,Ut as a,zr as b,rs as c,ss as d,os as f,jr as g,Pt as u};
