import{j as e,J as s,s as t}from"./auth-BzDSP4i9.js";import{r as a,L as i}from"./react-vendor-C9XH6RF0.js";import{P as r,C as l,a as n,b as u,B as o}from"./admin-BucOs87s.js";import{u as d}from"./useToast-DZJDYUEa.js";import{B as m}from"./index-DNP2-XsR.js";import"./ui-vendor-COFtXQcG.js";const c=()=>{const[c,b]=a.useState([]),[p,N]=a.useState(!0),{showToast:x}=d();a.useEffect(()=>{var e,s,a;e=null,s=null,a=function*(){try{N(!0);const{data:e,error:s}=yield t.from("resultados").select("\n            id,\n            puntaje_directo,\n            percentil,\n            errores,\n            tiempo_segundos,\n            concentracion,\n\n            created_at,\n            pacientes:paciente_id (\n              id,\n              nombre,\n              apellido,\n              documento,\n              genero\n            ),\n            aptitudes:aptitud_id (\n              codigo,\n              nombre,\n              descripcion\n            )\n          ").order("created_at",{ascending:!1});if(s)return void x("Error al cargar los resultados","error");const a=e.reduce((e,s)=>{var t,a,i;const r=null==(t=s.pacientes)?void 0:t.id;if(!r)return e;e[r]||(e[r]={paciente:s.pacientes,resultados:[],fechaUltimaEvaluacion:s.created_at});const l=s.percentil?m.obtenerInterpretacionPC(s.percentil):{nivel:"Pendiente",color:"text-gray-600",bg:"bg-gray-100"};return e[r].resultados.push({id:s.id,test:(null==(a=s.aptitudes)?void 0:a.codigo)||"N/A",testName:(null==(i=s.aptitudes)?void 0:i.nombre)||"Test Desconocido",puntajePD:s.puntaje_directo||0,puntajePC:s.percentil||"N/A",errores:s.errores||0,tiempo:s.tiempo_segundos?`${Math.round(s.tiempo_segundos/60)}:${String(s.tiempo_segundos%60).padStart(2,"0")}`:"N/A",concentracion:s.concentracion?`${s.concentracion.toFixed(1)}%`:"N/A",fecha:new Date(s.created_at).toLocaleDateString("es-ES"),interpretacion:l.nivel,interpretacionColor:l.color,interpretacionBg:l.bg}),new Date(s.created_at)>new Date(e[r].fechaUltimaEvaluacion)&&(e[r].fechaUltimaEvaluacion=s.created_at),e},{}),i=Object.values(a).sort((e,s)=>new Date(s.fechaUltimaEvaluacion)-new Date(e.fechaUltimaEvaluacion));b(i),N(!1)}catch(e){x("Error al cargar los resultados","error"),N(!1)}},new Promise((t,i)=>{var r=e=>{try{n(a.next(e))}catch(s){i(s)}},l=e=>{try{n(a.throw(e))}catch(s){i(s)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(r,l);n((a=a.apply(e,s)).next())})},[x]);const f=e=>({V:"fas fa-comments",E:"fas fa-cube",A:"fas fa-eye",R:"fas fa-puzzle-piece",N:"fas fa-calculator",M:"fas fa-cogs",O:"fas fa-spell-check"}[e]||"fas fa-clipboard-list");return e.jsxDEV("div",{children:[e.jsxDEV(r,{title:"Resultados de Tests",subtitle:`${c.length} paciente${1!==c.length?"s":""} con resultados disponibles`,icon:s},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:140,columnNumber:7},void 0),e.jsxDEV("div",{className:"container mx-auto py-6",children:p?e.jsxDEV("div",{className:"py-16 text-center",children:[e.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:150,columnNumber:11},void 0),e.jsxDEV("p",{className:"text-gray-500",children:"Cargando resultados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:151,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:149,columnNumber:9},void 0):e.jsxDEV(e.Fragment,{children:0===c.length?e.jsxDEV(l,{children:e.jsxDEV(n,{children:e.jsxDEV("div",{className:"py-8 text-center",children:[e.jsxDEV("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:159,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-gray-500",children:"No hay resultados de tests disponibles."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:160,columnNumber:19},void 0),e.jsxDEV("p",{className:"text-sm text-gray-400 mt-2",children:"Los resultados aparecerán aquí una vez que se completen los tests."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:161,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:158,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:157,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:156,columnNumber:13},void 0):e.jsxDEV("div",{className:"space-y-6",children:c.map((s,t)=>{var a,r,d,m;return e.jsxDEV(l,{className:"overflow-hidden shadow-lg border border-blue-200",children:[e.jsxDEV(u,{className:"bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300",children:e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-14 h-14 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4 shadow-lg "+(null==(a=s.paciente)||a.genero,"bg-white bg-opacity-20 border-2 border-white border-opacity-30"),children:e.jsxDEV("i",{className:"fas "+("masculino"===(null==(r=s.paciente)?void 0:r.genero)?"fa-mars text-blue-100":"fa-venus text-pink-200")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:177,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:174,columnNumber:25},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"text-xl font-bold text-white",children:[null==(d=s.paciente)?void 0:d.nombre," ",null==(m=s.paciente)?void 0:m.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:180,columnNumber:27},void 0),e.jsxDEV("p",{className:"text-blue-100 text-sm",children:[e.jsxDEV("i",{className:"fas fa-clipboard-check mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:184,columnNumber:29},void 0),s.resultados.length," test",1!==s.resultados.length?"s":""," completado",1!==s.resultados.length?"s":""]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:183,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:179,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:173,columnNumber:23},void 0),e.jsxDEV("div",{className:"flex items-center space-x-4",children:[e.jsxDEV("div",{className:"text-right",children:[e.jsxDEV("p",{className:"text-blue-100 text-sm",children:"Última evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:191,columnNumber:27},void 0),e.jsxDEV("p",{className:"text-white font-semibold",children:new Date(s.fechaUltimaEvaluacion).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:192,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:190,columnNumber:25},void 0),e.jsxDEV(o,{as:i,to:`/student/informe-completo/${s.paciente.id}`,className:"bg-white text-blue-600 hover:bg-blue-50 border-white shadow-lg",size:"sm",children:[e.jsxDEV("i",{className:"fas fa-file-alt mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:202,columnNumber:27},void 0),"Ver Informe Completo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:196,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:189,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:172,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:171,columnNumber:19},void 0),e.jsxDEV(n,{className:"p-0",children:e.jsxDEV("div",{className:"overflow-x-auto",children:e.jsxDEV("table",{className:"w-full",children:[e.jsxDEV("thead",{className:"bg-blue-50 border-b border-blue-200",children:e.jsxDEV("tr",{children:[e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:213,columnNumber:29},void 0),e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:216,columnNumber:29},void 0),e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Puntaje PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:219,columnNumber:29},void 0),e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Errores"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:222,columnNumber:29},void 0),e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Tiempo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:225,columnNumber:29},void 0),e.jsxDEV("th",{className:"px-4 py-3 text-center text-xs font-medium text-blue-700 uppercase tracking-wider",children:"Fecha Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:228,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:212,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:211,columnNumber:25},void 0),e.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:s.resultados.map((s,t)=>{return e.jsxDEV("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:[e.jsxDEV("td",{className:"px-4 py-4 text-center",children:e.jsxDEV("div",{className:"flex items-center justify-center",children:[e.jsxDEV("div",{className:`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2 ${a=s.test,{V:"text-blue-600",E:"text-indigo-600",A:"text-red-600",R:"text-amber-600",N:"text-teal-600",M:"text-slate-600",O:"text-green-600"}[a]||"text-gray-600"}`,children:e.jsxDEV("i",{className:f(s.test)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:239,columnNumber:37},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:238,columnNumber:35},void 0),e.jsxDEV("div",{className:"text-left",children:[e.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:s.test},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:242,columnNumber:37},void 0),e.jsxDEV("div",{className:"text-xs text-gray-500",children:s.testName},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:243,columnNumber:37},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:241,columnNumber:35},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:237,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:236,columnNumber:31},void 0),e.jsxDEV("td",{className:"px-4 py-4 text-center",children:e.jsxDEV("span",{className:"text-lg font-bold text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:s.puntajePD},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:248,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:247,columnNumber:31},void 0),e.jsxDEV("td",{className:"px-4 py-4 text-center",children:"N/A"!==s.puntajePC?e.jsxDEV("div",{className:"flex flex-col items-center",children:[e.jsxDEV("span",{className:"text-lg font-bold text-blue-600 bg-blue-100 px-3 py-1 rounded-full mb-1",children:s.puntajePC},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:255,columnNumber:37},void 0),e.jsxDEV("span",{className:`text-xs px-2 py-1 rounded-full ${s.interpretacionBg} ${s.interpretacionColor}`,children:s.interpretacion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:258,columnNumber:37},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:254,columnNumber:35},void 0):e.jsxDEV("span",{className:"text-gray-400 text-sm",children:"Pendiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:263,columnNumber:35},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:252,columnNumber:31},void 0),e.jsxDEV("td",{className:"px-4 py-4 text-center",children:e.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:s.errores},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:267,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:266,columnNumber:31},void 0),e.jsxDEV("td",{className:"px-4 py-4 text-center",children:e.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:s.tiempo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:270,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:269,columnNumber:31},void 0),e.jsxDEV("td",{className:"px-4 py-4 text-center",children:e.jsxDEV("span",{className:"text-sm text-gray-500",children:s.fecha},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:273,columnNumber:33},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:272,columnNumber:31},void 0)]},s.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:235,columnNumber:29},void 0);var a})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:233,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:210,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:209,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:208,columnNumber:19},void 0)]},s.paciente.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:170,columnNumber:17},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:168,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:154,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:146,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Results.jsx",lineNumber:138,columnNumber:5},void 0)};export{c as default};
