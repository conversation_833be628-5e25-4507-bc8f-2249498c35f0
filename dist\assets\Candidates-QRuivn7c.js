var e=Object.defineProperty,a=Object.defineProperties,i=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,n=(a,i,s)=>i in a?e(a,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[i]=s,t=(e,a)=>{for(var i in a||(a={}))r.call(a,i)&&n(e,i,a[i]);if(s)for(var i of s(a))l.call(a,i)&&n(e,i,a[i]);return e},d=(e,s)=>a(e,i(s)),o=(e,a)=>{var i={};for(var n in e)r.call(e,n)&&a.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&s)for(var n of s(e))a.indexOf(n)<0&&l.call(e,n)&&(i[n]=e[n]);return i};import{j as m}from"./auth-BzDSP4i9.js";import{r as c}from"./react-vendor-C9XH6RF0.js";import{B as u,C as b,b as N,a as p}from"./admin-BucOs87s.js";import{u as f}from"./useToast-DZJDYUEa.js";import"./ui-vendor-COFtXQcG.js";const g=e=>{var a=e,{type:i="text",name:s,value:r,onChange:l,placeholder:n,disabled:d=!1,readOnly:c=!1,error:u,className:b=""}=a,N=o(a,["type","name","value","onChange","placeholder","disabled","readOnly","error","className"]);const p=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${u?"border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${d?"bg-gray-100 cursor-not-allowed":""} ${b}`;return m.jsxDEV("input",t({type:i,name:s,value:r,onChange:l,placeholder:n,disabled:d,readOnly:c,className:p,"aria-invalid":u?"true":"false"},N),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Input.jsx",lineNumber:21,columnNumber:5},void 0)},x=e=>{var a=e,{options:i=[],value:s,onChange:r,placeholder:l="Seleccionar...",displayKey:n="label",valueKey:u="value",name:b,disabled:N=!1,error:p,className:f=""}=a,g=o(a,["options","value","onChange","placeholder","displayKey","valueKey","name","disabled","error","className"]);const[x,v]=c.useState(!1),h=c.useRef(null),j=i.find(e=>e[u]===s),C=j?j[n]:l;c.useEffect(()=>{const e=e=>{h.current&&!h.current.contains(e.target)&&v(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const V=`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${p?"border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500":"border-gray-300"} ${N?"bg-gray-100 cursor-not-allowed":"cursor-pointer"} ${f}`;return m.jsxDEV("div",{className:"relative",ref:h,children:[m.jsxDEV("div",d(t({className:V,onClick:()=>{N||v(!x)},tabIndex:0,role:"button","aria-haspopup":"listbox","aria-expanded":x},g),{children:m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("span",{className:j?"":"text-gray-500",children:C},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:69,columnNumber:11},void 0),m.jsxDEV("svg",{className:"w-5 h-5 text-gray-400 transition-transform "+(x?"transform rotate-180":""),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:m.jsxDEV("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:78,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:72,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:68,columnNumber:9},void 0)}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:59,columnNumber:7},void 0),x&&m.jsxDEV("div",{className:"absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md max-h-60 overflow-auto border border-gray-200",children:m.jsxDEV("ul",{className:"py-1",role:"listbox",children:[i.map((e,a)=>m.jsxDEV("li",{className:"px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 "+(e[u]===s?"bg-primary-50 text-primary-700":""),onClick:()=>(e=>{r(e[u],b),v(!1)})(e),role:"option","aria-selected":e[u]===s,children:e[n]},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:91,columnNumber:15},void 0)),0===i.length&&m.jsxDEV("li",{className:"px-3 py-2 text-sm text-gray-500",children:"No hay opciones disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:104,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:89,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:88,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Select.jsx",lineNumber:58,columnNumber:5},void 0)},v=e=>{var a=e,{data:i=[],columns:s=[],pagination:r=null,searchable:l=!1,className:n=""}=a,u=o(a,["data","columns","pagination","searchable","className"]);const[b,N]=c.useState(1),[p,f]=c.useState(""),[x,v]=c.useState(i),h=(null==r?void 0:r.pageSize)||10,j=Math.ceil(x.length/h);c.useEffect(()=>{if(l&&p){const e=i.filter(e=>s.some(a=>{if(!a.accessor)return!1;const i=e[a.accessor];return null!=i&&String(i).toLowerCase().includes(p.toLowerCase())}));v(e),N(1)}else v(i)},[p,i,s,l]);const C=r?x.slice((b-1)*h,b*h):x,V=e=>{N(e)};return m.jsxDEV("div",{className:"overflow-hidden",children:[l&&m.jsxDEV("div",{className:"mb-4",children:m.jsxDEV(g,{type:"text",placeholder:"Buscar...",value:p,onChange:e=>f(e.target.value),className:"max-w-xs"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:53,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:52,columnNumber:9},void 0),m.jsxDEV("div",{className:"overflow-x-auto",children:m.jsxDEV("table",d(t({className:`min-w-full divide-y divide-gray-200 ${n}`},u),{children:[m.jsxDEV("thead",{className:"bg-gray-50",children:m.jsxDEV("tr",{children:s.map((e,a)=>m.jsxDEV("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.header},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:68,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:66,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:65,columnNumber:11},void 0),m.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:C.length>0?C.map((e,a)=>m.jsxDEV("tr",{className:"hover:bg-gray-50",children:s.map((a,i)=>m.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:a.cell?a.cell({value:e[a.accessor],row:e}):e[a.accessor]},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:83,columnNumber:21},void 0))},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:81,columnNumber:17},void 0)):m.jsxDEV("tr",{children:m.jsxDEV("td",{colSpan:s.length,className:"px-6 py-4 text-center text-sm text-gray-500",children:"No hay datos disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:93,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:92,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:78,columnNumber:11},void 0)]}),void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:64,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:63,columnNumber:7},void 0),r&&j>1&&m.jsxDEV("nav",{className:"flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4",children:[m.jsxDEV("div",{className:"hidden sm:block",children:m.jsxDEV("p",{className:"text-sm text-gray-700",children:["Mostrando ",m.jsxDEV("span",{className:"font-medium",children:(b-1)*h+1},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:109,columnNumber:25},void 0)," ","a"," ",m.jsxDEV("span",{className:"font-medium",children:Math.min(b*h,x.length)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:111,columnNumber:15},void 0)," ","de ",m.jsxDEV("span",{className:"font-medium",children:x.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:114,columnNumber:18},void 0)," resultados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:108,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:107,columnNumber:11},void 0),m.jsxDEV("div",{className:"flex-1 flex justify-between sm:justify-end",children:[m.jsxDEV("button",{onClick:()=>V(b-1),disabled:1===b,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(1===b?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:118,columnNumber:13},void 0),m.jsxDEV("div",{className:"hidden md:flex mx-2",children:[...Array(j)].map((e,a)=>m.jsxDEV("button",{onClick:()=>V(a+1),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium "+(b===a+1?"bg-primary-50 border-primary-500 text-primary-700":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"),children:a+1},a,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:131,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:129,columnNumber:13},void 0),m.jsxDEV("button",{onClick:()=>V(b+1),disabled:b===j,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md "+(b===j?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"),children:"Siguiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:144,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:117,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:106,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Table.jsx",lineNumber:50,columnNumber:5},void 0)},h=()=>{const[e,a]=c.useState([]),[i,s]=c.useState(!1),[r,l]=c.useState(!1),[n,o]=c.useState(null),[h,j]=c.useState({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),{showSuccess:C,showError:V,showInfo:E}=f();c.useEffect(()=>{s(!0),setTimeout(()=>{a([{id:1,firstName:"Juan",lastName:"Pérez",email:"<EMAIL>",gender:"male",age:28,phone:"+591 72345678",education:"Licenciatura en Psicología",position:"Psicólogo Clínico"},{id:2,firstName:"María",lastName:"González",email:"<EMAIL>",gender:"female",age:32,phone:"+591 73456789",education:"Maestría en Recursos Humanos",position:"Especialista en RRHH"},{id:3,firstName:"Carlos",lastName:"Rodríguez",email:"<EMAIL>",gender:"male",age:25,phone:"+591 74567890",education:"Ingeniería en Sistemas",position:"Desarrollador Web"}]),s(!1)},800)},[]);const y=e=>{const{name:a,value:i}=e.target;j(d(t({},h),{[a]:i}))},B=()=>{j({id:null,firstName:"",lastName:"",email:"",gender:"",age:"",phone:"",education:"",position:""}),o(null)},D=(e=null)=>{e?(j({id:e.id,firstName:e.firstName,lastName:e.lastName,email:e.email,gender:e.gender,age:e.age.toString(),phone:e.phone,education:e.education,position:e.position}),o(e)):B(),l(!0)},w=()=>{l(!1),B()},S=[{header:"",accessor:"gender",cell:({value:e})=>m.jsxDEV("div",{className:"flex justify-center",children:"male"===e?m.jsxDEV("div",{className:"w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center",children:m.jsxDEV("i",{className:"fas fa-mars text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:211,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:210,columnNumber:13},void 0):"female"===e?m.jsxDEV("div",{className:"w-8 h-8 rounded-lg bg-pink-100 flex items-center justify-center",children:m.jsxDEV("i",{className:"fas fa-venus text-pink-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:215,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:214,columnNumber:13},void 0):m.jsxDEV("div",{className:"w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center",children:m.jsxDEV("i",{className:"fas fa-genderless text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:219,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:218,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:208,columnNumber:9},void 0)},{header:"Nombre",accessor:"firstName",cell:({value:e,row:a})=>m.jsxDEV("div",{children:[m.jsxDEV("div",{className:"font-medium text-gray-900",children:[e," ",a.lastName]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:230,columnNumber:11},void 0),m.jsxDEV("div",{className:"text-xs text-gray-500",children:a.position||"Sin cargo asignado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:233,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:229,columnNumber:9},void 0)},{header:"Email",accessor:"email",cell:({value:e})=>m.jsxDEV("div",{children:[m.jsxDEV("div",{className:"text-gray-900",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:244,columnNumber:11},void 0),m.jsxDEV("div",{className:"text-xs text-gray-500",children:[m.jsxDEV("i",{className:"fas fa-envelope mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:246,columnNumber:13},void 0)," Contacto principal"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:245,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:243,columnNumber:9},void 0)},{header:"Teléfono",accessor:"phone"},{header:"Edad",accessor:"age",cell:({value:e})=>m.jsxDEV("div",{className:"text-center bg-gray-100 rounded-lg py-1 px-2 w-12",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:259,columnNumber:9},void 0)},{header:"Formación",accessor:"education"},{header:"Acciones",accessor:"id",cell:({value:i,row:r})=>m.jsxDEV("div",{className:"flex space-x-2",children:[m.jsxDEV("button",{onClick:()=>D(r),className:"text-blue-600 hover:text-sky-800 bg-blue-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Editar",children:m.jsxDEV("i",{className:"fas fa-edit"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:278,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:273,columnNumber:11},void 0),m.jsxDEV("button",{onClick:()=>{return r=i,void(window.confirm("¿Está seguro de eliminar este candidato?")&&(s(!0),setTimeout(()=>{a(e.filter(e=>e.id!==r)),E("Candidato eliminado correctamente"),s(!1)},600)));var r},className:"text-red-600 hover:text-red-800 bg-red-100 p-2 rounded-lg focus:outline-none transition-all duration-200",title:"Eliminar",children:m.jsxDEV("i",{className:"fas fa-trash-alt"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:285,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:280,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:272,columnNumber:9},void 0)}];return m.jsxDEV("div",{className:"container mx-auto py-6",children:[m.jsxDEV("div",{className:"mb-6 flex flex-col md:flex-row md:items-center md:justify-between",children:[m.jsxDEV("div",{children:[m.jsxDEV("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Gestión de Candidatos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:303,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600",children:"Administre la información de los candidatos para pruebas psicométricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:304,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:302,columnNumber:9},void 0),m.jsxDEV("div",{className:"mt-4 md:mt-0",children:m.jsxDEV(u,{variant:"primary",onClick:()=>D(),className:"flex items-center",children:[m.jsxDEV("i",{className:"fas fa-plus mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:312,columnNumber:13},void 0),"Nuevo Candidato"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:307,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:306,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:301,columnNumber:7},void 0),m.jsxDEV(b,{className:"overflow-hidden shadow-lg border-0 rounded-xl",children:[m.jsxDEV(N,{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white border-0",children:m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"bg-white/20 p-2 rounded-lg mr-3",children:m.jsxDEV("i",{className:"fas fa-user-tie text-xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:322,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:321,columnNumber:13},void 0),m.jsxDEV("h2",{className:"text-xl font-semibold",children:"Lista de Candidatos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:324,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:320,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:319,columnNumber:9},void 0),m.jsxDEV(p,{children:i?m.jsxDEV("div",{className:"py-16 text-center",children:m.jsxDEV("div",{className:"flex flex-col items-center justify-center",children:[m.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:331,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"Cargando datos..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:332,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:330,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:329,columnNumber:13},void 0):m.jsxDEV(v,{data:e,columns:S,pagination:{pageSize:5},searchable:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:336,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:327,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:318,columnNumber:7},void 0),r&&m.jsxDEV("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center",children:m.jsxDEV("div",{className:"relative mx-auto p-5 w-full max-w-md md:max-w-lg",children:m.jsxDEV("div",{className:"bg-white rounded-xl shadow-2xl overflow-hidden",children:[m.jsxDEV("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-white py-4 px-6 flex items-center justify-between",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"bg-white/20 h-8 w-8 rounded-lg flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-user-tie"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:354,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:353,columnNumber:19},void 0),m.jsxDEV("h3",{className:"text-lg font-medium",children:n?"Editar Candidato":"Nuevo Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:356,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:352,columnNumber:17},void 0),m.jsxDEV("button",{onClick:w,className:"text-white hover:text-gray-200 focus:outline-none",children:m.jsxDEV("i",{className:"fas fa-times"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:364,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:360,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:351,columnNumber:15},void 0),m.jsxDEV("form",{onSubmit:i=>{i.preventDefault(),(h.firstName.trim()?h.lastName.trim()?h.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(h.email)?h.gender?h.age.trim()||(V("La edad es obligatoria"),0):(V("El género es obligatorio"),0):(V("El formato de email no es válido"),0):(V("El email es obligatorio"),0):(V("El apellido es obligatorio"),0):(V("El nombre es obligatorio"),0))&&(s(!0),setTimeout(()=>{const i=d(t({},h),{id:h.id||Date.now(),age:parseInt(h.age,10)});n?(a(e.map(e=>e.id===i.id?i:e)),C("Candidato actualizado correctamente")):(a([...e,i]),C("Candidato creado correctamente")),s(!1),w()},600))},className:"p-6",children:[m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[m.jsxDEV("div",{children:[m.jsxDEV("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Nombre ",m.jsxDEV("span",{className:"text-red-500",children:"*"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:372,columnNumber:30},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:371,columnNumber:21},void 0),m.jsxDEV(g,{type:"text",id:"firstName",name:"firstName",value:h.firstName,onChange:y,placeholder:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:374,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:370,columnNumber:19},void 0),m.jsxDEV("div",{children:[m.jsxDEV("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:["Apellido ",m.jsxDEV("span",{className:"text-red-500",children:"*"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:386,columnNumber:32},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:385,columnNumber:21},void 0),m.jsxDEV(g,{type:"text",id:"lastName",name:"lastName",value:h.lastName,onChange:y,placeholder:"Apellido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:388,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:384,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:369,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-4",children:[m.jsxDEV("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["Email ",m.jsxDEV("span",{className:"text-red-500",children:"*"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:401,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:400,columnNumber:19},void 0),m.jsxDEV(g,{type:"email",id:"email",name:"email",value:h.email,onChange:y,placeholder:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:403,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:399,columnNumber:17},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[m.jsxDEV("div",{children:[m.jsxDEV("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-1",children:["Género ",m.jsxDEV("span",{className:"text-red-500",children:"*"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:416,columnNumber:30},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:415,columnNumber:21},void 0),m.jsxDEV(x,{id:"gender",name:"gender",value:h.gender,onChange:(e,a)=>{j(d(t({},h),{[a]:e}))},options:[{value:"male",label:"Masculino"},{value:"female",label:"Femenino"},{value:"other",label:"Otro"}],placeholder:"Seleccionar género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:418,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:414,columnNumber:19},void 0),m.jsxDEV("div",{children:[m.jsxDEV("label",{htmlFor:"age",className:"block text-sm font-medium text-gray-700 mb-1",children:["Edad ",m.jsxDEV("span",{className:"text-red-500",children:"*"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:430,columnNumber:28},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:429,columnNumber:21},void 0),m.jsxDEV(g,{type:"number",id:"age",name:"age",value:h.age,onChange:y,placeholder:"Edad",min:"18",max:"100"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:432,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:428,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:413,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-4",children:[m.jsxDEV("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:446,columnNumber:19},void 0),m.jsxDEV(g,{type:"text",id:"phone",name:"phone",value:h.phone,onChange:y,placeholder:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:449,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:445,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-4",children:[m.jsxDEV("label",{htmlFor:"education",className:"block text-sm font-medium text-gray-700 mb-1",children:"Formación Académica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:460,columnNumber:19},void 0),m.jsxDEV(g,{type:"text",id:"education",name:"education",value:h.education,onChange:y,placeholder:"Formación académica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:463,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:459,columnNumber:17},void 0),m.jsxDEV("div",{className:"mb-6",children:[m.jsxDEV("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"Cargo o Posición"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:474,columnNumber:19},void 0),m.jsxDEV(g,{type:"text",id:"position",name:"position",value:h.position,onChange:y,placeholder:"Cargo o posición"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:477,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:473,columnNumber:17},void 0),m.jsxDEV("div",{className:"flex justify-end space-x-3",children:[m.jsxDEV(u,{type:"button",variant:"outline",onClick:w,children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:488,columnNumber:19},void 0),m.jsxDEV(u,{type:"submit",variant:"primary",disabled:i,children:i?m.jsxDEV(m.Fragment,{children:[m.jsxDEV("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[m.jsxDEV("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:503,columnNumber:27},void 0),m.jsxDEV("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:504,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:502,columnNumber:25},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:501,columnNumber:23},void 0):"Guardar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:495,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:487,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:368,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:350,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:349,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:348,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/candidate/Candidates.jsx",lineNumber:300,columnNumber:5},void 0)};export{h as default};
