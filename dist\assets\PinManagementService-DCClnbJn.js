var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,r=(s,t,o)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):s[t]=o,a=(e,s)=>{for(var t in s||(s={}))i.call(s,t)&&r(e,t,s[t]);if(o)for(var t of o(s))n.call(s,t)&&r(e,t,s[t]);return e},l=(e,s,t)=>new Promise((o,i)=>{var n=e=>{try{a(t.next(e))}catch(s){i(s)}},r=e=>{try{a(t.throw(e))}catch(s){i(s)}},a=e=>e.done?o(e.value):Promise.resolve(e.value).then(n,r);a((t=t.apply(e,s)).next())});import{s as c}from"./auth-Cw5QfmsP.js";import{Q as d}from"./ui-vendor-COFtXQcG.js";const p=new class{getPsychologistsWithPinStats(){return l(this,null,function*(){try{const{data:e,error:s}=yield c.rpc("get_all_psychologists_pin_balance");if(s)throw s;return(e||[]).map(e=>({psicologo_id:e.psych_id,nombre_psicologo:e.psych_name,email_psicologo:e.psych_email,total_asignado:parseInt(e.total_asignado)||0,total_consumido:parseInt(e.total_consumido)||0,pines_restantes:parseInt(e.pines_disponibles)||0,ultima_transaccion:e.ultima_transaccion,pacientes_asignados:parseInt(e.pacientes_asignados)||0,tests_completados:parseInt(e.tests_completados)||0,status:this._determineStatus(parseInt(e.pines_disponibles)||0)}))}catch(e){throw e}})}assignPins(e,s,t="Asignación manual"){return l(this,null,function*(){if(!e||!s||s<=0){const e="Se requiere ID del psicólogo y una cantidad positiva de pines.";throw d.error(e),new Error(e)}try{const{data:o,error:i}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(i||!o){const e="Psicólogo no encontrado.";throw d.error(e),new Error(e)}const n={psicologo_id:e,cantidad:s,tipo:"asignacion",motivo:t,metadata:{psychologist_name:`${o.nombre} ${o.apellido}`,psychologist_email:o.email,assigned_at:(new Date).toISOString(),method:"simple_service"}},{data:r,error:a}=yield c.from("pines_transacciones").insert(n).select().single();if(a)throw d.error("No se pudo completar la asignación de pines."),a;return d.success(`Se asignaron ${s} pines a ${o.nombre} ${o.apellido} correctamente.`),r}catch(o){throw o}})}getPsychologistBalance(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.rpc("get_psychologist_pin_balance",{p_psicologo_id:e});if(t)throw t;return s&&0!==s.length?s[0]:{psicologo_id:e,total_asignado:0,total_consumido:0,pines_disponibles:0,ultima_transaccion:null}}catch(s){throw s}})}getTransactionHistory(e=null,s=50){return l(this,null,function*(){try{let t=c.from("pines_transacciones").select("\n          *,\n          psicologos (\n            nombre,\n            apellido,\n            email\n          )\n        ").order("created_at",{ascending:!1}).limit(s);e&&(t=t.eq("psicologo_id",e));const{data:o,error:i}=yield t;if(i)throw i;return o||[]}catch(t){throw t}})}getSystemStats(){return l(this,null,function*(){try{const e=yield this.getPsychologistsWithPinStats();return{total_psychologists:e.length,total_pins_assigned:e.reduce((e,s)=>e+s.total_asignado,0),total_pins_consumed:e.reduce((e,s)=>e+s.total_consumido,0),total_pins_available:e.reduce((e,s)=>e+s.pines_restantes,0),psychologists_with_pins:e.filter(e=>e.pines_restantes>0).length,psychologists_without_pins:e.filter(e=>0===e.pines_restantes).length,total_patients:e.reduce((e,s)=>e+s.pacientes_asignados,0),total_tests:e.reduce((e,s)=>e+s.tests_completados,0)}}catch(e){throw e}})}consumePin(e){return l(this,arguments,function*(e,o="Consumo automático",i={}){try{const l=yield this.getPsychologistBalance(e);if(l.pines_disponibles<=0){const e="El psicólogo no tiene pines disponibles.";throw d.error(e),new Error(e)}const p={psicologo_id:e,cantidad:-1,tipo:"consumo",motivo:o,metadata:(n=a({},i),r={consumed_at:(new Date).toISOString(),remaining_pins_before:l.pines_disponibles,remaining_pins_after:l.pines_disponibles-1},s(n,t(r)))},{data:u,error:g}=yield c.from("pines_transacciones").insert(p).select().single();if(g)throw g;return u}catch(l){throw l}var n,r})}_determineStatus(e){return 0===e?"sin_pines":e<=5?"pocos_pines":"activo"}deleteTransaction(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.from("pines_transacciones").select("psicologo_id, cantidad, tipo").eq("id",e).single();if(t)throw t;const{data:o,error:i}=yield c.from("pines_transacciones").delete().eq("id",e).select().single();if(i)throw i;return yield this._refreshPsychologistMetrics(s.psicologo_id),{success:!0,data:o,affectedPsychologist:s.psicologo_id}}catch(s){return{success:!1,message:s.message}}})}deleteMultipleTransactions(e){return l(this,null,function*(){try{if(!e||0===e.length)throw new Error("No se proporcionaron IDs para eliminar");const{data:s,error:t}=yield c.from("pines_transacciones").select("psicologo_id, cantidad, tipo").in("id",e);if(t)throw t;const o=[...new Set(s.map(e=>e.psicologo_id))],{data:i,error:n}=yield c.from("pines_transacciones").delete().in("id",e).select();if(n)throw n;for(const e of o)yield this._refreshPsychologistMetrics(e);return{success:!0,deletedCount:i.length,data:i,affectedPsychologists:o}}catch(s){return{success:!1,message:s.message}}})}removePinsFromPsychologist(e,s,t="Eliminación manual de pines"){return l(this,null,function*(){try{if(!e||!s||s<=0)throw new Error("Se requiere ID del psicólogo y una cantidad positiva de pines.");const{data:o,error:i}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(i||!o)throw new Error("Psicólogo no encontrado.");const n=yield this.getPsychologistBalance(e);if(n.pines_disponibles<s)throw new Error(`No se pueden eliminar ${s} pines. Solo hay ${n.pines_disponibles} disponibles.`);const r={psicologo_id:e,cantidad:-s,tipo:"eliminacion",motivo:t,metadata:{psychologist_name:`${o.nombre} ${o.apellido}`,psychologist_email:o.email,removed_at:(new Date).toISOString(),method:"manual_removal",original_amount:s}},{data:a,error:l}=yield c.from("pines_transacciones").insert([r]).select().single();if(l)throw l;return yield this._refreshPsychologistMetrics(e),{success:!0,data:a,removedAmount:s}}catch(o){return d.error(o.message),{success:!1,message:o.message}}})}removePsychologistPinAssignment(e,s="Eliminación completa de asignación"){return l(this,null,function*(){try{const{data:s,error:t}=yield c.from("psicologos").select("id, nombre, apellido, email").eq("id",e).single();if(t||!s)throw new Error("Psicólogo no encontrado.");const{data:o,error:i}=yield c.from("pines_transacciones").select("id, cantidad, tipo").eq("psicologo_id",e);if(i)throw i;if(!o||0===o.length)return yield this._refreshPsychologistMetrics(e),{success:!0,data:[],deletedTransactions:0,psychologist:`${s.nombre} ${s.apellido}`,psychologist_name:`${s.nombre} ${s.apellido}`,message:"No había transacciones para eliminar"};const n=o.map(e=>e.id),{data:r,error:a}=yield c.from("pines_transacciones").delete().in("id",n).select();if(a)throw a;return yield this._refreshPsychologistMetrics(e),{success:!0,data:r,deletedTransactions:r.length,psychologist:`${s.nombre} ${s.apellido}`,psychologist_name:`${s.nombre} ${s.apellido}`}}catch(s){return d.error(s.message),{success:!1,message:s.message}}})}removeMultiplePsychologistAssignments(e,s="Eliminación múltiple de asignaciones"){return l(this,null,function*(){try{if(!e||0===e.length)throw new Error("No se proporcionaron IDs de psicólogos para eliminar");const o=[];let i=0,n=0,r=0,a=0;for(const l of e){try{const e=yield this.removePsychologistPinAssignment(l,s);e.success?(i++,r+=e.deletedTransactions,o.push({psychologistId:l,success:!0,deletedTransactions:e.deletedTransactions,psychologist:e.psychologist,psychologist_name:e.psychologist_name})):(n++,o.push({psychologistId:l,success:!1,error:e.message}))}catch(t){n++,o.push({psychologistId:l,success:!1,error:t.message})}yield new Promise(e=>setTimeout(e,500))}return{success:i>0,message:`Procesados ${e.length} psicólogos: ${i} exitosos, ${n} errores`,successCount:i,errorCount:n,totalDeletedTransactions:r,totalDeletedUsageControl:a,results:o}}catch(t){return d.error(t.message),{success:!1,message:t.message}}})}_refreshPsychologistMetrics(e){return l(this,null,function*(){try{const{data:s,error:t}=yield c.rpc("get_psychologist_pin_balance",{p_psicologo_id:e});if(t)return;return s}catch(s){}})}};const u=new class{getPsychologistsWithPinStats(){return l(this,null,function*(){return yield p.getPsychologistsWithPinStats()})}assignPins(e,s,t="Asignación manual"){return l(this,null,function*(){return yield p.assignPins(e,s,t)})}getPsychologistBalance(e){return l(this,null,function*(){return yield p.getPsychologistBalance(e)})}getTransactionHistory(e=null,s=50){return l(this,null,function*(){return yield p.getTransactionHistory(e,s)})}deleteTransaction(e){return l(this,null,function*(){return yield p.deleteTransaction(e)})}deleteMultipleTransactions(e){return l(this,null,function*(){return yield p.deleteMultipleTransactions(e)})}getSystemStats(){return l(this,null,function*(){return yield p.getSystemStats()})}consumePin(e){return l(this,arguments,function*(e,s="Consumo automático",t={}){return yield p.consumePin(e,s,t)})}removePinsFromPsychologist(e,s,t="Eliminación manual de pines"){return l(this,null,function*(){return yield p.removePinsFromPsychologist(e,s,t)})}removePsychologistPinAssignment(e,s="Eliminación completa de asignación"){return l(this,null,function*(){return yield p.removePsychologistPinAssignment(e,s)})}removeMultiplePsychologistAssignments(e,s="Eliminación masiva de asignaciones"){return l(this,null,function*(){return yield p.removeMultiplePsychologistAssignments(e,s)})}};export{u as p,p as s};
