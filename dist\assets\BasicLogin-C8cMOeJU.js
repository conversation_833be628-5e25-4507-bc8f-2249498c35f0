import{j as e,K as a,L as i,F as s}from"./auth-BzDSP4i9.js";import{r,d as o}from"./react-vendor-C9XH6RF0.js";import"./ui-vendor-COFtXQcG.js";const l=()=>{const[l,t]=r.useState("<EMAIL>"),[n,u]=r.useState("123456"),[m,c]=r.useState("administrador"),[d,g]=r.useState(!1),b=o(),N=e=>{c(e),g(!0),setTimeout(()=>{switch(localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",e),localStorage.setItem("userEmail","<EMAIL>"),g(!1),e){case"administrador":b("/admin/administration");break;case"psicologo":b("/admin/candidates");break;default:b("/home")}},500)};return e.jsxDEV("div",{className:"min-h-screen flex",children:[e.jsxDEV("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden",children:[e.jsxDEV("div",{className:"absolute inset-0 opacity-10",children:[e.jsxDEV("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:77,columnNumber:11},void 0),e.jsxDEV("div",{className:"absolute bottom-20 right-20 w-24 h-24 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:78,columnNumber:11},void 0),e.jsxDEV("div",{className:"absolute top-1/2 left-10 w-16 h-16 border border-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:79,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:76,columnNumber:9},void 0),e.jsxDEV("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:[e.jsxDEV("div",{className:"mb-12",children:e.jsxDEV("div",{className:"flex items-center mb-4",children:[e.jsxDEV("div",{className:"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsxDEV("span",{className:"text-white font-bold text-xl",children:"B"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:88,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:87,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:"text-3xl font-bold",children:"BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:91,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-blue-200",children:"Sistema de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:92,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:90,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:86,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:85,columnNumber:11},void 0),e.jsxDEV("div",{className:"mb-8",children:[e.jsxDEV("h2",{className:"text-4xl font-bold mb-4",children:["Bienvenido al",e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:100,columnNumber:28},void 0),e.jsxDEV("span",{className:"text-orange-400",children:"Futuro de la Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:101,columnNumber:15},void 0),e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:101,columnNumber:79},void 0),e.jsxDEV("span",{className:"text-blue-200",children:"Psicométrica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:102,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:99,columnNumber:13},void 0),e.jsxDEV("p",{className:"text-blue-200 text-lg leading-relaxed",children:"Plataforma de nueva generación para evaluaciones psicológicas inteligentes y análisis avanzado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:104,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:98,columnNumber:11},void 0),e.jsxDEV("div",{className:"space-y-4",children:[e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:113,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:112,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"font-semibold",children:"Evaluaciones Inteligentes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:116,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Adaptativas y avanzadas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:117,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:115,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:111,columnNumber:13},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:123,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:122,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"font-semibold",children:"Dashboard Avanzado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:126,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Análisis en tiempo real"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:127,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:125,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:121,columnNumber:13},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-4",children:e.jsxDEV("span",{className:"text-white text-sm",children:"✓"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:133,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:132,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:"font-semibold",children:"Seguridad Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:136,columnNumber:17},void 0),e.jsxDEV("p",{className:"text-blue-200 text-sm",children:"Protección de datos garantizada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:137,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:135,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:131,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:110,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:83,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:74,columnNumber:7},void 0),e.jsxDEV("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50",children:e.jsxDEV("div",{className:"w-full max-w-md",children:[e.jsxDEV("div",{className:"lg:hidden text-center mb-8",children:[e.jsxDEV("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:149,columnNumber:13},void 0),e.jsxDEV("p",{className:"text-gray-600",children:"Sistema de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:150,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:148,columnNumber:11},void 0),e.jsxDEV("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxDEV("form",{onSubmit:e=>{return a=null,i=null,s=function*(){e.preventDefault(),g(!0),setTimeout(()=>{switch(localStorage.setItem("isLoggedIn","true"),localStorage.setItem("userRole",m),localStorage.setItem("userEmail",l),g(!1),m){case"administrador":b("/admin/administration");break;case"psicologo":b("/admin/candidates");break;default:b("/home")}},1e3)},new Promise((r,o)=>{var l=a=>{try{n(s.next(a))}catch(e){o(e)}},t=a=>{try{n(s.throw(a))}catch(e){o(e)}},n=e=>e.done?r(e.value):Promise.resolve(e.value).then(l,t);n((s=s.apply(a,i)).next())});var a,i,s},className:"space-y-6",children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"TIPO DE USUARIO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:158,columnNumber:17},void 0),e.jsxDEV("div",{className:"space-y-3",children:[e.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-orange-50 transition-colors",children:[e.jsxDEV("input",{type:"radio",name:"userType",value:"candidato",checked:"candidato"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:163,columnNumber:21},void 0),e.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("candidato"===m?"border-orange-500 bg-orange-500":"border-gray-300"),children:"candidato"===m&&e.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:174,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:171,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mr-3",children:e.jsxDEV(a,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:178,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:177,columnNumber:23},void 0),e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"font-medium text-gray-800",children:"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:181,columnNumber:25},void 0),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso para realizar evaluaciones psicométricas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:182,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:180,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:176,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:162,columnNumber:19},void 0),e.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[e.jsxDEV("input",{type:"radio",name:"userType",value:"psicologo",checked:"psicologo"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:188,columnNumber:21},void 0),e.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("psicologo"===m?"border-gray-500 bg-gray-500":"border-gray-300"),children:"psicologo"===m&&e.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:199,columnNumber:56},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:196,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:e.jsxDEV(a,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:203,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:202,columnNumber:23},void 0),e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"font-medium text-gray-800",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:206,columnNumber:25},void 0),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso para gestionar candidatos y resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:207,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:205,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:201,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:187,columnNumber:19},void 0),e.jsxDEV("label",{className:"flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[e.jsxDEV("input",{type:"radio",name:"userType",value:"administrador",checked:"administrador"===m,onChange:e=>c(e.target.value),className:"sr-only"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:213,columnNumber:21},void 0),e.jsxDEV("div",{className:"w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center "+("administrador"===m?"border-gray-500 bg-gray-500":"border-gray-300"),children:"administrador"===m&&e.jsxDEV("div",{className:"w-2 h-2 bg-white rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:224,columnNumber:60},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:221,columnNumber:21},void 0),e.jsxDEV("div",{className:"flex items-center",children:[e.jsxDEV("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-3",children:e.jsxDEV(a,{className:"text-white text-sm"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:228,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:227,columnNumber:23},void 0),e.jsxDEV("div",{children:[e.jsxDEV("div",{className:"font-medium text-gray-800",children:"Administrador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:231,columnNumber:25},void 0),e.jsxDEV("div",{className:"text-sm text-gray-500",children:"Acceso completo al sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:232,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:230,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:226,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:212,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:161,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:157,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EMAIL O DOCUMENTO"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:241,columnNumber:17},void 0),e.jsxDEV("div",{className:"relative",children:[e.jsxDEV(a,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:245,columnNumber:19},void 0),e.jsxDEV("input",{type:"text",value:l,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Número de documento",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:246,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:244,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:240,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CONTRASEÑA"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:259,columnNumber:17},void 0),e.jsxDEV("div",{className:"relative",children:[e.jsxDEV(i,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:263,columnNumber:19},void 0),e.jsxDEV("input",{type:"password",value:n,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Ingresa tu contraseña",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:264,columnNumber:19},void 0),e.jsxDEV("button",{type:"button",className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:e.jsxDEV("i",{className:"fas fa-eye"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:276,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:272,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:262,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:258,columnNumber:15},void 0),e.jsxDEV("div",{className:"flex items-center justify-between",children:[e.jsxDEV("label",{className:"flex items-center",children:[e.jsxDEV("input",{type:"checkbox",className:"rounded border-gray-300 text-orange-500 focus:ring-orange-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:284,columnNumber:19},void 0),e.jsxDEV("span",{className:"ml-2 text-sm text-gray-600",children:"Recordarme"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:285,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:283,columnNumber:17},void 0),e.jsxDEV("a",{href:"#",className:"text-sm text-orange-500 hover:text-orange-600",children:"¿Olvidaste tu contraseña?"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:287,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:282,columnNumber:15},void 0),e.jsxDEV("button",{type:"submit",disabled:d,className:"w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center",children:d?e.jsxDEV(e.Fragment,{children:[e.jsxDEV(s,{className:"animate-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:300,columnNumber:21},void 0),"Iniciando sesión..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:299,columnNumber:19},void 0):e.jsxDEV(e.Fragment,{children:[e.jsxDEV(a,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:305,columnNumber:21},void 0),"Iniciar Sesión"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:304,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:293,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:155,columnNumber:13},void 0),e.jsxDEV("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[e.jsxDEV("p",{className:"text-xs text-gray-500 text-center mb-3",children:"Acceso rápido para testing:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:314,columnNumber:15},void 0),e.jsxDEV("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxDEV("button",{onClick:()=>N("administrador"),disabled:d,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors",children:"Admin"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:316,columnNumber:17},void 0),e.jsxDEV("button",{onClick:()=>N("psicologo"),disabled:d,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:323,columnNumber:17},void 0),e.jsxDEV("button",{onClick:()=>N("candidato"),disabled:d,className:"px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors",children:"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:330,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:315,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:313,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:154,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:146,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:145,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/auth/BasicLogin.jsx",lineNumber:72,columnNumber:5},void 0)};export{l as default};
