var e=Object.defineProperty,i=Object.defineProperties,n=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,m=(i,n,s)=>n in i?e(i,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[n]=s,r=(e,i)=>{for(var n in i||(i={}))a.call(i,n)&&m(e,n,i[n]);if(s)for(var n of s(i))t.call(i,n)&&m(e,n,i[n]);return e},o=(e,s)=>i(e,n(s)),l=(e,i,n)=>new Promise((s,a)=>{var t=e=>{try{r(n.next(e))}catch(i){a(i)}},m=e=>{try{r(n.throw(e))}catch(i){a(i)}},r=e=>e.done?s(e.value):Promise.resolve(e.value).then(t,m);r((n=n.apply(e,i)).next())});import{j as c,F as d,D as u,E as p,G as x,i as N,H as b,w as g,I as f}from"./auth-Cw5QfmsP.js";import{r as v}from"./react-vendor-C9XH6RF0.js";import{Q as h}from"./ui-vendor-COFtXQcG.js";import{p as P}from"./PinManagementService-DCClnbJn.js";const j=()=>{const[e,i]=v.useState([]),[n,s]=v.useState(!0),[a,t]=v.useState(null),[m,j]=v.useState({}),[V,y]=v.useState(null),[C,A]=v.useState(null),[E,D]=v.useState(!1),[B,w]=v.useState([]),_=v.useCallback(()=>l(null,null,function*(){try{s(!0),t(null);const[e,n]=yield Promise.all([P.getUsageStats(),P.getSystemSummary()]);i(e.statistics),A(n)}catch(e){t("No se pudieron cargar los datos. Intente de nuevo más tarde.")}finally{s(!1)}}),[]),S=v.useCallback(()=>l(null,null,function*(){try{const e=yield P.getTransactionHistory(null,20);w(e)}catch(e){h.error("Error al cargar el historial de transacciones.")}}),[]);v.useEffect(()=>{_()},[_]),v.useEffect(()=>{E&&S()},[E,S]);const k=(e,i)=>{const n={sin_pines:"bg-red-100 text-red-800",pocos_pines:"bg-yellow-100 text-yellow-800",activo:"bg-green-100 text-green-800"};return c.jsxDEV("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${n[e]||n.activo}`,children:[{sin_pines:"Sin pines",pocos_pines:"Pocos pines",activo:"Activo"}[e]||"Activo"," (",i,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:131,columnNumber:7},void 0)},O=e=>e?new Date(e).toLocaleDateString("es-ES",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Nunca";return n?c.jsxDEV("div",{className:"flex items-center justify-center p-10",children:[c.jsxDEV(d,{className:"animate-spin text-blue-500 text-4xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:154,columnNumber:9},void 0),c.jsxDEV("span",{className:"ml-4 text-xl",children:"Cargando datos del sistema..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:155,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:153,columnNumber:7},void 0):a?c.jsxDEV("div",{className:"flex flex-col items-center justify-center p-10 bg-red-50 border border-red-200 rounded-lg",children:[c.jsxDEV(u,{className:"text-red-500 text-4xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:163,columnNumber:9},void 0),c.jsxDEV("span",{className:"mt-4 text-xl text-red-700",children:a},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:164,columnNumber:9},void 0),c.jsxDEV("button",{onClick:_,className:"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors flex items-center",children:[c.jsxDEV(p,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:169,columnNumber:11},void 0),"Reintentar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:165,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:162,columnNumber:7},void 0):c.jsxDEV("div",{className:"p-6 bg-gray-50 min-h-screen",children:[c.jsxDEV("div",{className:"flex justify-between items-center mb-6",children:[c.jsxDEV("h1",{className:"text-3xl font-bold text-gray-800",children:"Gestión de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:180,columnNumber:9},void 0),c.jsxDEV("div",{className:"flex space-x-3",children:[c.jsxDEV("button",{onClick:()=>D(!E),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[c.jsxDEV(x,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:186,columnNumber:13},void 0),E?"Ocultar":"Ver"," Historial"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:182,columnNumber:11},void 0),c.jsxDEV("button",{onClick:_,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[c.jsxDEV(p,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:193,columnNumber:13},void 0),"Actualizar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:189,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:181,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:179,columnNumber:7},void 0),C&&c.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[c.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow",children:c.jsxDEV("div",{className:"flex items-center",children:[c.jsxDEV(N,{className:"text-blue-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:204,columnNumber:15},void 0),c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm text-gray-600",children:"Psicólogos Totales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:206,columnNumber:17},void 0),c.jsxDEV("p",{className:"text-2xl font-bold",children:C.total_psychologists},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:207,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:205,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:203,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:202,columnNumber:11},void 0),c.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow",children:c.jsxDEV("div",{className:"flex items-center",children:[c.jsxDEV(b,{className:"text-yellow-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:214,columnNumber:15},void 0),c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm text-gray-600",children:"Pines Disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:216,columnNumber:17},void 0),c.jsxDEV("p",{className:"text-2xl font-bold text-green-600",children:C.total_pins_available},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:217,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:215,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:213,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:212,columnNumber:11},void 0),c.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow",children:c.jsxDEV("div",{className:"flex items-center",children:[c.jsxDEV(g,{className:"text-red-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:224,columnNumber:15},void 0),c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm text-gray-600",children:"Pines Consumidos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:226,columnNumber:17},void 0),c.jsxDEV("p",{className:"text-2xl font-bold text-red-600",children:C.total_pins_consumed},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:227,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:225,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:223,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:222,columnNumber:11},void 0),c.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow",children:c.jsxDEV("div",{className:"flex items-center",children:[c.jsxDEV(N,{className:"text-purple-500 text-2xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:234,columnNumber:15},void 0),c.jsxDEV("div",{children:[c.jsxDEV("p",{className:"text-sm text-gray-600",children:"Con Pines / Sin Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:236,columnNumber:17},void 0),c.jsxDEV("p",{className:"text-2xl font-bold",children:[c.jsxDEV("span",{className:"text-green-600",children:C.psychologists_with_pins},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:238,columnNumber:19},void 0),c.jsxDEV("span",{className:"text-gray-400 mx-1",children:"/"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:239,columnNumber:19},void 0),c.jsxDEV("span",{className:"text-red-600",children:C.psychologists_without_pins},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:240,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:237,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:235,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:233,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:232,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:201,columnNumber:9},void 0),E&&c.jsxDEV("div",{className:"bg-white rounded-lg shadow mb-6",children:[c.jsxDEV("div",{className:"p-4 border-b",children:c.jsxDEV("h2",{className:"text-xl font-semibold",children:"Historial de Transacciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:252,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:251,columnNumber:11},void 0),c.jsxDEV("div",{className:"p-4",children:0===B.length?c.jsxDEV("p",{className:"text-gray-500 text-center py-4",children:"No hay transacciones registradas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:256,columnNumber:15},void 0):c.jsxDEV("div",{className:"overflow-x-auto",children:c.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsxDEV("thead",{className:"bg-gray-50",children:c.jsxDEV("tr",{children:[c.jsxDEV("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Fecha"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:262,columnNumber:23},void 0),c.jsxDEV("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:263,columnNumber:23},void 0),c.jsxDEV("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Tipo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:264,columnNumber:23},void 0),c.jsxDEV("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Cantidad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:265,columnNumber:23},void 0),c.jsxDEV("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Motivo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:266,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:261,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:260,columnNumber:19},void 0),c.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:B.map(e=>{var i,n;return c.jsxDEV("tr",{className:"hover:bg-gray-50",children:[c.jsxDEV("td",{className:"px-4 py-2 text-sm text-gray-900",children:O(e.created_at)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:272,columnNumber:25},void 0),c.jsxDEV("td",{className:"px-4 py-2 text-sm text-gray-900",children:[null==(i=e.psicologos)?void 0:i.nombre," ",null==(n=e.psicologos)?void 0:n.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:275,columnNumber:25},void 0),c.jsxDEV("td",{className:"px-4 py-2 text-sm",children:c.jsxDEV("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+("asignacion"===e.tipo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"asignacion"===e.tipo?"Asignación":"Consumo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:279,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:278,columnNumber:25},void 0),c.jsxDEV("td",{className:"px-4 py-2 text-sm font-medium",children:c.jsxDEV("span",{className:"asignacion"===e.tipo?"text-green-600":"text-red-600",children:["asignacion"===e.tipo?"+":"",e.cantidad]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:288,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:287,columnNumber:25},void 0),c.jsxDEV("td",{className:"px-4 py-2 text-sm text-gray-600",children:e.motivo||"Sin motivo especificado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:292,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:271,columnNumber:23},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:269,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:259,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:258,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:254,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:250,columnNumber:9},void 0),c.jsxDEV("div",{className:"bg-white shadow-md rounded-lg overflow-hidden",children:[c.jsxDEV("div",{className:"p-4 border-b",children:c.jsxDEV("h2",{className:"text-xl font-semibold",children:"Asignación de Pines por Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:308,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:307,columnNumber:9},void 0),c.jsxDEV("div",{className:"overflow-x-auto",children:c.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsxDEV("thead",{className:"bg-gray-100",children:c.jsxDEV("tr",{children:[c.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:314,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:317,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Asignados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:320,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Consumidos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:323,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:326,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:329,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:332,columnNumber:17},void 0),c.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Asignar Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:335,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:313,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:312,columnNumber:13},void 0),c.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>c.jsxDEV("tr",{className:"hover:bg-gray-50",children:[c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:[c.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:e.nombre_psicologo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:344,columnNumber:21},void 0),c.jsxDEV("div",{className:"text-sm text-gray-500",children:e.email_psicologo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:347,columnNumber:21},void 0),e.ultima_transaccion&&c.jsxDEV("div",{className:"text-xs text-gray-400",children:["Última: ",O(e.ultima_transaccion)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:351,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:343,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:k(e.status,e.pines_restantes)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:356,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-blue-600 font-semibold",children:e.total_asignado},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:359,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-red-600 font-semibold",children:e.total_consumido},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:362,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-green-600 font-bold text-lg",children:e.pines_restantes},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:365,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700",children:e.pacientes_asignados},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:368,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700",children:e.tests_completados},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:371,columnNumber:19},void 0),c.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsxDEV("div",{className:"flex items-center space-x-2",children:[c.jsxDEV("input",{type:"number",min:"1",max:"1000",placeholder:"Cantidad",value:m[e.psicologo_id]||"",onChange:i=>((e,i)=>{const n=parseInt(i,10);j(i=>o(r({},i),{[e]:isNaN(n)?"":Math.max(0,n)}))})(e.psicologo_id,i.target.value),className:"w-24 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm",disabled:V===e.psicologo_id},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:376,columnNumber:23},void 0),c.jsxDEV("button",{onClick:()=>{return i=e.psicologo_id,l(null,null,function*(){const e=m[i];if(!e||e<=0)h.warn("Por favor, ingrese una cantidad válida de pines.");else{y(i);try{yield P.assignPins(i,e,`Asignación manual de ${e} pines`),j(e=>o(r({},e),{[i]:""})),yield _()}catch(n){}finally{y(null)}}});var i},className:"px-3 py-1.5 text-white rounded-md disabled:bg-gray-400 flex items-center justify-center transition-colors text-sm "+(e.pines_restantes>0?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"),disabled:V===e.psicologo_id||!m[e.psicologo_id]||m[e.psicologo_id]<=0,title:e.pines_restantes>0?"Agregar pines adicionales":"Asignar pines iniciales",children:[V===e.psicologo_id?c.jsxDEV(d,{className:"animate-spin"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:401,columnNumber:27},void 0):c.jsxDEV(f,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:403,columnNumber:27},void 0),c.jsxDEV("span",{className:"ml-1",children:e.pines_restantes>0?"Agregar":"Asignar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:405,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:386,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:375,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:374,columnNumber:19},void 0)]},e.psicologo_id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:342,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:340,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:311,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:310,columnNumber:9},void 0),0===e.length&&c.jsxDEV("div",{className:"text-center py-8",children:[c.jsxDEV(N,{className:"mx-auto text-gray-400 text-4xl mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:419,columnNumber:13},void 0),c.jsxDEV("p",{className:"text-gray-500",children:"No hay psicólogos registrados en el sistema."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:420,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:418,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:306,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentPanel.jsx",lineNumber:177,columnNumber:5},void 0)};export{j as default};
