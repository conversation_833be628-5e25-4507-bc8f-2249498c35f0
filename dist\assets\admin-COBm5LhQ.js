var e=Object.defineProperty,i=Object.defineProperties,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,t=(i,a,s)=>a in i?e(i,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[a]=s,o=(e,i)=>{for(var a in i||(i={}))n.call(i,a)&&t(e,a,i[a]);if(s)for(var a of s(i))r.call(i,a)&&t(e,a,i[a]);return e},l=(e,s)=>i(e,a(s)),m=(e,i)=>{var a={};for(var t in e)n.call(e,t)&&i.indexOf(t)<0&&(a[t]=e[t]);if(null!=e&&s)for(var t of s(e))i.indexOf(t)<0&&r.call(e,t)&&(a[t]=e[t]);return a},u=(e,i,a)=>t(e,"symbol"!=typeof i?i+"":i,a),c=(e,i,a)=>new Promise((s,n)=>{var r=e=>{try{o(a.next(e))}catch(i){n(i)}},t=e=>{try{o(a.throw(e))}catch(i){n(i)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,t);o((a=a.apply(e,i)).next())});import{j as d,s as b,F as p,a as N,b as g,c as f,d as x,e as v,f as h,g as j,h as V,i as C,k as y}from"./auth-Cw5QfmsP.js";import{L as E,r as D,u as B}from"./react-vendor-C9XH6RF0.js";import{Q as T}from"./ui-vendor-COFtXQcG.js";const w=e=>{var i=e,{children:a,className:s=""}=i,n=m(i,["children","className"]);return d.jsxDEV("div",l(o({className:`bg-white rounded-lg shadow-sm border border-gray-200 ${s}`},n),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:5,columnNumber:5},void 0)},P=e=>{var i=e,{children:a,className:s=""}=i,n=m(i,["children","className"]);return d.jsxDEV("div",l(o({className:`px-6 py-4 border-b border-gray-200 ${s}`},n),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:16,columnNumber:5},void 0)},S=e=>{var i=e,{children:a,className:s=""}=i,n=m(i,["children","className"]);return d.jsxDEV("div",l(o({className:`px-6 py-4 ${s}`},n),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:27,columnNumber:5},void 0)},_=e=>{var i=e,{children:a,className:s=""}=i,n=m(i,["children","className"]);return d.jsxDEV("div",l(o({className:`px-6 py-4 border-t border-gray-200 ${s}`},n),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Card.jsx",lineNumber:38,columnNumber:5},void 0)},I=e=>{var i=e,{children:a,variant:s="primary",size:n="md",className:r="",disabled:t=!1,as:u="button",to:c}=i,b=m(i,["children","variant","size","className","disabled","as","to"]);const p=`inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-400 shadow-sm",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-gray-400 shadow-sm",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm"}[s]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[n]} ${t?"opacity-50 cursor-not-allowed":"cursor-pointer"} ${r}`;if(u===E||"Link"===u&&c)return d.jsxDEV(E,l(o({to:c,className:p},b),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:37,columnNumber:7},void 0);if("function"==typeof u){const e=u;return d.jsxDEV(e,l(o({className:p},b),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:51,columnNumber:7},void 0)}return d.jsxDEV("button",l(o({className:p,disabled:t},b),{children:a}),void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/Button.jsx",lineNumber:62,columnNumber:5},void 0)};class M{static _validarParametrosConversion(e,i,a){const s=[];return("number"!=typeof e||e<0||e>100)&&s.push("Puntaje directo debe ser un número entre 0 y 100"),i&&"string"==typeof i&&0!==i.length||s.push("Código de aptitud es requerido"),("number"!=typeof a||a<6||a>18)&&s.push("Edad debe ser un número entre 6 y 18 años"),s}static _limpiarCache(){this._cache={baremos:null,funcionesDisponibles:null,lastCheck:null}}static _esCacheValido(){return this._cache.lastCheck&&Date.now()-this._cache.lastCheck<this.CACHE_DURATION}static recalcularTodosLosPercentiles(){return c(this,null,function*(){try{const{data:e,error:i}=yield b.rpc("recalcular_todos_los_percentiles");if(i)return T.error("Error al recalcular percentiles en Supabase"),{success:!1,error:i};const a=e||0;return a>0?T.success(`Se actualizaron ${a} resultados con sus percentiles`):T.info("No hay resultados pendientes de conversión"),{success:!0,count:a}}catch(e){return T.error("Error al ejecutar el recálculo de percentiles"),{success:!1,error:e}}})}static probarConversion(e,i,a){return c(this,null,function*(){try{const s=this._validarParametrosConversion(e,i,a);if(s.length>0){const e=`Parámetros inválidos: ${s.join(", ")}`;return T.error(e),{success:!1,error:{message:e},validationErrors:s}}const{data:n,error:r}=yield b.rpc("convertir_pd_a_pc",{p_puntaje_directo:e,p_aptitud_codigo:i.toUpperCase(),p_edad:Math.round(a)});if(r)return T.error(`Error en conversión: ${r.message||"Error desconocido"}`),{success:!1,error:r};if(null==n){const s=`No se pudo calcular percentil para PD=${e}, aptitud=${i}, edad=${a}`;return T.warning(s),{success:!1,error:{message:s}}}if("number"!=typeof n||n<1||n>99){const e=`Percentil fuera de rango válido (1-99): ${n}`;return T.warning(e),{success:!1,error:{message:e}}}return{success:!0,percentil:n}}catch(s){return T.error(`Error inesperado en conversión: ${s.message}`),{success:!1,error:s}}})}static convertirResultadosEnLote(e,i=!0){return c(this,null,function*(){try{if(!Array.isArray(e)||0===e.length)return T.warning("No se proporcionaron resultados para convertir"),{success:!1,error:"Lista de resultados vacía"};const i={exitosos:[],fallidos:[],total:e.length},a=10,s=[];for(let r=0;r<e.length;r+=a)s.push(e.slice(r,r+a));for(let e=0;e<s.length;e++){const a=s[e].map(e=>c(this,null,function*(){try{const a=yield this.forzarConversionResultado(e);a.success?i.exitosos.push({id:e,resultado:a.resultado}):i.fallidos.push({id:e,error:a.error})}catch(a){i.fallidos.push({id:e,error:a})}}));yield Promise.all(a),e<s.length-1&&(yield new Promise(e=>setTimeout(e,100)))}const n=(i.exitosos.length/i.total*100).toFixed(1);return i.exitosos.length>0&&T.success(`Conversión completada: ${i.exitosos.length}/${i.total} resultados convertidos`),i.fallidos.length>0&&T.warning(`${i.fallidos.length} conversiones fallaron`),{success:i.exitosos.length>0,resultados:i,porcentajeExito:parseFloat(n)}}catch(i){return T.error("Error en conversión en lote"),{success:!1,error:i}}})}static configurarConversionAutomatica(){return c(this,null,function*(){try{return(yield this.recalcularTodosLosPercentiles()).success?(T.success("Conversión automática configurada correctamente"),!0):(T.error("Error al configurar la conversión automática"),!1)}catch(e){return T.error("Error al configurar la conversión automática"),!1}})}static obtenerEstadisticasConversion(){return c(this,null,function*(){try{const{data:e,error:i}=yield b.from("resultados").select("id",{count:"exact"}).not("percentil","is",null),{data:a,error:s}=yield b.from("resultados").select("id",{count:"exact"}).is("percentil",null);if(i||s)return null;return{totalResultados:((null==e?void 0:e.length)||0)+((null==a?void 0:a.length)||0),conPercentil:(null==e?void 0:e.length)||0,sinPercentil:(null==a?void 0:a.length)||0,porcentajeConvertido:(((null==e?void 0:e.length)||0)/(((null==e?void 0:e.length)||0)+((null==a?void 0:a.length)||0))*100).toFixed(1)}}catch(e){return null}})}static forzarConversionResultado(e){return c(this,null,function*(){try{const{data:i,error:a}=yield b.from("resultados").select("\n          id,\n          puntaje_directo,\n          aptitudes:aptitud_id (codigo),\n          pacientes:paciente_id (fecha_nacimiento)\n        ").eq("id",e).single();if(a||!i)return{success:!1,error:a};const s=new Date(i.pacientes.fecha_nacimiento),n=new Date;let r=n.getFullYear()-s.getFullYear();const t=n.getMonth()-s.getMonth();(t<0||0===t&&n.getDate()<s.getDate())&&r--;const o=yield this.probarConversion(i.puntaje_directo,i.aptitudes.codigo,r);if(!o.success)return{success:!1,error:"Error en conversión"};const{data:l,error:m}=yield b.from("resultados").update({percentil:o.percentil,updated_at:(new Date).toISOString()}).eq("id",e).select().single();return m?{success:!1,error:m}:(T.success(`Conversión completada: PC ${o.percentil}`),{success:!0,resultado:l})}catch(i){return{success:!1,error:i}}})}static verificarBaremos(){return c(this,null,function*(){try{const{data:e,error:i}=yield b.from("baremos").select("factor, baremo_grupo, count(*)").group("factor, baremo_grupo");return i?{success:!1,error:i}:{success:!0,baremos:e}}catch(e){return{success:!1,error:e}}})}}u(M,"_cache",{baremos:null,funcionesDisponibles:null,lastCheck:null}),u(M,"CACHE_DURATION",3e5);const k=({title:e,subtitle:i,icon:a,className:s="",showTransitions:n=!0})=>d.jsxDEV("div",{className:`bg-gradient-to-r from-blue-900 to-blue-800 text-white ${s}`,children:d.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:d.jsxDEV("div",{className:"text-center",children:[d.jsxDEV("div",{className:"flex items-center justify-center mb-3",children:[a&&d.jsxDEV("div",{className:"w-12 h-12 bg-[#f59e0b] rounded-full flex items-center justify-center mr-4 shadow-lg",children:d.jsxDEV(a,{className:"text-white text-xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:22,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:21,columnNumber:15},void 0),d.jsxDEV("h1",{className:"text-3xl font-bold",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:25,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:19,columnNumber:11},void 0),i&&d.jsxDEV("p",{className:"text-blue-100 text-lg",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:30,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:18,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:17,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/ui/PageHeader.jsx",lineNumber:15,columnNumber:5},void 0),A=(e,i,a,s)=>c(null,null,function*(){try{yield b.from("logs").insert({action:e,table_name:i,record_id:a,data:s,created_at:(new Date).toISOString()})}catch(n){}}),F={checkConnection(){return c(this,null,function*(){try{const{data:e,error:i}=yield b.rpc("get_tables");if(i)throw i;return{success:!0,data:e}}catch(e){return{success:!1,error:e}}})},getInstitutions(){return c(this,null,function*(){return yield b.from("instituciones").select("*").order("nombre",{ascending:!0})})},createInstitution(e){return c(this,null,function*(){const i={nombre:e.nombre,direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},a=yield b.from("instituciones").insert([i]).select();return a.data&&a.data.length>0&&(yield A("create","instituciones",a.data[0].id,i)),a})},updateInstitution(e,i){return c(this,null,function*(){const a={nombre:i.nombre,direccion:i.direccion||"",telefono:i.telefono||"",email:i.email||"",sitio_web:i.sitio_web||"",updated_at:(new Date).toISOString()},s=yield b.from("instituciones").update(a).eq("id",e).select();return s.data&&s.data.length>0&&(yield A("update","instituciones",e,a)),s})},deleteInstitution(e){return c(this,null,function*(){const{data:i}=yield b.from("instituciones").select("*").eq("id",e).single(),a=yield b.from("instituciones").delete().eq("id",e);return!a.error&&i&&(yield A("delete","instituciones",e,i)),a})},getPsychologists(){return c(this,null,function*(){return yield b.from("psicologos").select("*, instituciones(id, nombre)").order("nombre",{ascending:!0})})},createPsychologist(e){return c(this,null,function*(){const i={usuario_id:e.usuario_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||"",email:e.email||"",telefono:e.telefono||"",genero:e.genero||"",especialidad:e.especialidad||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},a=yield b.from("psicologos").insert([i]).select();return a.data&&a.data.length>0&&(yield A("create","psicologos",a.data[0].id,i)),a})},updatePsychologist(e,i){return c(this,null,function*(){const a={institucion_id:i.institucion_id,nombre:i.nombre,apellido:i.apellido||"",email:i.email||"",telefono:i.telefono||"",genero:i.genero||"",especialidad:i.especialidad||"",updated_at:(new Date).toISOString()},s=yield b.from("psicologos").update(a).eq("id",e).select();return s.data&&s.data.length>0&&(yield A("update","psicologos",e,a)),s})},deletePsychologist(e){return c(this,null,function*(){const{data:i}=yield b.from("psicologos").select("*").eq("id",e).single(),a=yield b.from("psicologos").delete().eq("id",e);return!a.error&&i&&(yield A("delete","psicologos",e,i)),a})},getPatients(){return c(this,null,function*(){return yield b.from("pacientes").select("*, instituciones(id, nombre), psicologos(id, nombre, apellido)").order("nombre",{ascending:!0})})},createPatient(e){return c(this,null,function*(){const i={psicologo_id:e.psicologo_id||null,institucion_id:e.institucion_id,nombre:e.nombre,apellido:e.apellido||e.apellidos||"",documento:e.documento||e.documento_identidad||"",email:e.email||"",genero:e.genero||"",fecha_nacimiento:e.fecha_nacimiento||null,nivel_educativo:e.nivel_educativo||"",ocupacion:e.ocupacion||"",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},a=yield b.from("pacientes").insert([i]).select();return a.data&&a.data.length>0&&(yield A("create","pacientes",a.data[0].id,i)),a})},updatePatient(e,i){return c(this,null,function*(){const a={psicologo_id:i.psicologo_id||null,institucion_id:i.institucion_id,nombre:i.nombre,apellido:i.apellido||i.apellidos||"",documento:i.documento||i.documento_identidad||"",email:i.email||"",genero:i.genero||"",fecha_nacimiento:i.fecha_nacimiento||null,nivel_educativo:i.nivel_educativo||"",ocupacion:i.ocupacion||"",updated_at:(new Date).toISOString()},s=yield b.from("pacientes").update(a).eq("id",e).select();return s.data&&s.data.length>0&&(yield A("update","pacientes",e,a)),s})},deletePatient(e){return c(this,null,function*(){const{data:i}=yield b.from("pacientes").select("*").eq("id",e).single(),a=yield b.from("pacientes").delete().eq("id",e);return!a.error&&i&&(yield A("delete","pacientes",e,i)),a})},getPatientsByPsychologist(e){return c(this,null,function*(){return yield b.from("pacientes").select("*").eq("psicologo_id",e).order("nombre",{ascending:!0})})},getPatientsByInstitution(e){return c(this,null,function*(){return yield b.from("pacientes").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})},getPsychologistsByInstitution(e){return c(this,null,function*(){return yield b.from("psicologos").select("*").eq("institucion_id",e).order("nombre",{ascending:!0})})}},$=Object.freeze(Object.defineProperty({__proto__:null,default:()=>d.jsxDEV("div",{className:"container mx-auto py-6",children:[d.jsxDEV("h1",{className:"text-2xl font-bold text-gray-800 mb-6",children:"Administración de Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:7,columnNumber:7},void 0),d.jsxDEV(w,{children:[d.jsxDEV(P,{children:d.jsxDEV("h2",{className:"text-lg font-medium",children:"Lista de Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:11,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:10,columnNumber:9},void 0),d.jsxDEV(S,{children:d.jsxDEV("p",{className:"text-gray-600",children:"Esta sección permitirá gestionar las instituciones registradas en el sistema (componente en desarrollo)."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:14,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:13,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:9,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Institutions.jsx",lineNumber:6,columnNumber:5},void 0)},Symbol.toStringTag,{value:"Module"})),O=({initialData:e,onSubmit:i,onCancel:a})=>{const[s,n]=D.useState({nombre:"",direccion:"",telefono:"",email:"",sitio_web:""}),[r,t]=D.useState(!1);D.useEffect(()=>{e&&n({nombre:e.nombre||"",direccion:e.direccion||"",telefono:e.telefono||"",email:e.email||"",sitio_web:e.sitio_web||""})},[e]);const m=e=>{const{name:i,value:a,type:r,checked:t}=e.target;n(l(o({},s),{[i]:"checkbox"===r?t:a}))};return d.jsxDEV("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),s.nombre.trim()||(alert("El nombre de la institución es obligatorio"),0)){t(!0);try{yield i(s)}catch(a){}finally{t(!1)}}}),className:"space-y-4",children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{className:"md:col-span-2",children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre de la Institución *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:71,columnNumber:11},void 0),d.jsxDEV("input",{type:"text",name:"nombre",value:s.nombre,onChange:m,className:"w-full p-2 border border-gray-300 rounded-md",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:74,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:70,columnNumber:9},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:84,columnNumber:11},void 0),d.jsxDEV("input",{type:"email",name:"email",value:s.email,onChange:m,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:87,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:83,columnNumber:9},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dirección"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:96,columnNumber:11},void 0),d.jsxDEV("input",{type:"text",name:"direccion",value:s.direccion,onChange:m,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:99,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:95,columnNumber:9},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:108,columnNumber:11},void 0),d.jsxDEV("input",{type:"text",name:"telefono",value:s.telefono,onChange:m,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:111,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:107,columnNumber:9},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sitio Web"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:120,columnNumber:11},void 0),d.jsxDEV("input",{type:"url",name:"sitio_web",value:s.sitio_web,onChange:m,className:"w-full p-2 border border-gray-300 rounded-md",placeholder:"https://ejemplo.com"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:123,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:119,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:69,columnNumber:7},void 0),d.jsxDEV("div",{className:"flex justify-end space-x-3 mt-6",children:[d.jsxDEV("button",{type:"button",onClick:a,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:136,columnNumber:9},void 0),d.jsxDEV("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:r,children:r?d.jsxDEV(d.Fragment,{children:[d.jsxDEV(p,{className:"animate-spin inline mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:150,columnNumber:15},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:149,columnNumber:13},void 0):e?"Actualizar":"Crear"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:143,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:135,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/InstitutionForm.jsx",lineNumber:68,columnNumber:5},void 0)},q=()=>{const[e,i]=D.useState([]),[a,s]=D.useState(!0),[n,r]=D.useState(!1),[t,o]=D.useState(null),[l,m]=D.useState("");D.useEffect(()=>{u()},[]);const u=()=>c(null,null,function*(){s(!0);try{const{data:e,error:a}=yield F.getInstitutions();if(a)throw a;i(e||[])}catch(e){T.error("Error al cargar las instituciones")}finally{s(!1)}}),b=(e=null)=>{o(e),r(!0)},v=()=>{r(!1),o(null)},h=e.filter(e=>{var i,a,s,n;return(null==(i=e.nombre)?void 0:i.toLowerCase().includes(l.toLowerCase()))||(null==(a=e.email)?void 0:a.toLowerCase().includes(l.toLowerCase()))||(null==(s=e.direccion)?void 0:s.toLowerCase().includes(l.toLowerCase()))||(null==(n=e.telefono)?void 0:n.toLowerCase().includes(l.toLowerCase()))});return d.jsxDEV("div",{className:"space-y-6",children:[d.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:113,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Administre las instituciones registradas en el sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:114,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:112,columnNumber:9},void 0),d.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-2",children:[d.jsxDEV("div",{className:"relative",children:[d.jsxDEV("input",{type:"text",placeholder:"Buscar institución...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:l,onChange:e=>m(e.target.value)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:118,columnNumber:13},void 0),d.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:d.jsxDEV("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsxDEV("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:127,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:126,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:125,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:117,columnNumber:11},void 0),d.jsxDEV("button",{onClick:()=>b(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:a,children:[d.jsxDEV(N,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:136,columnNumber:13},void 0),"Nueva Institución"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:131,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:116,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:111,columnNumber:7},void 0),d.jsxDEV("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:a?d.jsxDEV("div",{className:"flex justify-center items-center p-8",children:[d.jsxDEV(p,{className:"animate-spin text-blue-600 text-2xl mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:146,columnNumber:13},void 0),d.jsxDEV("span",{children:"Cargando instituciones..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:147,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:145,columnNumber:11},void 0):0===h.length?d.jsxDEV("div",{className:"text-center p-8 text-gray-500",children:l?"No se encontraron instituciones que coincidan con la búsqueda.":"No hay instituciones registradas."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:150,columnNumber:11},void 0):d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[d.jsxDEV("thead",{className:"bg-sky-800",children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:158,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:159,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Dirección"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:160,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:161,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Sitio Web"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:162,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:163,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:157,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:156,columnNumber:15},void 0),d.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map(e=>d.jsxDEV("tr",{className:"hover:bg-gray-50",children:[d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV(g,{className:"text-gray-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:171,columnNumber:25},void 0),d.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:e.nombre},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:172,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:170,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:169,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:175,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.direccion},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:176,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:177,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.sitio_web?d.jsxDEV("a",{href:e.sitio_web,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:e.sitio_web.replace(/^https?:\/\//,"").split("/")[0]},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:180,columnNumber:25},void 0):"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:178,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[d.jsxDEV("button",{onClick:()=>b(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:d.jsxDEV(f,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:192,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:188,columnNumber:23},void 0),d.jsxDEV("button",{onClick:()=>{return i=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar esta institución?"))try{s(!0);const{error:e}=yield F.deleteInstitution(i);if(e)throw e;T.success("Institución eliminada correctamente"),u()}catch(e){T.error("Error al eliminar la institución")}finally{s(!1)}});var i},className:"text-red-600 hover:text-red-900",children:d.jsxDEV(x,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:198,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:194,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:187,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:168,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:166,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:155,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:154,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:143,columnNumber:7},void 0),n&&d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[d.jsxDEV("h2",{className:"text-xl font-bold mb-4",children:t?"Editar Institución":"Nueva Institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:213,columnNumber:13},void 0),d.jsxDEV(O,{initialData:t,onSubmit:e=>c(null,null,function*(){try{let i;if(s(!0),t){if(i=yield F.updateInstitution(t.id,e),i.error)throw i.error;T.success("Institución actualizada correctamente")}else{if(i=yield F.createInstitution(e),i.error)throw i.error;T.success("Institución creada correctamente")}u(),v()}catch(i){T.error(t?"Error al actualizar la institución":"Error al crear la institución")}finally{s(!1)}}),onCancel:v},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:216,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:212,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:211,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/InstitutionsTab.jsx",lineNumber:109,columnNumber:5},void 0)},L=()=>{const[e,i]=D.useState([]),[a,s]=D.useState([]),[n,r]=D.useState(!0),[t,m]=D.useState(!1),[u,b]=D.useState(null),[g,j]=D.useState(""),[V,C]=D.useState({nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:""});D.useEffect(()=>{y(),E()},[]);const y=()=>c(null,null,function*(){r(!0);try{const{data:e,error:a}=yield F.getPsychologists();if(a)throw a;i(e||[])}catch(e){T.error("Error al cargar los psicólogos")}finally{r(!1)}}),E=()=>c(null,null,function*(){try{const{data:e,error:i}=yield F.getInstitutions();if(i)throw i;s(e||[])}catch(e){T.error("Error al cargar las instituciones")}}),B=(e=null)=>{b(e),C(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",email:e.email||"",telefono:e.telefono||"",especialidad:e.especialidad||"",institucion_id:e.institucion_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",email:"",telefono:"",especialidad:"",institucion_id:"",activo:!0}),m(!0)},w=()=>{m(!1),b(null)},P=e=>{const{name:i,value:a,type:s,checked:n}=e.target;C(l(o({},V),{[i]:"checkbox"===s?n:a}))},S=e.filter(e=>{var i,a,s,n;return(null==(i=e.nombre)?void 0:i.toLowerCase().includes(g.toLowerCase()))||(null==(a=e.apellido)?void 0:a.toLowerCase().includes(g.toLowerCase()))||(null==(s=e.email)?void 0:s.toLowerCase().includes(g.toLowerCase()))||(null==(n=e.especialidad)?void 0:n.toLowerCase().includes(g.toLowerCase()))}),_=e=>{const i=a.find(i=>i.id===e);return i?i.nombre:"No asignada"};return d.jsxDEV("div",{className:"space-y-6",children:[d.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:200,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Administre los psicólogos registrados en el sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:201,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:199,columnNumber:9},void 0),d.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-2",children:[d.jsxDEV("div",{className:"relative",children:[d.jsxDEV("input",{type:"text",placeholder:"Buscar psicólogo...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:g,onChange:e=>j(e.target.value)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:205,columnNumber:13},void 0),d.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:d.jsxDEV("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsxDEV("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:214,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:213,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:212,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:204,columnNumber:11},void 0),d.jsxDEV("button",{onClick:()=>B(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:n,children:[d.jsxDEV(N,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:223,columnNumber:13},void 0),"Nuevo Psicólogo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:218,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:203,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:198,columnNumber:7},void 0),d.jsxDEV("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:n&&0===e.length?d.jsxDEV("div",{className:"flex justify-center items-center p-8",children:[d.jsxDEV(p,{className:"animate-spin text-blue-600 text-2xl mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:233,columnNumber:13},void 0),d.jsxDEV("span",{children:"Cargando psicólogos..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:234,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:232,columnNumber:11},void 0):0===S.length?d.jsxDEV("div",{className:"text-center p-8 text-gray-500",children:g?"No se encontraron psicólogos que coincidan con la búsqueda.":"No hay psicólogos registrados."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:237,columnNumber:11},void 0):d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[d.jsxDEV("thead",{className:"bg-sky-800",children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:245,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:246,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:247,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Especialidad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:248,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:249,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:250,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:251,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:244,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:243,columnNumber:15},void 0),d.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:S.map(e=>d.jsxDEV("tr",{className:"hover:bg-gray-50",children:[d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:d.jsxDEV("div",{className:"flex items-center",children:["femenino"===e.genero?d.jsxDEV(v,{className:"text-pink-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:260,columnNumber:27},void 0):d.jsxDEV(h,{className:"text-blue-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:262,columnNumber:27},void 0),d.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:264,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:258,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:257,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:269,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.telefono||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:270,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.especialidad||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:271,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:_(e.institucion_id)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:272,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:d.jsxDEV("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full "+(e.activo?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.activo?"Activo":"Inactivo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:276,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:275,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[d.jsxDEV("button",{onClick:()=>B(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:d.jsxDEV(f,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:287,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:283,columnNumber:23},void 0),d.jsxDEV("button",{onClick:()=>{return i=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este psicólogo?"))try{r(!0);const{error:e}=yield F.deletePsychologist(i);if(e)throw e;T.success("Psicólogo eliminado correctamente"),y()}catch(e){T.error("Error al eliminar el psicólogo")}finally{r(!1)}});var i},className:"text-red-600 hover:text-red-900",children:d.jsxDEV(x,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:293,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:289,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:282,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:256,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:254,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:242,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:241,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:230,columnNumber:7},void 0),t&&d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[d.jsxDEV("h2",{className:"text-xl font-bold mb-4",children:u?"Editar Psicólogo":"Nuevo Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:308,columnNumber:13},void 0),d.jsxDEV("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),V.nombre.trim()?V.apellido.trim()?V.institucion_id?!V.email||/\S+@\S+\.\S+/.test(V.email)||(T.error("El email no es válido"),0):(T.error("Debe seleccionar una institución"),0):(T.error("El apellido es obligatorio"),0):(T.error("El nombre es obligatorio"),0))try{let e;if(r(!0),u){if(e=yield F.updatePsychologist(u.id,V),e.error)throw e.error;T.success("Psicólogo actualizado correctamente")}else{if(e=yield F.createPsychologist(V),e.error)throw e.error;T.success("Psicólogo creado correctamente")}y(),w()}catch(i){T.error(u?"Error al actualizar el psicólogo":"Error al crear el psicólogo")}finally{r(!1)}}),className:"space-y-4",children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:314,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"nombre",value:V.nombre,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:317,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:313,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:327,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"apellido",value:V.apellido,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:330,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:326,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:340,columnNumber:19},void 0),d.jsxDEV("select",{name:"genero",value:V.genero,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md",children:[d.jsxDEV("option",{value:"masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:349,columnNumber:21},void 0),d.jsxDEV("option",{value:"femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:350,columnNumber:21},void 0),d.jsxDEV("option",{value:"otro",children:"Otro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:351,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:343,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:339,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:355,columnNumber:19},void 0),d.jsxDEV("input",{type:"email",name:"email",value:V.email,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:358,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:354,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Teléfono"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:367,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"telefono",value:V.telefono,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:370,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:366,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Especialidad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:379,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"especialidad",value:V.especialidad,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:382,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:378,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:391,columnNumber:19},void 0),d.jsxDEV("select",{name:"institucion_id",value:V.institucion_id,onChange:P,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[d.jsxDEV("option",{value:"",children:"Seleccione una institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:401,columnNumber:21},void 0),a.map(e=>d.jsxDEV("option",{value:e.id,children:e.nombre},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:403,columnNumber:23},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:394,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:390,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:312,columnNumber:15},void 0),d.jsxDEV("div",{className:"flex justify-end space-x-3 mt-6",children:[d.jsxDEV("button",{type:"button",onClick:w,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:412,columnNumber:17},void 0),d.jsxDEV("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:n,children:n?d.jsxDEV(d.Fragment,{children:[d.jsxDEV(p,{className:"animate-spin inline mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:426,columnNumber:23},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:425,columnNumber:21},void 0):u?"Actualizar":"Crear"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:419,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:411,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:311,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:307,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:306,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PsychologistsTab.jsx",lineNumber:196,columnNumber:5},void 0)},z=()=>{const[e,i]=D.useState([]),[a,s]=D.useState([]),[n,r]=D.useState([]),[t,m]=D.useState(!0),[u,b]=D.useState(!1),[g,j]=D.useState(null),[V,C]=D.useState(""),[y,E]=D.useState("nombre"),[B,w]=D.useState("asc"),[P,S]=D.useState({institucion_id:"",genero:"",psicologo_id:"",edad_min:"",edad_max:""}),[_,I]=D.useState({nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",email:"",nivel_educativo:"",ocupacion:"",institucion_id:"",psicologo_id:""});D.useEffect(()=>{M(),k(),A()},[]);const M=()=>c(null,null,function*(){m(!0);try{const{data:e,error:a}=yield F.getPatients();if(a)throw a;const s=(Array.isArray(e)?e:[]).map(e=>l(o({},e),{edad:L(e.fecha_nacimiento)}));i(s)}catch(e){T.error("Error: "+(e.message||"Error al cargar los pacientes"))}finally{m(!1)}}),k=()=>c(null,null,function*(){try{const{data:e,error:i}=yield F.getInstitutions();if(i)throw i;s(e||[])}catch(e){T.error("Error: "+(e.message||"Error al cargar las instituciones"))}}),A=()=>c(null,null,function*(){try{const{data:e,error:i}=yield F.getPsychologists();if(i)throw i;r(e||[])}catch(e){T.error("Error: "+(e.message||"Error al cargar los psicólogos"))}}),$=(e=null)=>{j(e),I(e?{nombre:e.nombre||"",apellido:e.apellido||"",genero:e.genero||"masculino",fecha_nacimiento:e.fecha_nacimiento||"",documento:e.documento||"",email:e.email||"",institucion_id:e.institucion_id||"",psicologo_id:e.psicologo_id||"",activo:!1!==e.activo}:{nombre:"",apellido:"",genero:"masculino",fecha_nacimiento:"",documento:"",email:"",institucion_id:"",psicologo_id:"",activo:!0}),b(!0)},O=()=>{b(!1),j(null)},q=e=>{const{name:i,value:a,type:s,checked:n}=e.target;I(l(o({},_),{[i]:"checkbox"===s?n:a}))},L=e=>{if(!e)return"-";const i=new Date,a=new Date(e);let s=i.getFullYear()-a.getFullYear();const n=i.getMonth()-a.getMonth();return(n<0||0===n&&i.getDate()<a.getDate())&&s--,s},z=e=>{const i=a.find(i=>i.id===e);return i?i.nombre:"No asignada"},R=e=>{const i=n.find(i=>i.id===e);return i?`${i.nombre} ${i.apellido}`:"No asignado"},G=e.filter(e=>{var i,s,n,r,t,o;const l=V.toLowerCase(),m=!V||(null==(i=e.nombre)?void 0:i.toLowerCase().includes(l))||(null==(s=e.apellidos)?void 0:s.toLowerCase().includes(l))||(null==(n=e.documento_identidad)?void 0:n.toLowerCase().includes(l))||(null==(r=e.email)?void 0:r.toLowerCase().includes(l))||((null==(t=a.find(i=>i.id===e.institucion_id))?void 0:t.nombre)||"").toLowerCase().includes(l)||(null==(o=e.notas)?void 0:o.toLowerCase().includes(l)),u=!P.institucion_id||e.institucion_id===P.institucion_id,c=!P.genero||e.genero===P.genero,d=!P.psicologo_id||("null"===P.psicologo_id?!e.psicologo_id:e.psicologo_id===P.psicologo_id),b="number"==typeof e.edad?e.edad:-1,p=parseInt(P.edad_min),N=isNaN(p)||-1===b||b>=p,g=parseInt(P.edad_max),f=isNaN(g)||-1===b||b<=g;return m&&u&&c&&d&&N&&f});return a.map(e=>({value:e.id,label:e.nombre})),n.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),a.map(e=>({value:e.id,label:e.nombre})),n.map(e=>({value:e.id,label:`${e.nombre} ${e.apellidos}`})),d.jsxDEV("div",{className:"space-y-6",children:[d.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("h2",{className:"text-xl font-bold text-gray-900",children:"Gestión de Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:497,columnNumber:11},void 0),d.jsxDEV("p",{className:"text-gray-600",children:"Administre los pacientes registrados en el sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:498,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:496,columnNumber:9},void 0),d.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-2",children:[d.jsxDEV("div",{className:"relative",children:[d.jsxDEV("input",{type:"text",placeholder:"Buscar paciente...",className:"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",value:V,onChange:e=>C(e.target.value)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:502,columnNumber:13},void 0),d.jsxDEV("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:d.jsxDEV("svg",{className:"h-5 w-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsxDEV("path",{fillRule:"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z",clipRule:"evenodd"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:511,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:510,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:509,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:501,columnNumber:11},void 0),d.jsxDEV("button",{onClick:()=>$(),className:"flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:t,children:[d.jsxDEV(N,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:520,columnNumber:13},void 0),"Nuevo Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:515,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:500,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:495,columnNumber:7},void 0),d.jsxDEV("div",{className:"bg-white shadow overflow-hidden rounded-lg",children:t&&0===e.length?d.jsxDEV("div",{className:"flex justify-center items-center p-8",children:[d.jsxDEV(p,{className:"animate-spin text-blue-600 text-2xl mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:530,columnNumber:13},void 0),d.jsxDEV("span",{children:"Cargando pacientes..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:531,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:529,columnNumber:11},void 0):0===G.length?d.jsxDEV("div",{className:"text-center p-8 text-gray-500",children:V?"No se encontraron pacientes que coincidan con la búsqueda.":"No hay pacientes registrados."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:534,columnNumber:11},void 0):d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[d.jsxDEV("thead",{className:"bg-sky-800",children:d.jsxDEV("tr",{children:[d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:542,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:543,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Edad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:544,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Contacto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:545,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:546,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:547,columnNumber:19},void 0),d.jsxDEV("th",{className:"px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:548,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:541,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:540,columnNumber:15},void 0),d.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:G.map(e=>d.jsxDEV("tr",{className:"hover:bg-gray-50",children:[d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:d.jsxDEV("div",{className:"flex items-center",children:["femenino"===e.genero?d.jsxDEV(v,{className:"text-pink-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:557,columnNumber:27},void 0):d.jsxDEV(h,{className:"text-blue-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:559,columnNumber:27},void 0),d.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:561,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:555,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:554,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.documento||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:566,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:L(e.fecha_nacimiento)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:567,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.email?d.jsxDEV("div",{children:d.jsxDEV("div",{children:e.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:571,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:570,columnNumber:25},void 0):e.email||"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:568,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:z(e.institucion_id)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:577,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:R(e.psicologo_id)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:580,columnNumber:21},void 0),d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[d.jsxDEV("button",{onClick:()=>$(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:d.jsxDEV(f,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:588,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:584,columnNumber:23},void 0),d.jsxDEV("button",{onClick:()=>{return i=e.id,c(null,null,function*(){if(window.confirm("¿Está seguro de que desea eliminar este paciente?"))try{m(!0);const{error:e}=yield F.deletePatient(i);if(e)throw e;T.success("Paciente eliminado correctamente"),M()}catch(e){T.error("Error: "+(e.message||"Error al eliminar el paciente"))}finally{m(!1)}});var i},className:"text-red-600 hover:text-red-900",children:d.jsxDEV(x,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:594,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:590,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:583,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:553,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:551,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:539,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:538,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:527,columnNumber:7},void 0),u&&d.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxDEV("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-3xl mx-4",children:[d.jsxDEV("h2",{className:"text-xl font-bold mb-4",children:g?"Editar Paciente":"Nuevo Paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:609,columnNumber:13},void 0),d.jsxDEV("form",{onSubmit:e=>c(null,null,function*(){if(e.preventDefault(),_.nombre.trim()?_.apellido.trim()?_.institucion_id?!_.email||/\S+@\S+\.\S+/.test(_.email)||(T.error("El email no es válido"),0):(T.error("Debe seleccionar una institución"),0):(T.error("El apellido es obligatorio"),0):(T.error("El nombre es obligatorio"),0))try{m(!0);const e={nombre:_.nombre,apellido:_.apellido,genero:_.genero,fecha_nacimiento:_.fecha_nacimiento||null,documento:_.documento||"",email:_.email||"",institucion_id:_.institucion_id,psicologo_id:_.psicologo_id||null,activo:_.activo,updated_at:(new Date).toISOString()};let i;if(g||(e.created_at=(new Date).toISOString()),g){if(i=yield F.updatePatient(g.id,e),i.error)throw i.error;T.success(`Paciente "${_.nombre}" actualizado correctamente`)}else{if(i=yield F.createPatient(e),i.error)throw i.error;T.success(`Paciente "${_.nombre}" creado correctamente`)}M(),O()}catch(i){T.error("Error: "+(i.message||(g?"Error al actualizar el paciente":"Error al crear el paciente")))}finally{m(!1)}}),className:"space-y-4",children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:615,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"nombre",value:_.nombre,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:618,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:614,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:628,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"apellido",value:_.apellido,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:631,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:627,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:641,columnNumber:19},void 0),d.jsxDEV("select",{name:"genero",value:_.genero,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",children:[d.jsxDEV("option",{value:"masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:650,columnNumber:21},void 0),d.jsxDEV("option",{value:"femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:651,columnNumber:21},void 0),d.jsxDEV("option",{value:"otro",children:"Otro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:652,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:644,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:640,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Fecha de Nacimiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:656,columnNumber:19},void 0),d.jsxDEV("input",{type:"date",name:"fecha_nacimiento",value:_.fecha_nacimiento,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:659,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:655,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:668,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"documento",value:_.documento,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:671,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:667,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:681,columnNumber:19},void 0),d.jsxDEV("input",{type:"email",name:"email",value:_.email,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:684,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:680,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nivel Educativo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:693,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"nivel_educativo",value:_.nivel_educativo,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:696,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:692,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ocupación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:705,columnNumber:19},void 0),d.jsxDEV("input",{type:"text",name:"ocupacion",value:_.ocupacion,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:708,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:704,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Institución *"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:717,columnNumber:19},void 0),d.jsxDEV("select",{name:"institucion_id",value:_.institucion_id,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",required:!0,children:[d.jsxDEV("option",{value:"",children:"Seleccione una institución"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:727,columnNumber:21},void 0),a.map(e=>d.jsxDEV("option",{value:e.id,children:e.nombre},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:729,columnNumber:23},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:720,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:716,columnNumber:17},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:736,columnNumber:19},void 0),d.jsxDEV("select",{name:"psicologo_id",value:_.psicologo_id,onChange:q,className:"w-full p-2 border border-gray-300 rounded-md",children:[d.jsxDEV("option",{value:"",children:"Seleccione un psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:745,columnNumber:21},void 0),n.map(e=>d.jsxDEV("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:747,columnNumber:23},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:739,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:735,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:613,columnNumber:15},void 0),d.jsxDEV("div",{className:"flex justify-end space-x-3 mt-6",children:[d.jsxDEV("button",{type:"button",onClick:O,className:"px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:756,columnNumber:17},void 0),d.jsxDEV("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",disabled:t,children:t?d.jsxDEV(d.Fragment,{children:[d.jsxDEV(p,{className:"animate-spin inline mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:770,columnNumber:23},void 0),"Guardando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:769,columnNumber:21},void 0):g?"Actualizar":"Crear"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:763,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:755,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:612,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:608,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:607,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/tabs/PatientsTab.jsx",lineNumber:493,columnNumber:5},void 0)},R=({onConnectionChange:e})=>{const[i,a]=D.useState(!1),[s,n]=D.useState("No probado"),[r,t]=D.useState([]),[m,u]=D.useState(""),[p,N]=D.useState([]),[g,f]=D.useState({status:"No probado",result:null,loading:!1}),[x,v]=D.useState({environment:null,database:null,functions:null,permissions:null});return D.useEffect(()=>{var e;m&&(e=m,c(null,null,function*(){if(e){a(!0);try{const{data:i,error:a}=yield b.from(e).select("*").limit(10);if(a)throw a;N(i||[]),T.success(`Datos de la tabla ${e} cargados correctamente`)}catch(i){T.error(`Error al cargar datos: ${i.message||"Desconocido"}`),N([])}finally{a(!1)}}}))},[m]),d.jsxDEV("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[d.jsxDEV("h2",{className:"text-2xl font-bold mb-4 text-center",children:"Prueba de Conexión a Supabase"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:216,columnNumber:7},void 0),d.jsxDEV("div",{className:"mb-6",children:[d.jsxDEV("div",{className:"flex items-center justify-between mb-2",children:[d.jsxDEV("span",{className:"font-semibold",children:"Estado de la conexión:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:220,columnNumber:11},void 0),d.jsxDEV("span",{className:"px-3 py-1 rounded-full text-sm "+("Conectado"===s?"bg-green-100 text-green-800":"No probado"===s?"bg-gray-100 text-gray-800":"bg-red-100 text-red-800"),children:s},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:221,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:219,columnNumber:9},void 0),d.jsxDEV("div",{className:"flex gap-2",children:[d.jsxDEV("button",{onClick:()=>c(null,null,function*(){a(!0);try{const a={supabaseUrl:"https://ydglduxhgwajqdseqzpy.supabase.co",hasAnonKey:!0,urlValid:"https://ydglduxhgwajqdseqzpy.supabase.co".includes("supabase.co"),status:"success"};a.supabaseUrl&&a.hasAnonKey||(a.status="error",a.message="Variables de entorno faltantes"),v(e=>l(o({},e),{environment:a}));let s={status:"checking"};try{const{data:e,error:i}=yield b.from("information_schema.columns").select("table_name").eq("table_schema","public").order("table_name");if(i)throw i;const a=[...new Set(e.map(e=>e.table_name))],r=a.map(e=>({name:e,schema:"public"}));s={status:"success",tablesCount:a.length,tables:r},t(r),n("Conectado")}catch(i){s={status:"error",error:i.message,code:i.code},n(`Error: ${i.message}`)}v(e=>l(o({},e),{database:s}));let r={status:"info",available:!0,message:"Verificación de funciones de conversión deshabilitada"};v(e=>l(o({},e),{functions:r}));let m={status:"checking"};try{const{data:e,error:i}=yield b.from("resultados").select("id").limit(1);if(i)throw i;m={status:"success",canRead:!0,message:"Permisos de lectura verificados"}}catch(i){m={status:"warning",canRead:!1,error:i.message,message:"Permisos limitados detectados"}}v(e=>l(o({},e),{permissions:m}));"success"===s.status&&"success"===r.status&&"success"===m.status?(T.success("✅ Todos los diagnósticos completados exitosamente"),e&&e(!0)):(T.warning("⚠️ Diagnósticos completados con advertencias"),e&&e("success"===s.status))}catch(i){T.error(`Error en diagnósticos: ${i.message}`),e&&e(!1)}finally{a(!1)}}),disabled:i,className:"flex-1 bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300",children:i?"Ejecutando diagnósticos...":"Diagnósticos Completos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:233,columnNumber:11},void 0),d.jsxDEV("button",{onClick:()=>c(null,null,function*(){f(e=>l(o({},e),{loading:!0,status:"Probando..."}));try{const e=yield M.probarConversion(25,"V",13);e.success?(f({loading:!1,status:"Exitoso",result:{input:{pd:25,aptitud:"V",edad:13},output:{percentil:e.percentil},message:`PD 25 → PC ${e.percentil}`}}),T.success(`✅ Conversión exitosa: PC ${e.percentil}`)):(f({loading:!1,status:"Error",result:{error:e.error,validationErrors:e.validationErrors}}),T.error("❌ Error en conversión PD a PC"))}catch(e){f({loading:!1,status:"Error",result:{error:e.message}}),T.error(`❌ Error inesperado: ${e.message}`)}}),disabled:g.loading,className:"flex-1 bg-green-600 text-white py-2 rounded-md hover:bg-green-700 transition-colors disabled:bg-green-300",children:g.loading?"Probando...":"Probar Conversión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:241,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:232,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:218,columnNumber:7},void 0),Object.keys(x).length>0&&d.jsxDEV("div",{className:"mb-6 space-y-4",children:[d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800",children:"Resultados de Diagnósticos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:254,columnNumber:11},void 0),x.environment&&d.jsxDEV("div",{className:"p-4 rounded-lg border "+("success"===x.environment.status?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[d.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"🔧 Variables de Entorno"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:263,columnNumber:15},void 0),d.jsxDEV("div",{className:"text-sm space-y-1",children:[d.jsxDEV("p",{children:["URL: ",x.environment.supabaseUrl||"No definida"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:265,columnNumber:17},void 0),d.jsxDEV("p",{children:["Anon Key: ",x.environment.hasAnonKey?"✅ Definida":"❌ Faltante"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:266,columnNumber:17},void 0),d.jsxDEV("p",{children:["URL Válida: ",x.environment.urlValid?"✅ Sí":"❌ No"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:267,columnNumber:17},void 0),x.environment.message&&d.jsxDEV("p",{className:"text-red-600",children:x.environment.message},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:269,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:264,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:258,columnNumber:13},void 0),x.database&&d.jsxDEV("div",{className:"p-4 rounded-lg border "+("success"===x.database.status?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[d.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"🗄️ Base de Datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:282,columnNumber:15},void 0),d.jsxDEV("div",{className:"text-sm space-y-1",children:"success"===x.database.status?d.jsxDEV(d.Fragment,{children:[d.jsxDEV("p",{children:"✅ Conexión exitosa"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:286,columnNumber:21},void 0),d.jsxDEV("p",{children:["Tablas encontradas: ",x.database.tablesCount]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:287,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:285,columnNumber:19},void 0):d.jsxDEV(d.Fragment,{children:[d.jsxDEV("p",{children:"❌ Error de conexión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:291,columnNumber:21},void 0),d.jsxDEV("p",{className:"text-red-600",children:x.database.error},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:292,columnNumber:21},void 0),x.database.code&&d.jsxDEV("p",{className:"text-red-500",children:["Código: ",x.database.code]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:294,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:290,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:283,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:277,columnNumber:13},void 0),x.functions&&d.jsxDEV("div",{className:"p-4 rounded-lg border "+("success"===x.functions.status?"bg-green-50 border-green-200":"warning"===x.functions.status?"bg-yellow-50 border-yellow-200":"bg-red-50 border-red-200"),children:[d.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"⚙️ Funciones de Conversión"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:311,columnNumber:15},void 0),d.jsxDEV("div",{className:"text-sm space-y-1",children:[d.jsxDEV("p",{children:[x.functions.available?"✅":"❌"," ",x.functions.message]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:313,columnNumber:17},void 0),x.functions.error&&d.jsxDEV("p",{className:"text-red-600",children:x.functions.error},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:315,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:312,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:304,columnNumber:13},void 0),x.permissions&&d.jsxDEV("div",{className:"p-4 rounded-lg border "+("success"===x.permissions.status?"bg-green-50 border-green-200":"bg-yellow-50 border-yellow-200"),children:[d.jsxDEV("h4",{className:"font-medium text-gray-800 mb-2",children:"🔐 Permisos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:328,columnNumber:15},void 0),d.jsxDEV("div",{className:"text-sm space-y-1",children:[d.jsxDEV("p",{children:[x.permissions.canRead?"✅":"❌"," ",x.permissions.message]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:330,columnNumber:17},void 0),x.permissions.error&&d.jsxDEV("p",{className:"text-yellow-600",children:x.permissions.error},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:332,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:329,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:323,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:253,columnNumber:9},void 0),g.status&&"Pendiente"!==g.status&&d.jsxDEV("div",{className:"mb-6",children:[d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"Prueba de Conversión PD→PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:343,columnNumber:11},void 0),d.jsxDEV("div",{className:"p-4 rounded-lg border "+("Exitoso"===g.status?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:d.jsxDEV("div",{className:"text-sm space-y-2",children:[d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Estado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:350,columnNumber:18},void 0)," ",g.status]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:350,columnNumber:15},void 0),g.result&&d.jsxDEV(d.Fragment,{children:[g.result.message&&d.jsxDEV("p",{className:"text-green-700 font-medium",children:g.result.message},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:354,columnNumber:21},void 0),g.result.input&&d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Entrada:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:357,columnNumber:24},void 0)," PD=",g.result.input.pd,", Aptitud=",g.result.input.aptitud,", Edad=",g.result.input.edad]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:357,columnNumber:21},void 0),g.result.output&&d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Resultado:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:360,columnNumber:24},void 0)," PC=",g.result.output.percentil]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:360,columnNumber:21},void 0),g.result.error&&d.jsxDEV("p",{className:"text-red-600",children:[d.jsxDEV("strong",{children:"Error:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:363,columnNumber:49},void 0)," ",g.result.error]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:363,columnNumber:21},void 0),g.result.validationErrors&&d.jsxDEV("div",{className:"text-red-600",children:[d.jsxDEV("strong",{children:"Errores de validación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:367,columnNumber:23},void 0),d.jsxDEV("ul",{className:"list-disc list-inside ml-4",children:g.result.validationErrors.map((e,i)=>d.jsxDEV("li",{children:e},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:370,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:368,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:366,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:352,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:349,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:344,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:342,columnNumber:9},void 0),r.length>0&&d.jsxDEV("div",{className:"mb-6",children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Seleccionar tabla:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:384,columnNumber:11},void 0),d.jsxDEV("select",{value:m,onChange:e=>u(e.target.value),className:"w-full border border-gray-300 rounded-md py-2 px-3",disabled:i,children:[d.jsxDEV("option",{value:"",children:"Seleccione una tabla"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:393,columnNumber:13},void 0),r.map(e=>d.jsxDEV("option",{value:e.name,children:e.name},e.name,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:395,columnNumber:15},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:387,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:383,columnNumber:9},void 0),p.length>0&&d.jsxDEV("div",{children:[d.jsxDEV("h3",{className:"text-lg font-semibold mb-2",children:["Datos de ",m]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:405,columnNumber:11},void 0),d.jsxDEV("div",{className:"overflow-x-auto",children:d.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[d.jsxDEV("thead",{className:"bg-gray-50",children:d.jsxDEV("tr",{children:Object.keys(p[0]).map(e=>d.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e},e,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:411,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:409,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:408,columnNumber:15},void 0),d.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map((e,i)=>d.jsxDEV("tr",{children:Object.values(e).map((e,i)=>d.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:"object"==typeof e?JSON.stringify(e):String(e)},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:424,columnNumber:23},void 0))},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:422,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:420,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:407,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:406,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:404,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/SupabaseTest.jsx",lineNumber:215,columnNumber:5},void 0)},G=()=>{const[e,i]=D.useState(!1),[a,s]=D.useState(null),[n,r]=D.useState(!1),[t,m]=D.useState({puntajeDirecto:25,aptitudCodigo:"V",edad:13});D.useEffect(()=>{u()},[]);const u=()=>c(null,null,function*(){const e=yield M.obtenerEstadisticasConversion();s(e)});return d.jsxDEV("div",{className:"space-y-6",children:[d.jsxDEV(w,{children:[d.jsxDEV(P,{children:d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800",children:[d.jsxDEV("i",{className:"fas fa-cogs mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:93,columnNumber:13},void 0),"Estado del Sistema de Conversión"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:92,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:91,columnNumber:9},void 0),d.jsxDEV(S,{children:d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV("div",{className:"flex items-center",children:[d.jsxDEV("div",{className:"w-3 h-3 rounded-full mr-3 "+(n?"bg-green-500":"bg-red-500")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:100,columnNumber:15},void 0),d.jsxDEV("span",{className:"text-sm",children:["Funciones de Supabase: ",n?"Disponibles":"No disponibles"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:101,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:99,columnNumber:13},void 0),a&&d.jsxDEV(d.Fragment,{children:[d.jsxDEV("div",{className:"text-sm",children:[d.jsxDEV("span",{className:"font-medium",children:"Total resultados:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:109,columnNumber:19},void 0)," ",a.totalResultados]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:108,columnNumber:17},void 0),d.jsxDEV("div",{className:"text-sm",children:[d.jsxDEV("span",{className:"font-medium",children:"Con percentil:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:112,columnNumber:19},void 0)," ",a.conPercentil]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:111,columnNumber:17},void 0),d.jsxDEV("div",{className:"text-sm",children:[d.jsxDEV("span",{className:"font-medium",children:"Sin percentil:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:115,columnNumber:19},void 0)," ",a.sinPercentil]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:114,columnNumber:17},void 0),d.jsxDEV("div",{className:"text-sm",children:[d.jsxDEV("span",{className:"font-medium",children:"% Convertido:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:118,columnNumber:19},void 0)," ",a.porcentajeConvertido,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:117,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:107,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:98,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:97,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:90,columnNumber:7},void 0),d.jsxDEV(w,{children:[d.jsxDEV(P,{children:d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800",children:[d.jsxDEV("i",{className:"fas fa-tools mr-2 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:130,columnNumber:13},void 0),"Acciones de Conversión"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:129,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:128,columnNumber:9},void 0),d.jsxDEV(S,{children:d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxDEV(I,{onClick:()=>c(null,null,function*(){i(!0);try{yield M.configurarConversionAutomatica(),yield u()}catch(e){}finally{i(!1)}}),disabled:e,variant:"primary",className:"w-full",children:[e?d.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:143,columnNumber:17},void 0):d.jsxDEV("i",{className:"fas fa-play mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:145,columnNumber:17},void 0),"Configurar Conversión Automática"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:136,columnNumber:13},void 0),d.jsxDEV(I,{onClick:()=>c(null,null,function*(){i(!0);try{yield M.recalcularTodosLosPercentiles(),yield u()}catch(e){}finally{i(!1)}}),disabled:e,variant:"secondary",className:"w-full",children:[e?d.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:157,columnNumber:17},void 0):d.jsxDEV("i",{className:"fas fa-sync mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:159,columnNumber:17},void 0),"Recalcular Percentiles Existentes"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:150,columnNumber:13},void 0),d.jsxDEV(I,{onClick:()=>c(null,null,function*(){i(!0);try{(yield M.verificarBaremos()).success&&alert("Baremos verificados correctamente. Ver consola para detalles.")}catch(e){}finally{i(!1)}}),disabled:e,variant:"outline",className:"w-full",children:[e?d.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:171,columnNumber:17},void 0):d.jsxDEV("i",{className:"fas fa-check mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:173,columnNumber:17},void 0),"Verificar Baremos"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:164,columnNumber:13},void 0),d.jsxDEV(I,{onClick:u,disabled:e,variant:"outline",className:"w-full",children:[e?d.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:185,columnNumber:17},void 0):d.jsxDEV("i",{className:"fas fa-refresh mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:187,columnNumber:17},void 0),"Actualizar Estadísticas"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:178,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:135,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:134,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:127,columnNumber:7},void 0),d.jsxDEV(w,{children:[d.jsxDEV(P,{children:d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800",children:[d.jsxDEV("i",{className:"fas fa-flask mr-2 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:199,columnNumber:13},void 0),"Probar Conversión PD → PC"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:198,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:197,columnNumber:9},void 0),d.jsxDEV(S,{children:[d.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Puntaje PD"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:206,columnNumber:15},void 0),d.jsxDEV("input",{type:"number",value:t.puntajeDirecto,onChange:e=>m(l(o({},t),{puntajeDirecto:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"100"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:209,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:205,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Aptitud"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:220,columnNumber:15},void 0),d.jsxDEV("select",{value:t.aptitudCodigo,onChange:e=>m(l(o({},t),{aptitudCodigo:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"V",children:"V - Aptitud Verbal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:228,columnNumber:17},void 0),d.jsxDEV("option",{value:"E",children:"E - Aptitud Espacial"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:229,columnNumber:17},void 0),d.jsxDEV("option",{value:"A",children:"A - Atención"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:230,columnNumber:17},void 0),d.jsxDEV("option",{value:"R",children:"R - Razonamiento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:231,columnNumber:17},void 0),d.jsxDEV("option",{value:"N",children:"N - Aptitud Numérica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:232,columnNumber:17},void 0),d.jsxDEV("option",{value:"M",children:"M - Aptitud Mecánica"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:233,columnNumber:17},void 0),d.jsxDEV("option",{value:"O",children:"O - Ortografía"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:234,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:223,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:219,columnNumber:13},void 0),d.jsxDEV("div",{children:[d.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Edad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:239,columnNumber:15},void 0),d.jsxDEV("select",{value:t.edad,onChange:e=>m(l(o({},t),{edad:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[d.jsxDEV("option",{value:"12",children:"12 años"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:247,columnNumber:17},void 0),d.jsxDEV("option",{value:"13",children:"13 años"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:248,columnNumber:17},void 0),d.jsxDEV("option",{value:"14",children:"14 años"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:249,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:242,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:238,columnNumber:13},void 0),d.jsxDEV("div",{className:"flex items-end",children:d.jsxDEV(I,{onClick:()=>c(null,null,function*(){i(!0);try{const e=yield M.probarConversion(parseInt(t.puntajeDirecto),t.aptitudCodigo,parseInt(t.edad));e.success?alert(`Conversión exitosa: PD ${t.puntajeDirecto} → PC ${e.percentil}`):alert("Error en la conversión")}catch(e){}finally{i(!1)}}),disabled:e,variant:"primary",className:"w-full",children:[e?d.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:261,columnNumber:19},void 0):d.jsxDEV("i",{className:"fas fa-calculator mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:263,columnNumber:19},void 0),"Probar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:254,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:253,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:204,columnNumber:11},void 0),d.jsxDEV("div",{className:"text-sm text-gray-600",children:d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Ejemplo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:271,columnNumber:16},void 0)," PD 25 en Aptitud Verbal para 13 años debería dar PC 50 aproximadamente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:271,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:270,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:203,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:196,columnNumber:7},void 0),d.jsxDEV(w,{children:[d.jsxDEV(P,{children:d.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800",children:[d.jsxDEV("i",{className:"fas fa-info-circle mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:280,columnNumber:13},void 0),"Información del Sistema"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:279,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:278,columnNumber:9},void 0),d.jsxDEV(S,{children:d.jsxDEV("div",{className:"text-sm text-gray-700 space-y-2",children:[d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Conversión Automática:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:286,columnNumber:16},void 0)," Los nuevos resultados se convierten automáticamente usando triggers de Supabase."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:286,columnNumber:13},void 0),d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Baremos:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:287,columnNumber:16},void 0)," Se utilizan las tablas de baremos para edades 12-13 y 13-14 años."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:287,columnNumber:13},void 0),d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Recálculo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:288,columnNumber:16},void 0)," Permite actualizar resultados existentes que no tienen percentil calculado."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:288,columnNumber:13},void 0),d.jsxDEV("p",{children:[d.jsxDEV("strong",{children:"Verificación:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:289,columnNumber:16},void 0)," Comprueba que las funciones y baremos estén correctamente configurados en Supabase."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:289,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:285,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:284,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:277,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/ConversionManager.jsx",lineNumber:88,columnNumber:5},void 0)},H=Object.freeze(Object.defineProperty({__proto__:null,default:()=>{const[e,i]=D.useState("institutions"),[a,s]=D.useState(!0),[n,r]=D.useState(!1),t=B(),o=new URLSearchParams(t.search).get("tab");D.useEffect(()=>{o&&["institutions","psychologists","patients","supabase","conversion"].includes(o)&&i(o);return(()=>{let e=document.getElementById("modal-root");e||(e=document.createElement("div"),e.id="modal-root",document.body.appendChild(e),e.style.position="relative",e.style.zIndex="9999")})(),setTimeout(()=>{s(!1)},1e3),()=>{const e=document.getElementById("modal-root");e&&0===e.childElementCount&&document.body.removeChild(e)}},[o]);const l=e=>{i(e)};return a?d.jsxDEV("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-100",children:[d.jsxDEV(p,{className:"animate-spin text-blue-600 text-4xl mb-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:103,columnNumber:9},void 0),d.jsxDEV("h2",{className:"text-xl font-semibold text-gray-700",children:"Cargando Panel de Administración..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:104,columnNumber:9},void 0),d.jsxDEV("p",{className:"text-gray-500 mt-2",children:"Verificando permisos y cargando datos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:105,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:102,columnNumber:7},void 0):d.jsxDEV("div",{className:"bg-gray-100 min-h-screen",children:[d.jsxDEV(k,{title:"Panel de Administración",subtitle:"Gestión centralizada de recursos de la plataforma",icon:j},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:116,columnNumber:7},void 0),d.jsxDEV("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:d.jsxDEV("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[d.jsxDEV("div",{className:"border-b border-gray-200",children:d.jsxDEV("nav",{className:"flex -mb-px",children:[d.jsxDEV("button",{className:`px-4 py-4 text-center text-sm font-medium ${"institutions"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>l("institutions"),children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV(g,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:137,columnNumber:19},void 0),d.jsxDEV("span",{children:"Instituciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:138,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:136,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:128,columnNumber:15},void 0),d.jsxDEV("button",{className:`px-4 py-4 text-center text-sm font-medium ${"psychologists"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>l("psychologists"),children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV(V,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:151,columnNumber:19},void 0),d.jsxDEV("span",{children:"Psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:152,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:150,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:142,columnNumber:15},void 0),d.jsxDEV("button",{className:`px-4 py-4 text-center text-sm font-medium ${"patients"===e?"border-b-2 border-blue-500 text-amber-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>l("patients"),children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV(C,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:165,columnNumber:19},void 0),d.jsxDEV("span",{children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:166,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:164,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:156,columnNumber:15},void 0),d.jsxDEV("button",{className:`px-4 py-4 text-center text-sm font-medium ${"conversion"===e?"border-b-2 border-blue-500 text-green-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>l("conversion"),children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV("i",{className:"fas fa-exchange-alt mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:179,columnNumber:19},void 0),d.jsxDEV("span",{children:"Conversión PD→PC"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:180,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:178,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:170,columnNumber:15},void 0),d.jsxDEV("button",{className:`px-4 py-4 text-center text-sm font-medium ${"supabase"===e?"border-b-2 border-blue-500 text-blue-600":"text-gray-500 hover:text-gray-700 hover:border-gray-300"} focus:outline-none transition-colors w-1/5`,onClick:()=>l("supabase"),children:d.jsxDEV("div",{className:"flex items-center justify-center",children:[d.jsxDEV(y,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:193,columnNumber:19},void 0),d.jsxDEV("span",{children:"Supabase"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:194,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:192,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:184,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:127,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:126,columnNumber:11},void 0),d.jsxDEV("div",{className:"p-6",children:(()=>{switch(e){case"institutions":return d.jsxDEV(q,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:85,columnNumber:16},void 0);case"psychologists":return d.jsxDEV(L,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:87,columnNumber:16},void 0);case"patients":return d.jsxDEV(z,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:89,columnNumber:16},void 0);case"supabase":return d.jsxDEV(R,{onConnectionChange:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:91,columnNumber:16},void 0);case"conversion":return d.jsxDEV(G,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:93,columnNumber:16},void 0);default:return d.jsxDEV(q,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:95,columnNumber:16},void 0)}})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:201,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:124,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:123,columnNumber:7},void 0),d.jsxDEV("footer",{className:"bg-white border-t border-gray-200 py-8",children:d.jsxDEV("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:d.jsxDEV("p",{className:"text-center text-gray-500 text-sm",children:["© ",(new Date).getFullYear()," Sistema de Gestión Psicológica - Panel de Administración"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:210,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:209,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:208,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/admin/Administration.jsx",lineNumber:114,columnNumber:5},void 0)}},Symbol.toStringTag,{value:"Module"}));export{H as A,I as B,w as C,$ as I,k as P,M as S,S as a,P as b,_ as c};
