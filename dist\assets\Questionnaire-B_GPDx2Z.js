var e=Object.defineProperty,s=Object.defineProperties,i=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,n=(s,i,a)=>i in s?e(s,i,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[i]=a,o=(e,s)=>{for(var i in s||(s={}))t.call(s,i)&&n(e,i,s[i]);if(a)for(var i of a(s))r.call(s,i)&&n(e,i,s[i]);return e},l=(e,a)=>s(e,i(a)),u=(e,s,i)=>new Promise((a,t)=>{var r=e=>{try{o(i.next(e))}catch(s){t(s)}},n=e=>{try{o(i.throw(e))}catch(s){t(s)}},o=e=>e.done?a(e.value):Promise.resolve(e.value).then(r,n);o((i=i.apply(e,s)).next())});import{j as m,K as c,C as d,Y as b,s as N,O as f,Z as p}from"./auth-Cw5QfmsP.js";import{r as x,u as g,d as h}from"./react-vendor-C9XH6RF0.js";import{Q as v}from"./ui-vendor-COFtXQcG.js";import{T as j}from"./TestCard-7H4NnEOB.js";import{P as V}from"./admin-COBm5LhQ.js";import{b as C,u as E}from"./index-OwywlZoY.js";const D=({completedTests:e=[],allTests:s=[],totalTime:i=0})=>{if(0===s.length)return m.jsxDEV("div",{className:"flex items-center justify-center h-64",children:m.jsxDEV("p",{className:"text-gray-500",children:"No hay tests disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:25,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:24,columnNumber:7},void 0);const a=[{codigo:"V",nombre:"Verbal",color:"#3B82F6"},{codigo:"E",nombre:"Espacial",color:"#6366F1"},{codigo:"A",nombre:"Atención",color:"#EF4444"},{codigo:"R",nombre:"Razonamiento",color:"#F59E0B"},{codigo:"N",nombre:"Numérica",color:"#14B8A6"},{codigo:"M",nombre:"Mecánica",color:"#64748B"},{codigo:"O",nombre:"Ortografía",color:"#10B981"}].map(i=>{const a=e.find(e=>{var s;return(null==(s=e.aptitudes)?void 0:s.codigo)===i.codigo}),t=s.find(e=>e.abbreviation===i.codigo);return l(o({},i),{completed:!!a,available:!!t,puntaje:(null==a?void 0:a.puntaje_directo)||0,fecha:(null==a?void 0:a.created_at)||null})}),t=({aptitud:e})=>{const s=e.completed?100:0,i=2*Math.PI*30,a=`${s/100*i} ${i}`;return m.jsxDEV("div",{className:"flex flex-col items-center flex-1 min-w-0",children:[m.jsxDEV("div",{className:"relative mb-3",children:[m.jsxDEV("svg",{viewBox:"0 0 80 80",className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28",children:[m.jsxDEV("circle",{cx:"40",cy:"40",r:"30",fill:"none",stroke:"#E5E7EB",strokeWidth:"6"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:70,columnNumber:13},void 0),e.available&&m.jsxDEV("circle",{cx:"40",cy:"40",r:"30",fill:"none",stroke:e.completed?e.color:"#E5E7EB",strokeWidth:"6",strokeDasharray:a,strokeDashoffset:"0",transform:"rotate(-90 40 40)",strokeLinecap:"round"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:80,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:68,columnNumber:11},void 0),m.jsxDEV("div",{className:"absolute inset-0 flex items-center justify-center",children:m.jsxDEV("span",{className:"text-sm sm:text-base font-bold",style:{color:e.color},children:e.codigo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:96,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:95,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:67,columnNumber:9},void 0),m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("div",{className:"text-xs sm:text-sm font-medium text-gray-700 mb-1",children:e.nombre},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:102,columnNumber:11},void 0),e.completed?m.jsxDEV("div",{className:"text-xs sm:text-sm text-green-600 font-semibold",children:["PD: ",e.puntaje]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:106,columnNumber:13},void 0):e.available?m.jsxDEV("div",{className:"text-xs sm:text-sm text-gray-500",children:"Pendiente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:110,columnNumber:13},void 0):m.jsxDEV("div",{className:"text-xs sm:text-sm text-gray-400",children:"No disponible"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:114,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:101,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:66,columnNumber:7},void 0)},r=a.filter(e=>e.completed).length,n=a.filter(e=>e.available).length;return m.jsxDEV("div",{className:"flex flex-col items-center",children:[m.jsxDEV("div",{className:"w-full max-w-6xl",children:m.jsxDEV("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-7 gap-2 sm:gap-4 mb-6",children:a.map((e,s)=>m.jsxDEV(t,{aptitud:e},s,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:132,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:130,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:129,columnNumber:7},void 0),m.jsxDEV("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-6 text-center",children:[m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV("div",{className:"w-4 h-4 bg-green-500 rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:140,columnNumber:11},void 0),m.jsxDEV("span",{className:"text-sm text-gray-700",children:["Completados: ",m.jsxDEV("span",{className:"font-semibold",children:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:142,columnNumber:26},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:141,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:139,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV("div",{className:"w-4 h-4 bg-gray-300 rounded-full"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:146,columnNumber:11},void 0),m.jsxDEV("span",{className:"text-sm text-gray-700",children:["Pendientes: ",m.jsxDEV("span",{className:"font-semibold",children:n-r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:148,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:147,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:145,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV("i",{className:"fas fa-clock text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:152,columnNumber:11},void 0),m.jsxDEV("span",{className:"text-sm text-gray-700",children:["Tiempo total: ",m.jsxDEV("span",{className:"font-semibold",children:(e=>{if(e<60)return`${e}s`;if(e<3600){const s=Math.floor(e/60),i=e%60;return i>0?`${s}m ${i}s`:`${s}m`}{const s=Math.floor(e/3600),i=Math.floor(e%3600/60);return i>0?`${s}h ${i}m`:`${s}h`}})(i)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:154,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:153,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:151,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:138,columnNumber:7},void 0),m.jsxDEV("div",{className:"mt-4 text-center",children:[m.jsxDEV("div",{className:"text-lg font-semibold text-gray-800",children:[r," de ",n," tests"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:161,columnNumber:9},void 0),m.jsxDEV("div",{className:"text-sm text-gray-500",children:n>0?`${(r/n*100).toFixed(0)}% completado`:"Sin progreso"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:164,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:160,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestProgressChart.jsx",lineNumber:127,columnNumber:5},void 0)},B=({results:e=[],completedTests:s=[],selectedLevel:i="E"})=>{var a,t;const[r,n]=x.useState(!1),[o,l]=x.useState(null),u=e.filter(e=>null!==e.respuestas_correctas&&null!==e.respuestas_incorrectas&&null!==e.respuestas_sin_contestar),c=(null==(a={E:[{id:"verbal",name:"Aptitud Verbal",code:"V"},{id:"espacial",name:"Aptitud Espacial",code:"E"},{id:"atencion",name:"Atención y Concentración",code:"A"},{id:"razonamiento",name:"Razonamiento",code:"R"},{id:"numerico",name:"Aptitud Numérica",code:"N"},{id:"mecanico",name:"Comprensión Mecánica",code:"M"},{id:"ortografia",name:"Ortografía",code:"O"}],B:[],S:[]}[i])?void 0:a.length)||0,d=s.length||0,b=c-d,N=c>0?Math.round(d/c*100):0;if(0===u.length)return m.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:[m.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[m.jsxDEV("i",{className:"fas fa-chart-pie mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:46,columnNumber:11},void 0),"Resultados de Tests Aplicados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:45,columnNumber:9},void 0),m.jsxDEV("div",{className:"text-center py-8",children:[m.jsxDEV("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:m.jsxDEV("i",{className:"fas fa-chart-pie text-2xl text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:51,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:50,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-500",children:"No hay resultados detallados disponibles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:53,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-sm text-gray-400 mt-1",children:"Los gráficos aparecerán cuando se completen tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:54,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:49,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:44,columnNumber:7},void 0);const f=e=>`${Math.floor(e/60)}:${(e%60).toString().padStart(2,"0")}`;return m.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:[m.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-6 flex items-center",children:[m.jsxDEV("i",{className:"fas fa-chart-pie mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:84,columnNumber:9},void 0),"Resultados de Tests Aplicados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:83,columnNumber:7},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u.map(e=>{var s,i,a;const t=e.total_preguntas||0,r=t>0?((e.respuestas_correctas||0)/t*100).toFixed(1):0;return m.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-3",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white mr-2",style:{backgroundColor:(o=null==(s=e.aptitudes)?void 0:s.codigo,{V:"#3B82F6",E:"#6366F1",A:"#EF4444",R:"#F59E0B",N:"#14B8A6",M:"#64748B",O:"#10B981"}[o]||"#6B7280")},children:(null==(i=e.aptitudes)?void 0:i.codigo)||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:98,columnNumber:19},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h4",{className:"text-sm font-semibold text-gray-800",children:(null==(a=e.aptitudes)?void 0:a.nombre)||"Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:105,columnNumber:21},void 0),m.jsxDEV("p",{className:"text-xs text-gray-500",children:new Date(e.created_at).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:108,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:104,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:97,columnNumber:17},void 0),m.jsxDEV("button",{onClick:()=>{l(e),n(!0)},className:"text-blue-600 hover:text-blue-800 text-sm font-medium",title:"Ver gráfico",children:[m.jsxDEV("i",{className:"fas fa-chart-pie mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:121,columnNumber:19},void 0),"Gráfico"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:113,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:96,columnNumber:15},void 0),m.jsxDEV("div",{className:"space-y-2",children:[m.jsxDEV("div",{className:"flex justify-between items-center p-2 bg-white rounded",children:[m.jsxDEV("span",{className:"text-sm text-gray-600",children:"Porcentaje de Aciertos:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:129,columnNumber:19},void 0),m.jsxDEV("span",{className:"text-lg font-bold text-green-600",children:[r,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:130,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:128,columnNumber:17},void 0),m.jsxDEV("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[m.jsxDEV("div",{className:"flex justify-between",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Correctas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:135,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-medium text-green-600",children:e.respuestas_correctas||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:136,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:134,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Incorrectas:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:139,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-medium text-red-600",children:e.respuestas_incorrectas||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:140,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:138,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between",children:[m.jsxDEV("span",{className:"text-gray-600",children:"PD:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:143,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-medium text-blue-600",children:e.puntaje_directo||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:144,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:142,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between",children:[m.jsxDEV("span",{className:"text-gray-600",children:"PC:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:147,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-medium text-purple-600",children:e.percentil||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:148,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:146,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between col-span-2",children:[m.jsxDEV("span",{className:"text-gray-600",children:"Tiempo:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:151,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-medium text-gray-800",children:f(e.tiempo_segundos||0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:152,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:150,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:133,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:127,columnNumber:15},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:94,columnNumber:13},void 0);var o})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:88,columnNumber:7},void 0),m.jsxDEV("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[m.jsxDEV("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[m.jsxDEV("div",{className:"bg-blue-50 rounded-lg p-3",children:[m.jsxDEV("div",{className:"text-lg font-bold text-blue-600",children:d},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:165,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-xs text-gray-600",children:"Tests Completados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:166,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:164,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-orange-50 rounded-lg p-3",children:[m.jsxDEV("div",{className:"text-lg font-bold text-orange-600",children:b},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:170,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-xs text-gray-600",children:"Tests Pendientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:171,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:169,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-purple-50 rounded-lg p-3",children:[m.jsxDEV("div",{className:"text-lg font-bold text-purple-600",children:[N,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:175,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-xs text-gray-600",children:"Progreso Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:176,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:174,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-green-50 rounded-lg p-3",children:[m.jsxDEV("div",{className:"text-lg font-bold text-green-600",children:u.reduce((e,s)=>e+(s.respuestas_correctas||0),0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:180,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-xs text-gray-600",children:"Total Correctas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:183,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:179,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-3",children:[m.jsxDEV("div",{className:"text-lg font-bold text-gray-600",children:f(u.reduce((e,s)=>e+(s.tiempo_segundos||0),0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:187,columnNumber:13},void 0),m.jsxDEV("div",{className:"text-xs text-gray-600",children:"Tiempo Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:190,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:186,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:163,columnNumber:9},void 0),m.jsxDEV("div",{className:"mt-4",children:[m.jsxDEV("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[m.jsxDEV("span",{children:"Progreso de Evaluación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:197,columnNumber:13},void 0),m.jsxDEV("span",{children:[d," de ",c," tests"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:198,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:196,columnNumber:11},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-3",children:m.jsxDEV("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500",style:{width:`${N}%`}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:201,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:200,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:195,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:162,columnNumber:7},void 0),r&&o&&m.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:()=>n(!1),children:m.jsxDEV("div",{className:"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[m.jsxDEV("div",{className:"flex justify-between items-center mb-6",children:[m.jsxDEV("h3",{className:"text-xl font-semibold text-gray-800",children:["Resultados Detallados - ",(null==(t=o.aptitudes)?void 0:t.nombre)||"Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:214,columnNumber:15},void 0),m.jsxDEV("button",{onClick:()=>n(!1),className:"text-gray-400 hover:text-gray-600 text-2xl",children:m.jsxDEV("i",{className:"fas fa-times"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:221,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:217,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:213,columnNumber:13},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("h5",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:228,columnNumber:17},void 0),m.jsxDEV("div",{className:"flex justify-center mb-4",children:m.jsxDEV("div",{className:"w-48 h-48",children:m.jsxDEV(C,{data:[{name:"Correctas",value:o.respuestas_correctas||0,color:"#10B981"},{name:"Incorrectas",value:o.respuestas_incorrectas||0,color:"#EF4444"},{name:"Sin Responder",value:o.respuestas_sin_contestar||0,color:"#6B7280"}].filter(e=>e.value>0),width:192,height:192,centerText:`${((o.respuestas_correctas||0)/(o.total_preguntas||1)*100).toFixed(1)}%`,centerSubtext:"Aciertos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:231,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:230,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:229,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:227,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("h5",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Estadísticas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:260,columnNumber:17},void 0),m.jsxDEV("div",{className:"space-y-3",children:[m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-4 h-4 bg-green-500 rounded-full mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:264,columnNumber:23},void 0),m.jsxDEV("span",{className:"text-gray-700",children:"Respuestas correctas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:265,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:263,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-semibold text-gray-800",children:[o.respuestas_correctas||0," de ",o.total_preguntas||0]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:267,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:262,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-4 h-4 bg-red-500 rounded-full mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:272,columnNumber:23},void 0),m.jsxDEV("span",{className:"text-gray-700",children:"Respuestas incorrectas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:273,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:271,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-semibold text-gray-800",children:o.respuestas_incorrectas||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:275,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:270,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-white rounded-lg border",children:[m.jsxDEV("div",{className:"flex items-center",children:[m.jsxDEV("div",{className:"w-4 h-4 bg-gray-500 rounded-full mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:280,columnNumber:23},void 0),m.jsxDEV("span",{className:"text-gray-700",children:"Sin responder"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:281,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:279,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-semibold text-gray-800",children:o.respuestas_sin_contestar||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:283,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:278,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-blue-50 rounded-lg border border-blue-200",children:[m.jsxDEV("span",{className:"text-blue-700 font-medium",children:"Tiempo utilizado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:287,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-semibold text-blue-800",children:f(o.tiempo_segundos||0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:288,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:286,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-purple-50 rounded-lg border border-purple-200",children:[m.jsxDEV("span",{className:"text-purple-700 font-medium",children:"Puntaje Directo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:292,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-bold text-purple-800 text-lg",children:o.puntaje_directo||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:293,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:291,columnNumber:19},void 0),m.jsxDEV("div",{className:"flex justify-between items-center p-3 bg-indigo-50 rounded-lg border border-indigo-200",children:[m.jsxDEV("span",{className:"text-indigo-700 font-medium",children:"Percentil (PC)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:297,columnNumber:21},void 0),m.jsxDEV("span",{className:"font-bold text-indigo-800 text-lg",children:o.percentil||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:298,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:296,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:261,columnNumber:17},void 0),m.jsxDEV("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[m.jsxDEV("h6",{className:"text-sm font-semibold text-blue-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-lightbulb mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:305,columnNumber:21},void 0),"Recomendaciones"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:304,columnNumber:19},void 0),m.jsxDEV("ul",{className:"text-sm text-blue-700 space-y-1",children:[m.jsxDEV("li",{className:"flex items-start",children:[m.jsxDEV("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:310,columnNumber:23},void 0),"Continúa practicando ejercicios similares para mejorar tu desempeño"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:309,columnNumber:21},void 0),m.jsxDEV("li",{className:"flex items-start",children:[m.jsxDEV("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:314,columnNumber:23},void 0),"Revisa los conceptos básicos relacionados con este tipo de prueba"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:313,columnNumber:21},void 0),m.jsxDEV("li",{className:"flex items-start",children:[m.jsxDEV("i",{className:"fas fa-check text-green-600 mr-2 mt-0.5 text-xs"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:318,columnNumber:23},void 0),"Analiza las preguntas que te resultaron más difíciles para identificar áreas de mejora"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:317,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:308,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:303,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:259,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:225,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:212,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:211,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/charts/TestResultsCharts.jsx",lineNumber:82,columnNumber:5},void 0)},y=({className:e=""})=>{const{selectedPatient:s,isSessionActive:i,selectedLevel:a,completedTests:t,sessionDuration:r,hasActiveSession:n}=E();if(!n)return null;return m.jsxDEV("div",{className:`bg-blue-50 border border-blue-200 rounded-lg p-4 ${e}`,children:m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("div",{className:"flex items-center space-x-3",children:m.jsxDEV("div",{className:"flex items-center space-x-2",children:[m.jsxDEV(c,{className:"text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:34,columnNumber:13},void 0),m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"font-medium text-gray-900",children:[s.nombre," ",s.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:36,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-sm text-gray-600",children:["Nivel: ",{E:"Escolar",B:"Bachillerato",S:"Superior"}[a]||a]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:39,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:35,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:33,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:32,columnNumber:9},void 0),m.jsxDEV("div",{className:"flex items-center space-x-4",children:[m.jsxDEV("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[m.jsxDEV(d,{className:"text-gray-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:48,columnNumber:13},void 0),m.jsxDEV("span",{children:[r," min"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:49,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:47,columnNumber:11},void 0),m.jsxDEV("div",{className:"flex items-center space-x-1 text-sm text-green-600",children:[m.jsxDEV(b,{className:"text-green-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:53,columnNumber:13},void 0),m.jsxDEV("span",{children:[t.length," completados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:54,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:52,columnNumber:11},void 0),m.jsxDEV("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse",title:"Sesión activa"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:57,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:46,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:31,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/test/PatientSessionIndicator.jsx",lineNumber:30,columnNumber:5},void 0)};class Q{static startSession(e,s,i,a=null){return u(this,null,function*(){try{const t={paciente_id:e,test_id:s,usuario_id:null==i?void 0:i.id,aptitud_id:a,fecha_inicio:(new Date).toISOString(),estado:"iniciado",ip_address:yield this.getClientIP(),user_agent:navigator.userAgent},{data:r,error:n}=yield N.from("test_sessions").insert(t).select().single();if(n){if(406===n.status)return{id:`mock-session-${Date.now()}`,paciente_id:e,test_id:s,estado:"iniciado",fecha_inicio:(new Date).toISOString()};throw n}return r}catch(t){if(406===t.status)return{id:`mock-session-${Date.now()}`,paciente_id:e,test_id:s,estado:"iniciado",fecha_inicio:(new Date).toISOString()};throw t}})}static finishSession(e,s,i=null){return u(this,null,function*(){try{if(e&&e.startsWith("mock-session-"))return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};const s={fecha_fin:(new Date).toISOString(),estado:"finalizado",updated_at:(new Date).toISOString()};i&&(s.resultados=i);const{data:a,error:t}=yield N.from("test_sessions").update(s).eq("id",e).select().single();if(t){if(406===t.status)return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};throw t}return a}catch(s){if(406===s.status)return{id:e,estado:"finalizado",fecha_fin:(new Date).toISOString()};throw s}})}static cancelSession(e,s,i="Cancelado por usuario"){return u(this,null,function*(){try{const{data:s,error:a}=yield N.from("test_sessions").update({fecha_fin:(new Date).toISOString(),estado:"cancelado",updated_at:(new Date).toISOString(),resultados:{cancelled:!0,reason:i}}).eq("id",e).select().single();if(a)throw a;return s}catch(s){throw s}})}static getActiveSession(e){return u(this,null,function*(){try{const{data:s,error:i}=yield N.from("test_sessions").select("*").eq("paciente_id",e).eq("estado","iniciado").order("fecha_inicio",{ascending:!1}).limit(1).single();if(i){if("PGRST116"===i.code||406===i.status)return null;throw i}return s||null}catch(s){return s.status,null}})}static getPatientSessions(e,s=10){return u(this,null,function*(){try{const{data:i,error:a}=yield N.from("test_sessions").select("\n          *,\n          aptitudes (\n            codigo,\n            nombre\n          )\n        ").eq("paciente_id",e).order("fecha_inicio",{ascending:!1}).limit(s);if(a)throw a;return i||[]}catch(i){throw i}})}static getSessionsPendingPinConsumption(e){return u(this,null,function*(){try{const{data:s,error:i}=yield N.from("test_sessions").select("*").eq("paciente_id",e).eq("estado","finalizado").is("pin_consumed_at",null).order("fecha_fin",{ascending:!1});if(i)throw i;return s||[]}catch(s){throw s}})}static markSessionPinConsumed(e){return u(this,null,function*(){try{const{data:s,error:i}=yield N.from("test_sessions").update({pin_consumed_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",e).select().single();if(i)throw i;return s}catch(s){throw s}})}static getSessionStats(){return u(this,arguments,function*(e={}){try{let s=N.from("test_sessions").select("*");e.pacienteId&&(s=s.eq("paciente_id",e.pacienteId)),e.estado&&(s=s.eq("estado",e.estado)),e.fechaDesde&&(s=s.gte("fecha_inicio",e.fechaDesde)),e.fechaHasta&&(s=s.lte("fecha_inicio",e.fechaHasta));const{data:i,error:a}=yield s;if(a)throw a;return{total:i.length,iniciadas:i.filter(e=>"iniciado"===e.estado).length,finalizadas:i.filter(e=>"finalizado"===e.estado).length,canceladas:i.filter(e=>"cancelado"===e.estado).length,pendientesPinConsumo:i.filter(e=>"finalizado"===e.estado&&!e.pin_consumed_at).length,conPinConsumido:i.filter(e=>e.pin_consumed_at).length}}catch(s){throw s}})}static getClientIP(){return u(this,null,function*(){try{return"127.0.0.1"}catch(e){return"unknown"}})}static cleanupOldSessions(e=30){return u(this,null,function*(){try{const s=new Date;s.setDate(s.getDate()-e);const{data:i,error:a}=yield N.from("test_sessions").delete().lt("fecha_inicio",s.toISOString()).eq("estado","cancelado").select();if(a)throw a;return(null==i?void 0:i.length)||0}catch(s){throw s}})}}const w={verbal:"V",espacial:"E",atencion:"A",razonamiento:"R",numerico:"N",mecanico:"M",ortografia:"O"},T=()=>{const e=g(),s=h(),{user:i}=f(),{selectedPatient:a,selectedLevel:t,isSessionActive:r,completedTests:n,startPatientSession:c,endPatientSession:d,markTestCompleted:b,isTestCompleted:C,updateSelectedLevel:T,hasActiveSession:R}=E(),[S,_]=x.useState([]),[P,k]=x.useState(""),[$,I]=x.useState(!1),[z,A]=x.useState([]),[O,q]=x.useState(!1),[M,L]=x.useState(null),[F,G]=x.useState(null),[Y,H]=x.useState(!1),[W,K]=x.useState(null),[U,Z]=x.useState(!1),[J,X]=x.useState({genero:"",nivel_educativo:"",edad_min:"",edad_max:""});x.useEffect(()=>{ee()},[]),x.useEffect(()=>{a&&se(a.id)},[a]),x.useEffect(()=>{z&&0!==z.length&&z.forEach(e=>{var s;if(null==(s=e.aptitudes)?void 0:s.codigo){const s=Object.keys(w).find(s=>w[s]===e.aptitudes.codigo);s&&b(s)}})},[z]),x.useEffect(()=>{var s;if((null==(s=e.state)?void 0:s.selectedPatient)&&S.length>0){const s=e.state.selectedPatient;if(s.id){const e=S.find(e=>e.id===s.id);e&&setSelectedPatient(e)}}},[e.state,S]);const ee=()=>u(null,null,function*(){try{I(!0);const{data:e,error:s}=yield N.from("pacientes").select("\n          id,\n          nombre,\n          apellido,\n          documento,\n          email,\n          genero,\n          fecha_nacimiento,\n          nivel_educativo,\n          ocupacion\n        ").order("nombre",{ascending:!0});if(s)throw s;_(e||[])}catch(e){v.error("Error al cargar la lista de pacientes")}finally{I(!1)}}),se=e=>u(null,null,function*(){try{q(!0);const{data:s,error:i}=yield N.from("resultados").select("\n          *,\n          aptitudes:aptitud_id (\n            codigo,\n            nombre,\n            descripcion\n          )\n        ").eq("paciente_id",e).order("created_at",{ascending:!1});if(i)throw i;A(s||[])}catch(s){v.error("Error al cargar los resultados del paciente")}finally{q(!1)}}),ie=e=>{if(!e)return null;const s=new Date,i=new Date(e);let a=s.getFullYear()-i.getFullYear();const t=s.getMonth()-i.getMonth();return(t<0||0===t&&s.getDate()<i.getDate())&&a--,a},ae=S.filter(e=>{const s=!P||e.nombre.toLowerCase().includes(P.toLowerCase())||e.apellido.toLowerCase().includes(P.toLowerCase())||e.documento&&e.documento.toLowerCase().includes(P.toLowerCase())||e.email&&e.email.toLowerCase().includes(P.toLowerCase()),i=!J.genero||e.genero===J.genero,a=!J.nivel_educativo||e.nivel_educativo===J.nivel_educativo,t=ie(e.fecha_nacimiento),r=!J.edad_min||null!==t&&t>=parseInt(J.edad_min),n=!J.edad_max||null!==t&&t<=parseInt(J.edad_max);return s&&i&&a&&r&&n}),te=()=>{setSelectedPatient(null),k(""),A([])},re=(e,s)=>{X(i=>l(o({},i),{[e]:s}))},ne=()=>{X({genero:"",nivel_educativo:"",edad_min:"",edad_max:""}),k("")},oe="estudiante"===(null==i?void 0:i.tipo_usuario)||"estudiante"===(null==i?void 0:i.rol);x.useEffect(()=>{a&&le()},[a]);const le=()=>u(null,null,function*(){try{const e=yield Q.getActiveSession(a.id);L(e),G(null==e?void 0:e.id)}catch(e){}}),ue={E:{code:"E",name:"Nivel E (Escolar)",subtitle:"Estudiantes Básicos",description:"Tests diseñados para estudiantes de educación básica y media",icon:"fas fa-graduation-cap",color:"green",bgClass:"bg-green-50",borderClass:"border-green-200",textClass:"text-green-700",iconBg:"bg-green-100",available:!0},M:{code:"M",name:"Nivel M (Media)",subtitle:"Media Vocacional",description:"Tests para estudiantes de educación media vocacional y técnica",icon:"fas fa-tools",color:"blue",bgClass:"bg-blue-50",borderClass:"border-blue-200",textClass:"text-blue-700",iconBg:"bg-blue-100",available:!1},S:{code:"S",name:"Nivel S (Superior)",subtitle:"Selección Laboral",description:"Tests para selección de personal y evaluación profesional",icon:"fas fa-briefcase",color:"purple",bgClass:"bg-purple-50",borderClass:"border-purple-200",textClass:"text-purple-700",iconBg:"bg-purple-100",available:!1}},me={E:[{id:"verbal",title:"Aptitud Verbal",description:"Evaluación de analogías verbales y comprensión de relaciones entre conceptos",time:12,questions:32,iconClass:"fas fa-comments",bgClass:"bg-blue-100",textClass:"text-blue-600",buttonColor:"blue",abbreviation:"V"},{id:"espacial",title:"Aptitud Espacial",description:"Razonamiento espacial con cubos y redes geométricas",time:15,questions:28,iconClass:"fas fa-cube",bgClass:"bg-indigo-100",textClass:"text-indigo-600",buttonColor:"indigo",abbreviation:"E"},{id:"atencion",title:"Atención",description:"Rapidez y precisión en la localización de símbolos específicos",time:8,questions:80,iconClass:"fas fa-eye",bgClass:"bg-red-100",textClass:"text-red-600",buttonColor:"red",abbreviation:"A"},{id:"razonamiento",title:"Razonamiento",description:"Continuar series lógicas de figuras y patrones",time:20,questions:32,iconClass:"fas fa-puzzle-piece",bgClass:"bg-amber-100",textClass:"text-amber-600",buttonColor:"amber",abbreviation:"R"},{id:"numerico",title:"Aptitud Numérica",description:"Resolución de igualdades, series numéricas y análisis de datos",time:20,questions:30,iconClass:"fas fa-calculator",bgClass:"bg-teal-100",textClass:"text-teal-600",buttonColor:"teal",abbreviation:"N"},{id:"mecanico",title:"Aptitud Mecánica",description:"Comprensión de principios físicos y mecánicos básicos",time:12,questions:28,iconClass:"fas fa-cogs",bgClass:"bg-slate-100",textClass:"text-slate-600",buttonColor:"slate",abbreviation:"M"},{id:"ortografia",title:"Ortografía",description:"Identificación de palabras con errores ortográficos",time:10,questions:32,iconClass:"fas fa-spell-check",bgClass:"bg-green-100",textClass:"text-green-600",buttonColor:"green",abbreviation:"O"}],M:[],S:[]};return m.jsxDEV("div",{children:[m.jsxDEV(V,{title:m.jsxDEV("span",{children:[m.jsxDEV("span",{className:"text-red-600",children:"BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:713,columnNumber:13},void 0)," ",m.jsxDEV("span",{className:"text-blue-600",children:"Batería de Aptitudes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:714,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:712,columnNumber:11},void 0),subtitle:"Selecciona un paciente para ver sus resultados y aplicar nuevos tests",icon:p},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:710,columnNumber:7},void 0),m.jsxDEV("div",{className:"container mx-auto px-4 py-8",children:[m.jsxDEV(y,{className:"mb-6"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:724,columnNumber:9},void 0),m.jsxDEV("div",{className:"mb-8",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[m.jsxDEV("i",{className:"fas fa-layer-group mr-2 text-indigo-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:730,columnNumber:13},void 0),"Seleccionar Nivel de Evaluación"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:729,columnNumber:11},void 0),m.jsxDEV("div",{className:"w-24 h-1 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:733,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-4",children:"Elige el nivel educativo apropiado para la evaluación del paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:734,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:728,columnNumber:9},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto",children:Object.values(ue).map(e=>m.jsxDEV("div",{onClick:()=>e.available&&T(e.code),className:"relative p-6 rounded-xl border-2 transition-all duration-300 cursor-pointer transform hover:scale-105 "+(t===e.code?`${e.borderClass} ${e.bgClass} shadow-lg ring-2 ring-${e.color}-300`:e.available?`border-gray-200 bg-white hover:${e.bgClass} hover:${e.borderClass} shadow-md`:"border-gray-200 bg-gray-50 cursor-not-allowed opacity-60"),children:[m.jsxDEV("div",{className:"absolute top-3 right-3",children:e.available?m.jsxDEV("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[m.jsxDEV("i",{className:"fas fa-check-circle mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:756,columnNumber:21},void 0),"Disponible"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:755,columnNumber:19},void 0):m.jsxDEV("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:[m.jsxDEV("i",{className:"fas fa-clock mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:761,columnNumber:21},void 0),"En desarrollo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:760,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:753,columnNumber:15},void 0),m.jsxDEV("div",{className:`inline-flex items-center justify-center w-16 h-16 ${e.iconBg} rounded-full mb-4`,children:m.jsxDEV("i",{className:`${e.icon} text-2xl ${e.textClass}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:769,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:768,columnNumber:15},void 0),m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("h3",{className:`text-lg font-bold mb-1 ${t===e.code?e.textClass:"text-gray-900"}`,children:["📗 ",e.name]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:774,columnNumber:17},void 0),m.jsxDEV("p",{className:`text-sm font-medium mb-2 ${t===e.code?e.textClass:"text-gray-600"}`,children:e.subtitle},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:777,columnNumber:17},void 0),m.jsxDEV("p",{className:`text-sm ${t===e.code?e.textClass:"text-gray-500"}`,children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:780,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:773,columnNumber:15},void 0),t===e.code&&m.jsxDEV("div",{className:"absolute inset-0 rounded-xl border-2 border-transparent",children:m.jsxDEV("div",{className:`absolute top-2 left-2 w-6 h-6 ${e.iconBg} rounded-full flex items-center justify-center`,children:m.jsxDEV("i",{className:`fas fa-check text-sm ${e.textClass}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:789,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:788,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:787,columnNumber:17},void 0),!e.available&&m.jsxDEV("div",{className:"mt-4 p-3 bg-gray-100 rounded-lg",children:m.jsxDEV("p",{className:"text-xs text-gray-600 text-center",children:[m.jsxDEV("i",{className:"fas fa-info-circle mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:798,columnNumber:21},void 0),"Este nivel estará disponible próximamente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:797,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:796,columnNumber:17},void 0)]},e.code,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:741,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:739,columnNumber:9},void 0),t&&m.jsxDEV("div",{className:"mt-6 max-w-3xl mx-auto",children:m.jsxDEV("div",{className:`p-4 rounded-lg ${ue[t].bgClass} ${ue[t].borderClass} border`,children:m.jsxDEV("div",{className:"flex items-center justify-center",children:[m.jsxDEV("div",{className:`w-8 h-8 ${ue[t].iconBg} rounded-full flex items-center justify-center mr-3`,children:m.jsxDEV("i",{className:`${ue[t].icon} ${ue[t].textClass}`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:813,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:812,columnNumber:17},void 0),m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("p",{className:`font-medium ${ue[t].textClass}`,children:["Nivel seleccionado: ",ue[t].name]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:816,columnNumber:19},void 0),m.jsxDEV("p",{className:`text-sm ${ue[t].textClass} opacity-80`,children:[me[t].length," tests disponibles para este nivel"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:819,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:815,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:811,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:810,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:809,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:727,columnNumber:7},void 0),m.jsxDEV("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8",children:[m.jsxDEV("div",{className:"flex items-center justify-between mb-6",children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-800",children:[m.jsxDEV("i",{className:"fas fa-search mr-3 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:833,columnNumber:13},void 0),"Buscar Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:832,columnNumber:11},void 0),m.jsxDEV("button",{onClick:()=>Z(!U),className:"flex items-center px-4 py-2 rounded-lg transition-all duration-200 "+(U?"bg-blue-600 text-white shadow-md":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:[m.jsxDEV("i",{className:"fas fa-filter mr-2 "+(U?"text-white":"text-gray-500")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:844,columnNumber:13},void 0),U?"Ocultar Filtros":"Filtros Avanzados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:836,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:831,columnNumber:9},void 0),m.jsxDEV("div",{className:"relative mb-4",children:[m.jsxDEV("input",{type:"text",placeholder:"Buscar por nombre, apellido, documento o email...",value:P,onChange:e=>k(e.target.value),className:"w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-700 placeholder-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:851,columnNumber:11},void 0),m.jsxDEV("i",{className:"fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:858,columnNumber:11},void 0),(P||Object.values(J).some(e=>e))&&m.jsxDEV("button",{onClick:()=>{te(),ne()},className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500 transition-colors duration-200",title:"Limpiar búsqueda y filtros",children:m.jsxDEV("i",{className:"fas fa-times"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:869,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:861,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:850,columnNumber:9},void 0),U&&m.jsxDEV("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-4 border border-blue-100",children:[m.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:[m.jsxDEV("i",{className:"fas fa-sliders-h mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:878,columnNumber:15},void 0),"Filtros Avanzados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:877,columnNumber:13},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[m.jsxDEV("div",{children:[m.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[m.jsxDEV("i",{className:"fas fa-venus-mars mr-1 text-pink-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:886,columnNumber:19},void 0),"Género"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:885,columnNumber:17},void 0),m.jsxDEV("select",{value:J.genero,onChange:e=>re("genero",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsxDEV("option",{value:"",children:"Todos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:894,columnNumber:19},void 0),m.jsxDEV("option",{value:"masculino",children:"Masculino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:895,columnNumber:19},void 0),m.jsxDEV("option",{value:"femenino",children:"Femenino"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:896,columnNumber:19},void 0),m.jsxDEV("option",{value:"otro",children:"Otro"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:897,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:889,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:884,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[m.jsxDEV("i",{className:"fas fa-graduation-cap mr-1 text-green-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:904,columnNumber:19},void 0),"Nivel Educativo"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:903,columnNumber:17},void 0),m.jsxDEV("select",{value:J.nivel_educativo,onChange:e=>re("nivel_educativo",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[m.jsxDEV("option",{value:"",children:"Todos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:912,columnNumber:19},void 0),m.jsxDEV("option",{value:"primaria",children:"Primaria"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:913,columnNumber:19},void 0),m.jsxDEV("option",{value:"secundaria",children:"Secundaria"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:914,columnNumber:19},void 0),m.jsxDEV("option",{value:"bachillerato",children:"Bachillerato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:915,columnNumber:19},void 0),m.jsxDEV("option",{value:"tecnico",children:"Técnico"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:916,columnNumber:19},void 0),m.jsxDEV("option",{value:"universitario",children:"Universitario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:917,columnNumber:19},void 0),m.jsxDEV("option",{value:"posgrado",children:"Posgrado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:918,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:907,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:902,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[m.jsxDEV("i",{className:"fas fa-calendar-alt mr-1 text-orange-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:925,columnNumber:19},void 0),"Edad Mínima"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:924,columnNumber:17},void 0),m.jsxDEV("input",{type:"number",placeholder:"Ej: 18",value:J.edad_min,onChange:e=>re("edad_min",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"120"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:928,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:923,columnNumber:15},void 0),m.jsxDEV("div",{children:[m.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[m.jsxDEV("i",{className:"fas fa-calendar-check mr-1 text-purple-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:942,columnNumber:19},void 0),"Edad Máxima"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:941,columnNumber:17},void 0),m.jsxDEV("input",{type:"number",placeholder:"Ej: 65",value:J.edad_max,onChange:e=>re("edad_max",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",min:"0",max:"120"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:945,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:940,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:882,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex justify-end mt-4",children:m.jsxDEV("button",{onClick:ne,className:"flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200",children:[m.jsxDEV("i",{className:"fas fa-eraser mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:963,columnNumber:17},void 0),"Limpiar Filtros"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:959,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:958,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:876,columnNumber:11},void 0),(P||Object.values(J).some(e=>e))&&m.jsxDEV("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:m.jsxDEV("p",{className:"text-sm text-blue-700",children:[m.jsxDEV("i",{className:"fas fa-info-circle mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:974,columnNumber:15},void 0),"Se encontraron ",m.jsxDEV("span",{className:"font-semibold",children:ae.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:975,columnNumber:30},void 0)," paciente(s) que coinciden con los criterios de búsqueda"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:973,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:972,columnNumber:11},void 0),P&&!a&&m.jsxDEV("div",{className:"mt-4 max-h-60 overflow-y-auto border border-gray-200 rounded-lg",children:$?m.jsxDEV("div",{className:"p-4 text-center",children:[m.jsxDEV("i",{className:"fas fa-spinner fa-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:985,columnNumber:17},void 0),"Cargando pacientes..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:984,columnNumber:15},void 0):ae.length>0?ae.map(e=>m.jsxDEV("div",{onClick:()=>(e=>{c(e,t),k(`${e.nombre} ${e.apellido}`),v.success(`Paciente ${e.nombre} ${e.apellido} seleccionado para evaluación`)})(e),className:"p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("div",{children:[m.jsxDEV("p",{className:"font-medium text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:997,columnNumber:23},void 0),m.jsxDEV("p",{className:"text-sm text-gray-500",children:[e.documento&&`Doc: ${e.documento}`,e.email&&` • ${e.email}`]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1e3,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:996,columnNumber:21},void 0),m.jsxDEV("i",{className:"fas fa-chevron-right text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1005,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:995,columnNumber:19},void 0)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:990,columnNumber:17},void 0)):m.jsxDEV("div",{className:"p-4 text-center text-gray-500",children:"No se encontraron pacientes que coincidan con la búsqueda"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1010,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:982,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:830,columnNumber:7},void 0),a&&m.jsxDEV("div",{className:"mb-8",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[m.jsxDEV("i",{className:"fas fa-user-check mr-2 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1023,columnNumber:15},void 0),"Paciente Seleccionado"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1022,columnNumber:13},void 0),m.jsxDEV("div",{className:"w-24 h-1 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1026,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1021,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-gradient-to-br from-white to-blue-50 rounded-2xl shadow-xl border border-blue-100 overflow-hidden max-w-5xl mx-auto",children:[m.jsxDEV("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4",children:m.jsxDEV("div",{className:"flex items-center justify-between",children:[m.jsxDEV("h3",{className:"text-xl font-bold text-white flex items-center",children:[m.jsxDEV("div",{className:"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-user text-white"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1035,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1034,columnNumber:19},void 0),"Información del Paciente"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1033,columnNumber:17},void 0),m.jsxDEV("button",{onClick:te,className:"text-white hover:text-red-200 transition-colors duration-200 p-2 rounded-full hover:bg-white hover:bg-opacity-10",title:"Deseleccionar paciente",children:m.jsxDEV("i",{className:"fas fa-times"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1044,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1039,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1032,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1031,columnNumber:13},void 0),m.jsxDEV("div",{className:"p-6",children:[m.jsxDEV("div",{className:"flex items-start space-x-6 mb-6",children:[m.jsxDEV("div",{className:"flex-shrink-0",children:m.jsxDEV("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg",children:m.jsxDEV("span",{className:"text-2xl font-bold text-white",children:[a.nombre.charAt(0),a.apellido.charAt(0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1055,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1054,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1053,columnNumber:17},void 0),m.jsxDEV("div",{className:"flex-1",children:[m.jsxDEV("h4",{className:"text-2xl font-bold text-gray-800 mb-1",children:[a.nombre," ",a.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1061,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 flex items-center",children:[m.jsxDEV("i",{className:"fas fa-id-card mr-2 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1065,columnNumber:21},void 0),a.documento]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1064,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-gray-600 flex items-center mt-1",children:[m.jsxDEV("i",{className:"fas fa-envelope mr-2 text-green-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1069,columnNumber:21},void 0),a.email]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1068,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1060,columnNumber:17},void 0),m.jsxDEV("div",{className:"text-right",children:m.jsxDEV("div",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[m.jsxDEV("i",{className:"fas fa-check-circle mr-1"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1075,columnNumber:21},void 0),"Seleccionado"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1074,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1073,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1052,columnNumber:15},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[m.jsxDEV("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[m.jsxDEV("div",{className:"flex items-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-venus-mars text-pink-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1087,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1086,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-500",children:"Género"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1089,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1085,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-lg font-semibold text-gray-800 capitalize",children:a.genero},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1091,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1084,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[m.jsxDEV("div",{className:"flex items-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-birthday-cake text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1100,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1099,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-500",children:"Edad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1102,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1098,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-lg font-semibold text-gray-800",children:[ie(a.fecha_nacimiento)," años"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1104,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-xs text-gray-500 mt-1",children:new Date(a.fecha_nacimiento).toLocaleDateString("es-ES")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1107,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1097,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100",children:[m.jsxDEV("div",{className:"flex items-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-graduation-cap text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1116,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1115,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-500",children:"Educación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1118,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1114,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-lg font-semibold text-gray-800 capitalize",children:a.nivel_educativo},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1120,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1113,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-xl p-4 shadow-sm border border-gray-100 md:col-span-2 lg:col-span-3",children:[m.jsxDEV("div",{className:"flex items-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3",children:m.jsxDEV("i",{className:"fas fa-briefcase text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1129,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1128,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-500",children:"Ocupación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1131,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1127,columnNumber:19},void 0),m.jsxDEV("p",{className:"text-lg font-semibold text-gray-800",children:a.ocupacion||"No especificada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1133,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1126,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1082,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1050,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1029,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1020,columnNumber:9},void 0),a&&m.jsxDEV("div",{className:"mb-8",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[m.jsxDEV("i",{className:"fas fa-chart-bar mr-2 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1150,columnNumber:15},void 0),"Resultados de Tests Aplicados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1149,columnNumber:13},void 0),m.jsxDEV("div",{className:"w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1153,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1148,columnNumber:11},void 0),m.jsxDEV("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 max-w-6xl mx-auto overflow-hidden",children:O?m.jsxDEV("div",{className:"text-center py-12",children:[m.jsxDEV("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4",children:m.jsxDEV("i",{className:"fas fa-spinner fa-spin text-purple-600 text-xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1161,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1160,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 font-medium",children:"Cargando resultados..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1163,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1159,columnNumber:15},void 0):z.length>0?m.jsxDEV("div",{className:"p-6",children:m.jsxDEV("div",{className:"mb-8",children:m.jsxDEV("div",{className:"bg-gradient-to-br from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200",children:[m.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-4 text-center",children:[m.jsxDEV("i",{className:"fas fa-chart-pie mr-2 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1171,columnNumber:23},void 0),"Progreso de Evaluación"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1170,columnNumber:21},void 0),m.jsxDEV(D,{completedTests:z,allTests:me[t]||[],totalTime:z.reduce((e,s)=>e+(s.tiempo_segundos||0),0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1174,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1169,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1168,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1166,columnNumber:15},void 0):m.jsxDEV("div",{className:"text-center py-12",children:[m.jsxDEV("div",{className:"inline-flex items-center justify-center w-20 h-20 bg-gray-100 rounded-full mb-4",children:m.jsxDEV("i",{className:"fas fa-clipboard-check text-3xl text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1191,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1190,columnNumber:17},void 0),m.jsxDEV("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Sin resultados registrados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1193,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-500 mb-4",children:"Este paciente no tiene resultados de tests registrados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1194,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-sm text-gray-400",children:"Aplica tests usando las opciones de abajo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1195,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1189,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1156,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1147,columnNumber:9},void 0),m.jsxDEV("div",{className:"mb-8",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:[m.jsxDEV("i",{className:"fas fa-clipboard-list mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1206,columnNumber:13},void 0),"Tests Disponibles - ",ue[t].name]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1205,columnNumber:11},void 0),m.jsxDEV("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1209,columnNumber:11},void 0),m.jsxDEV("p",{className:"text-gray-600 mt-4",children:ue[t].description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1210,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1204,columnNumber:9},void 0),a&&ue[t].available&&(()=>{const e=(()=>{var e;const s=(null==(e=me[t])?void 0:e.length)||0,i=n.length||0;return{total:s,completed:i,pending:s-i,percentage:s>0?Math.round(i/s*100):0}})();return m.jsxDEV("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 mb-8 border border-blue-200 max-w-5xl mx-auto",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:[m.jsxDEV("i",{className:"fas fa-chart-pie mr-2 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1222,columnNumber:19},void 0),"Progreso de Evaluación"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1221,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-blue-700 font-medium",children:(s=e.percentage,0===s?"¡Comienza tu evaluación! Selecciona un test para empezar.":s<25?"¡Buen comienzo! Continúa con los siguientes tests.":s<50?"¡Vas por buen camino! Ya completaste una cuarta parte.":s<75?"¡Excelente progreso! Estás a mitad de camino.":s<100?"¡Casi terminas! Solo faltan algunos tests más.":"¡Felicitaciones! Has completado todos los tests disponibles.")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1225,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1220,columnNumber:15},void 0),m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[m.jsxDEV("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-green-200",children:[m.jsxDEV("div",{className:"flex items-center justify-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2",children:m.jsxDEV("i",{className:"fas fa-check text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1235,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1234,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-600",children:"Completados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1237,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1233,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:e.completed},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1239,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1232,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-orange-200",children:[m.jsxDEV("div",{className:"flex items-center justify-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-2",children:m.jsxDEV("i",{className:"fas fa-clock text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1245,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1244,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-600",children:"Pendientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1247,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1243,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-2xl font-bold text-orange-600",children:e.pending},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1249,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1242,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-lg p-4 text-center shadow-sm border border-blue-200",children:[m.jsxDEV("div",{className:"flex items-center justify-center mb-2",children:[m.jsxDEV("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2",children:m.jsxDEV("i",{className:"fas fa-list text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1255,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1254,columnNumber:21},void 0),m.jsxDEV("span",{className:"text-sm font-medium text-gray-600",children:"Total"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1257,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1253,columnNumber:19},void 0),m.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:e.total},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1259,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1252,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1231,columnNumber:15},void 0),m.jsxDEV("div",{className:"mb-4",children:[m.jsxDEV("div",{className:"flex justify-between items-center mb-2",children:[m.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:"Progreso General"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1266,columnNumber:19},void 0),m.jsxDEV("span",{className:"text-sm font-bold text-gray-800",children:[e.percentage,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1267,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1265,columnNumber:17},void 0),m.jsxDEV("div",{className:"w-full bg-gray-200 rounded-full h-3",children:m.jsxDEV("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out",style:{width:`${e.percentage}%`}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1270,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1269,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1264,columnNumber:15},void 0),m.jsxDEV("div",{className:"text-center",children:m.jsxDEV("div",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium "+(100===e.percentage?"bg-green-100 text-green-800 border border-green-200":e.percentage>0?"bg-blue-100 text-blue-800 border border-blue-200":"bg-gray-100 text-gray-800 border border-gray-200"),children:[m.jsxDEV("i",{className:"mr-2 "+(100===e.percentage?"fas fa-trophy":e.percentage>0?"fas fa-play-circle":"fas fa-info-circle")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1286,columnNumber:19},void 0),100===e.percentage?"¡Evaluación Completa!":e.percentage>0?`${e.completed} de ${e.total} tests completados`:"Listo para comenzar"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1279,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1278,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1219,columnNumber:13},void 0);var s})(),!a&&m.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 max-w-4xl mx-auto",children:m.jsxDEV("div",{className:"flex items-center justify-center",children:[m.jsxDEV("i",{className:"fas fa-info-circle text-yellow-600 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1308,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-yellow-800",children:"Selecciona un paciente para poder aplicar los tests y guardar los resultados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1309,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1307,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1306,columnNumber:11},void 0),!ue[t].available&&m.jsxDEV("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6 max-w-4xl mx-auto",children:m.jsxDEV("div",{className:"text-center",children:[m.jsxDEV("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4",children:m.jsxDEV("i",{className:"fas fa-tools text-orange-600 text-2xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1320,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1319,columnNumber:15},void 0),m.jsxDEV("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Nivel en Desarrollo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1322,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-orange-700 mb-4",children:["Los tests para ",ue[t].name," están actualmente en desarrollo."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1325,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-sm text-orange-600",children:"Por favor, selecciona el Nivel E (Escolar) que está completamente disponible."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1328,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1318,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1317,columnNumber:11},void 0),ue[t].available&&m.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 auto-rows-fr max-w-7xl mx-auto",children:me[t].map(e=>m.jsxDEV(j,{test:e,iconClass:e.iconClass,bgClass:e.bgClass,textClass:e.textClass,buttonColor:e.buttonColor,abbreviation:e.abbreviation,showButton:!!a,disabled:!a,patientId:null==a?void 0:a.id,level:t,isCompleted:C(e.id),onRepeatTest:()=>(e=>{K(e),H(!0)})(e)},e.id,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1339,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1337,columnNumber:11},void 0),a&&z.length>0&&m.jsxDEV("div",{className:"mt-8",children:[m.jsxDEV(B,{results:z,completedTests:n,selectedLevel:t},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1361,columnNumber:13},void 0),m.jsxDEV("div",{className:"mt-8 text-center",children:m.jsxDEV("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-6 border border-red-200",children:[m.jsxDEV("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:[m.jsxDEV("i",{className:"fas fa-flag-checkered mr-2 text-red-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1371,columnNumber:19},void 0),"Finalizar Evaluación"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1370,columnNumber:17},void 0),m.jsxDEV("p",{className:"text-gray-600 mb-4",children:oe?"Cuando hayas completado todos los tests que necesites, puedes finalizar tu evaluación.":"Finaliza la evaluación del paciente cuando consideres que ha completado los tests necesarios."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1374,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-white rounded-lg p-4 mb-4 border border-gray-200",children:[m.jsxDEV("div",{className:"text-sm text-gray-600 mb-2",children:[m.jsxDEV("strong",{children:"Paciente:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1384,columnNumber:21},void 0)," ",a.nombre," ",a.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1383,columnNumber:19},void 0),a.documento&&m.jsxDEV("div",{className:"text-sm text-gray-600 mb-2",children:[m.jsxDEV("strong",{children:"Documento:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1388,columnNumber:23},void 0)," ",a.documento]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1387,columnNumber:21},void 0),M&&m.jsxDEV("div",{className:"text-sm text-gray-600",children:[m.jsxDEV("strong",{children:"Sesión activa:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1393,columnNumber:23},void 0)," ",new Date(M.fecha_inicio).toLocaleString()]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1392,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1382,columnNumber:17},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4",children:m.jsxDEV("p",{className:"text-yellow-800 text-sm",children:[m.jsxDEV("strong",{children:"Importante:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1401,columnNumber:21},void 0)," Al finalizar la evaluación, se cerrará la sesión actual. Asegúrate de que el paciente haya completado todos los tests necesarios."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1400,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1399,columnNumber:17},void 0),m.jsxDEV("button",{onClick:()=>u(null,null,function*(){if(!a)return;if(window.confirm("¿Estás seguro de que deseas terminar la evaluación completa? Esta acción cerrará la sesión actual."))try{const{error:e}=yield N.from("pacientes").update({evaluacion_finalizada:!0,fecha_finalizacion:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",a.id);if(e)throw e;M&&(yield Q.finishSession(M.id,i)),L(null),G(null),v.success("Evaluación finalizada correctamente. Ahora puedes generar el informe desde la sección de Resultados."),d(),k(""),A([]),oe&&s("/home")}catch(e){v.error("Error al finalizar la evaluación: "+e.message)}}),className:"px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium",children:[m.jsxDEV("i",{className:"fas fa-stop-circle mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1410,columnNumber:19},void 0),"Terminar Prueba"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1406,columnNumber:17},void 0),m.jsxDEV("div",{className:"mt-4 text-xs text-gray-500",children:m.jsxDEV("p",{children:["Después de finalizar, podrás generar informes desde la sección de Resultados.",!oe&&" También podrás seleccionar otro paciente para evaluar."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1416,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1415,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1369,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1368,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1360,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1203,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:721,columnNumber:7},void 0),Y&&W&&m.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:m.jsxDEV("div",{className:"bg-white rounded-xl p-6 max-w-md mx-4 shadow-2xl",children:[m.jsxDEV("div",{className:"text-center mb-6",children:[m.jsxDEV("div",{className:"w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4",children:m.jsxDEV("i",{className:"fas fa-exclamation-triangle text-orange-600 text-2xl"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1434,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1433,columnNumber:15},void 0),m.jsxDEV("h3",{className:"text-xl font-bold text-gray-800 mb-2",children:"Repetir Test"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1436,columnNumber:15},void 0),m.jsxDEV("p",{className:"text-gray-600",children:["¿Estás seguro de que deseas repetir el test de ",m.jsxDEV("strong",{children:W.title},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1440,columnNumber:64},void 0),"?"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1439,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1432,columnNumber:13},void 0),m.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:m.jsxDEV("div",{className:"flex items-start",children:[m.jsxDEV("i",{className:"fas fa-info-circle text-yellow-600 mr-2 mt-0.5"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1446,columnNumber:17},void 0),m.jsxDEV("div",{className:"text-sm text-yellow-800",children:[m.jsxDEV("p",{className:"font-medium mb-1",children:"Importante:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1448,columnNumber:19},void 0),m.jsxDEV("p",{children:"El resultado anterior será eliminado y sobrescrito con el nuevo resultado."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1449,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1447,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1445,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1444,columnNumber:13},void 0),m.jsxDEV("div",{className:"flex space-x-3",children:[m.jsxDEV("button",{onClick:()=>{H(!1),K(null)},className:"flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1455,columnNumber:15},void 0),m.jsxDEV("button",{onClick:()=>u(null,null,function*(){var e;if(W&&a)try{const i=yield(e=W.id,u(null,null,function*(){try{const s=w[e];if(!s)throw new Error(`Tipo de test no reconocido: ${e}`);const{data:i,error:a}=yield N.from("aptitudes").select("id").eq("codigo",s).single();if(a)throw a;return i.id}catch(t){throw t}})),{error:t}=yield N.from("resultados").delete().eq("paciente_id",a.id).eq("aptitud_id",i);if(t)throw t;yield se(a.id),v.success("Resultado anterior eliminado. Puedes realizar el test nuevamente."),s(`/test/instructions/${W.id}`,{state:{patientId:a.id}})}catch(i){v.error("Error al preparar la repetición del test")}finally{H(!1),K(null)}}),className:"flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium",children:[m.jsxDEV("i",{className:"fas fa-redo mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1468,columnNumber:17},void 0),"Repetir Test"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1464,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1454,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1431,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:1430,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/student/Questionnaire.jsx",lineNumber:708,columnNumber:5},void 0)};export{T as default};
