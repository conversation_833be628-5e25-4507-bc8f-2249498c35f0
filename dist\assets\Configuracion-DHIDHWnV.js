var e=Object.defineProperty,i=Object.defineProperties,a=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(i,a,n)=>a in i?e(i,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[a]=n,l=(e,i)=>{for(var a in i||(i={}))s.call(i,a)&&r(e,a,i[a]);if(n)for(var a of n(i))o.call(i,a)&&r(e,a,i[a]);return e},t=(e,n)=>i(e,a(n)),m=(e,i,a)=>new Promise((n,s)=>{var o=e=>{try{l(a.next(e))}catch(i){s(i)}},r=e=>{try{l(a.throw(e))}catch(i){s(i)}},l=e=>e.done?n(e.value):Promise.resolve(e.value).then(o,r);l((a=a.apply(e,i)).next())});import{i as c,J as d,_ as u,$ as b,j as p,L as g,a0 as N,a1 as x,r as f,c as v,d as h,Q as j,a2 as V,h as C,K as P,a3 as E,a4 as D,a as y,N as B,a5 as U,H as w,F as S,D as A,a6 as M,a7 as k,w as _,C as L,a8 as $,o as T,a9 as O,p as I,B as z,O as R,g as G}from"./auth-Cw5QfmsP.js";import{r as F}from"./react-vendor-C9XH6RF0.js";import{P as H}from"./admin-COBm5LhQ.js";import{Q as q}from"./ui-vendor-COFtXQcG.js";import{p as J,s as K}from"./PinManagementService-DCClnbJn.js";import{p as X,P as W}from"./ImprovedPinControlService-XBKYJtn2.js";import"./index-OwywlZoY.js";const Y=({title:e,value:i,icon:a,color:n,percentage:s,trend:o,subtitle:r})=>p.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:shadow-md transition-shadow",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{className:"flex-1",children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:12,columnNumber:11},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:i},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:13,columnNumber:11},void 0),r&&p.jsxDEV("p",{className:"text-xs text-gray-500 mt-1",children:r},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:15,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:11,columnNumber:9},void 0),p.jsxDEV("div",{className:"flex-shrink-0",children:p.jsxDEV("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-white",style:{backgroundColor:n},children:p.jsxDEV(a,{className:"w-6 h-6"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:23,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:19,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:18,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:10,columnNumber:7},void 0),s&&p.jsxDEV("div",{className:"mt-4 flex items-center",children:[p.jsxDEV("span",{className:"text-sm font-medium px-2 py-1 rounded "+("up"===o?"text-green-700 bg-green-100":"text-red-700 bg-red-100"),children:["up"===o?"+":"",s,"%"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:29,columnNumber:11},void 0),p.jsxDEV("span",{className:"text-sm text-gray-500 ml-2",children:r||"vs sem. anterior"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:36,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:28,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:9,columnNumber:5},void 0),Z=()=>{const[e,i]=F.useState({totalUsers:20,todayActions:6,newUsers:0,completedTests:30}),[a,n]=F.useState(!1),s=[{title:"Usuarios Totales",value:e.totalUsers,icon:c,color:"#3B82F6",percentage:12,trend:"up",subtitle:"vs ayer"},{title:"Acciones Hoy",value:e.todayActions,icon:d,color:"#10B981",percentage:12,trend:"up",subtitle:"vs ayer"},{title:"Nuevos (7 días)",value:e.newUsers,icon:u,color:"#3B82F6",percentage:-33,trend:"down",subtitle:"vs sem. anterior"},{title:"Tests Completados",value:e.completedTests,icon:b,color:"#F59E0B",percentage:5,trend:"up",subtitle:"este mes"}],o=[{id:1,type:"Nuevo usuario registrado",user:"<EMAIL>",time:"Hace 15m",icon:u,color:"#10B981"},{id:2,type:"Test MACI-II completado",user:"<EMAIL>",time:"Hace 1h",icon:b,color:"#F59E0B"},{id:3,type:"Rol de usuario actualizado",user:"<EMAIL>",time:"Hace 2h",icon:g,color:"#6366F1"}];return a?p.jsxDEV("div",{className:"flex items-center justify-center h-64",children:[p.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:126,columnNumber:9},void 0),p.jsxDEV("span",{className:"ml-3 text-gray-600",children:"Cargando..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:127,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:125,columnNumber:7},void 0):p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"text-center",children:[p.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Dashboard de Administración"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:136,columnNumber:9},void 0),p.jsxDEV("p",{className:"text-gray-600",children:"Resumen general del sistema BAT-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:137,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:135,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((e,i)=>p.jsxDEV(Y,{title:e.title,value:e.value,icon:e.icon,color:e.color,percentage:e.percentage,trend:e.trend,subtitle:e.subtitle},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:143,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:141,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[p.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad de Usuarios (Últimos 7 días)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:159,columnNumber:11},void 0),p.jsxDEV("div",{className:"h-48 flex items-end justify-between space-x-2",children:[12,8,15,10,6,14,9].map((e,i)=>p.jsxDEV("div",{className:"flex-1 flex flex-col items-center",children:[p.jsxDEV("div",{className:"w-full bg-gradient-to-t from-blue-500 to-blue-300 rounded-t-lg",style:{height:e/15*100+"%",minHeight:"20px"}},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:163,columnNumber:17},void 0),p.jsxDEV("span",{className:"text-xs text-gray-500 mt-2",children:["Lun","Mar","Mié","Jue","Vie","Sáb","Dom"][i]},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:167,columnNumber:17},void 0)]},i,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:162,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:160,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:158,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Actividad Reciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:177,columnNumber:11},void 0),p.jsxDEV("div",{className:"space-y-4",children:o.map(e=>{const i=e.icon;return p.jsxDEV("div",{className:"flex items-center space-x-3",children:[p.jsxDEV("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0",style:{backgroundColor:e.color},children:p.jsxDEV(i,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:187,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:183,columnNumber:19},void 0),p.jsxDEV("div",{className:"flex-1 min-w-0",children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-900",children:e.type},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:190,columnNumber:21},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500 truncate",children:e.user},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:191,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:189,columnNumber:19},void 0),p.jsxDEV("div",{className:"text-sm text-gray-500",children:e.time},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:193,columnNumber:19},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:182,columnNumber:17},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:178,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:176,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:157,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/AdminDashboard.jsx",lineNumber:133,columnNumber:5},void 0)},Q=()=>{const[e,i]=F.useState([{id:1,nombre:"Juan",apellido:"Pérez",email:"<EMAIL>",documento:"12345678",tipo_usuario:"candidato",activo:!0,fecha_creacion:"2025-07-15"},{id:2,nombre:"María",apellido:"García",email:"<EMAIL>",documento:"87654321",tipo_usuario:"psicologo",activo:!0,fecha_creacion:"2025-07-14"},{id:3,nombre:"Admin",apellido:"Sistema",email:"<EMAIL>",documento:"11111111",tipo_usuario:"administrador",activo:!0,fecha_creacion:"2025-07-10"}]),[a,n]=F.useState(""),[s,o]=F.useState("all"),[r,m]=F.useState(!1),[d,b]=F.useState(null),[g,j]=F.useState(!1),V=e.filter(e=>{var i,n,o,r;const l=(null==(i=e.nombre)?void 0:i.toLowerCase().includes(a.toLowerCase()))||(null==(n=e.apellido)?void 0:n.toLowerCase().includes(a.toLowerCase()))||(null==(o=e.email)?void 0:o.toLowerCase().includes(a.toLowerCase()))||(null==(r=e.documento)?void 0:r.includes(a)),t="all"===s||e.tipo_usuario===s;return l&&t}),C={total:e.length,active:e.filter(e=>!0===e.activo).length,inactive:e.filter(e=>!1===e.activo).length},P=e=>{switch(null==e?void 0:e.toLowerCase()){case"administrador":return"bg-red-100 text-red-800";case"psicologo":return"bg-blue-100 text-blue-800";case"candidato":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"text-center mb-6",children:[p.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestión de Usuarios"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:112,columnNumber:9},void 0),p.jsxDEV("p",{className:"text-gray-600 mt-2",children:"Administra usuarios del sistema"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:113,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:111,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[p.jsxDEV("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxDEV("div",{className:"flex items-center",children:[p.jsxDEV("div",{className:"p-3 rounded-full bg-blue-100 mr-4",children:p.jsxDEV(c,{className:"w-6 h-6 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:121,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:120,columnNumber:13},void 0),p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-2xl font-bold text-gray-900",children:C.total},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:124,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-gray-600",children:"Total Usuarios"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:125,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:123,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:119,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:118,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxDEV("div",{className:"flex items-center",children:[p.jsxDEV("div",{className:"p-3 rounded-full bg-green-100 mr-4",children:p.jsxDEV(N,{className:"w-6 h-6 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:133,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:132,columnNumber:13},void 0),p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-2xl font-bold text-gray-900",children:C.active},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:136,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-gray-600",children:"Usuarios Activos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:137,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:135,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:131,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:130,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:p.jsxDEV("div",{className:"flex items-center",children:[p.jsxDEV("div",{className:"p-3 rounded-full bg-red-100 mr-4",children:p.jsxDEV(x,{className:"w-6 h-6 text-red-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:145,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:144,columnNumber:13},void 0),p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-2xl font-bold text-gray-900",children:C.inactive},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:148,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-gray-600",children:"Usuarios Inactivos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:149,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:147,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:143,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:142,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:117,columnNumber:7},void 0),p.jsxDEV("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[p.jsxDEV("div",{className:"flex flex-col md:flex-row gap-4 mb-6",children:[p.jsxDEV("div",{className:"flex-1",children:p.jsxDEV("div",{className:"relative",children:[p.jsxDEV(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:160,columnNumber:15},void 0),p.jsxDEV("input",{type:"text",placeholder:"Buscar usuarios...",value:a,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:161,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:159,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:158,columnNumber:11},void 0),p.jsxDEV("select",{value:s,onChange:e=>o(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsxDEV("option",{value:"all",children:"Todos los roles"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:176,columnNumber:13},void 0),p.jsxDEV("option",{value:"administrador",children:"Administrador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:177,columnNumber:13},void 0),p.jsxDEV("option",{value:"psicologo",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:178,columnNumber:13},void 0),p.jsxDEV("option",{value:"candidato",children:"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:179,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:171,columnNumber:11},void 0),p.jsxDEV("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2",onClick:()=>m(!0),children:[p.jsxDEV(u,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:186,columnNumber:13},void 0),p.jsxDEV("span",{children:"Nuevo Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:187,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:182,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:157,columnNumber:9},void 0),p.jsxDEV("div",{className:"overflow-x-auto",children:p.jsxDEV("table",{className:"min-w-full",children:[p.jsxDEV("thead",{children:p.jsxDEV("tr",{className:"border-b border-gray-200",children:[p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:196,columnNumber:17},void 0),p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:197,columnNumber:17},void 0),p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:198,columnNumber:17},void 0),p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Rol"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:199,columnNumber:17},void 0),p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:200,columnNumber:17},void 0),p.jsxDEV("th",{className:"text-left py-3 px-4 font-medium text-gray-700",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:201,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:195,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:194,columnNumber:13},void 0),p.jsxDEV("tbody",{children:V.map(a=>{return p.jsxDEV("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[p.jsxDEV("td",{className:"py-3 px-4",children:p.jsxDEV("div",{children:[p.jsxDEV("div",{className:"font-medium text-gray-900",children:[a.nombre," ",a.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:209,columnNumber:23},void 0),p.jsxDEV("div",{className:"text-sm text-gray-500",children:["ID: ",a.id]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:212,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:208,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:207,columnNumber:19},void 0),p.jsxDEV("td",{className:"py-3 px-4 text-gray-600",children:a.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:217,columnNumber:19},void 0),p.jsxDEV("td",{className:"py-3 px-4 text-gray-600",children:a.documento},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:218,columnNumber:19},void 0),p.jsxDEV("td",{className:"py-3 px-4",children:p.jsxDEV("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${P(a.tipo_usuario)}`,children:a.tipo_usuario},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:220,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:219,columnNumber:19},void 0),p.jsxDEV("td",{className:"py-3 px-4",children:p.jsxDEV("span",{className:"px-2 py-1 text-xs font-medium rounded-full "+(n=a.activo,!0===n||"active"===n?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:a.activo?"Activo":"Inactivo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:225,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:224,columnNumber:19},void 0),p.jsxDEV("td",{className:"py-3 px-4",children:p.jsxDEV("div",{className:"flex space-x-2",children:[p.jsxDEV("button",{className:"p-2 text-blue-600 hover:bg-blue-100 rounded",onClick:()=>(e=>{b(e),j(!0)})(a),title:"Editar usuario",children:p.jsxDEV(v,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:236,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:231,columnNumber:23},void 0),p.jsxDEV("button",{className:"p-2 text-red-600 hover:bg-red-100 rounded",onClick:()=>{return n=a.id,void(window.confirm("¿Está seguro de que desea eliminar este usuario?")&&i(e.filter(e=>e.id!==n)));var n},title:"Eliminar usuario",children:p.jsxDEV(h,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:243,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:238,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:230,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:229,columnNumber:19},void 0)]},a.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:206,columnNumber:17},void 0);var n})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:204,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:193,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:192,columnNumber:9},void 0),0===V.length&&p.jsxDEV("div",{className:"text-center py-8 text-gray-500",children:"No se encontraron usuarios que coincidan con los criterios de búsqueda."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:254,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:156,columnNumber:7},void 0),r&&p.jsxDEV(ee,{isOpen:r,onClose:()=>m(!1),onSave:a=>{const n=t(l({id:Math.max(...e.map(e=>e.id))+1},a),{fecha_creacion:(new Date).toISOString().split("T")[0],activo:!0});i([...e,n]),m(!1)},title:"Crear Nuevo Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:262,columnNumber:9},void 0),g&&p.jsxDEV(ee,{isOpen:g,onClose:()=>j(!1),onSave:a=>{i(e.map(e=>e.id===d.id?l(l({},e),a):e)),j(!1),b(null)},title:"Editar Usuario",initialData:d},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:272,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:110,columnNumber:5},void 0)},ee=({isOpen:e,onClose:i,onSave:a,title:n,initialData:s=null})=>{const[o,r]=F.useState({nombre:(null==s?void 0:s.nombre)||"",apellido:(null==s?void 0:s.apellido)||"",email:(null==s?void 0:s.email)||"",documento:(null==s?void 0:s.documento)||"",tipo_usuario:(null==s?void 0:s.tipo_usuario)||"candidato"});return e?p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxDEV("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[p.jsxDEV("h3",{className:"text-lg font-semibold mb-4",children:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:313,columnNumber:9},void 0),p.jsxDEV("form",{onSubmit:e=>{e.preventDefault(),o.nombre&&o.apellido&&o.email&&o.documento&&(a(o),r({nombre:"",apellido:"",email:"",documento:"",tipo_usuario:"candidato"}))},className:"space-y-4",children:[p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:317,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",value:o.nombre,onChange:e=>r(t(l({},o),{nombre:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:320,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:316,columnNumber:11},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Apellido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:330,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",value:o.apellido,onChange:e=>r(t(l({},o),{apellido:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:333,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:329,columnNumber:11},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:343,columnNumber:13},void 0),p.jsxDEV("input",{type:"email",value:o.email,onChange:e=>r(t(l({},o),{email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:346,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:342,columnNumber:11},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:356,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",value:o.documento,onChange:e=>r(t(l({},o),{documento:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:359,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:355,columnNumber:11},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipo de Usuario"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:369,columnNumber:13},void 0),p.jsxDEV("select",{value:o.tipo_usuario,onChange:e=>r(t(l({},o),{tipo_usuario:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[p.jsxDEV("option",{value:"candidato",children:"Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:377,columnNumber:15},void 0),p.jsxDEV("option",{value:"psicologo",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:378,columnNumber:15},void 0),p.jsxDEV("option",{value:"administrador",children:"Administrador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:379,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:372,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:368,columnNumber:11},void 0),p.jsxDEV("div",{className:"flex justify-end space-x-3 pt-4",children:[p.jsxDEV("button",{type:"button",onClick:i,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:384,columnNumber:13},void 0),p.jsxDEV("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:s?"Actualizar":"Crear"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:391,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:383,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:315,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:312,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/SimpleUserManagementPanel.jsx",lineNumber:311,columnNumber:5},void 0):null},ie=()=>{const[e,i]=F.useState(""),[a,n]=F.useState(!1),[s,o]=F.useState([{id:1,name:"Inicio",path:"/home",description:"Página principal del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:["U1","U2"]},{id:2,name:"Cuestionario",path:"/cuestionario",description:"Evaluaciones psicológicas BAT-7",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:3,name:"Resultados",path:"/resultados",description:"Visualización de resultados de evaluaciones",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:4,name:"Informes",path:"/informes",description:"Generación y gestión de informes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:5,name:"Administración",path:"/admin/administration",description:"Panel de administración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:6,name:"Configuración",path:"/configuracion",description:"Configuración del sistema",status:"Activa",permissions:{administrador:!0,psicologo:!1,candidato:!1},specificUsers:[]},{id:7,name:"Pacientes",path:"/pacientes",description:"Gestión de pacientes",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!1},specificUsers:[]},{id:8,name:"Soporte",path:"/soporte",description:"Centro de ayuda y soporte técnico",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]},{id:9,name:"Ayuda",path:"/ayuda",description:"Documentación y guías de usuario",status:"Activa",permissions:{administrador:!0,psicologo:!0,candidato:!0},specificUsers:[]}]),r=s.filter(i=>i.name.toLowerCase().includes(e.toLowerCase())||i.path.toLowerCase().includes(e.toLowerCase())),m=(e,i)=>{switch(e){case"administrador":return"bg-blue-500";case"psicologo":return"bg-green-500";case"candidato":return"bg-orange-500";default:return"bg-gray-500"}};return p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"flex items-center space-x-3",children:[p.jsxDEV("div",{className:"p-2 bg-blue-100 rounded-lg",children:p.jsxDEV(c,{className:"w-5 h-5 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:183,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:182,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("h2",{className:"text-xl font-semibold text-gray-900",children:"Control de Acceso a Páginas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:186,columnNumber:11},void 0),p.jsxDEV("p",{className:"text-gray-600",children:"Gestiona qué roles pueden acceder a cada página del sistema. Los cambios se aplican inmediatamente."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:187,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:185,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:181,columnNumber:7},void 0),p.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:p.jsxDEV("div",{className:"flex flex-col sm:flex-row gap-4",children:[p.jsxDEV("div",{className:"flex-1 relative",children:[p.jsxDEV(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:195,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",placeholder:"Buscar rutas por nombre o path...",value:e,onChange:e=>i(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:196,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:194,columnNumber:11},void 0),p.jsxDEV("button",{onClick:()=>n(!a),className:"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2",children:[p.jsxDEV(j,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:209,columnNumber:13},void 0),p.jsxDEV("span",{children:"Filtros Avanzados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:210,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:205,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:193,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:192,columnNumber:7},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:[p.jsxDEV("div",{className:"bg-blue-600 text-white",children:p.jsxDEV("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 font-medium",children:[p.jsxDEV("div",{className:"col-span-2",children:"Página"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:220,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-2",children:"Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:221,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-2 text-center",children:"👑 Administrador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:222,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-2 text-center",children:"👨‍⚕️ Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:223,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-2 text-center",children:"🎓 Candidato"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:224,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-1 text-center",children:"👥 Usuarios Específicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:225,columnNumber:13},void 0),p.jsxDEV("div",{className:"col-span-1 text-center",children:"Info"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:226,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:219,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:218,columnNumber:9},void 0),p.jsxDEV("div",{className:"divide-y divide-gray-200",children:r.map(e=>p.jsxDEV("div",{className:"grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50",children:[p.jsxDEV("div",{className:"col-span-2",children:[p.jsxDEV("div",{className:"font-medium text-gray-900",children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:236,columnNumber:17},void 0),p.jsxDEV("div",{className:"text-sm text-gray-500",children:e.path},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:237,columnNumber:17},void 0),p.jsxDEV("div",{className:"text-xs text-gray-400",children:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:238,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:235,columnNumber:15},void 0),p.jsxDEV("div",{className:"col-span-2 flex items-center",children:p.jsxDEV("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("Activa"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:243,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:242,columnNumber:15},void 0),["administrador","psicologo","candidato"].map(i=>p.jsxDEV("div",{className:"col-span-2 flex justify-center",children:p.jsxDEV("button",{onClick:()=>((e,i)=>{o(s.map(a=>a.id===e?t(l({},a),{permissions:t(l({},a.permissions),{[i]:!a.permissions[i]})}):a))})(e.id,i),className:`w-12 h-6 rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${e.permissions[i]?m(i):"bg-gray-300"}`,children:p.jsxDEV("div",{className:"w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 ease-in-out "+(e.permissions[i]?"translate-x-6":"translate-x-0.5")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:263,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:255,columnNumber:19},void 0)},i,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:254,columnNumber:17},void 0)),p.jsxDEV("div",{className:"col-span-1 flex justify-center",children:p.jsxDEV("button",{className:"px-3 py-1 text-xs bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200",children:["👥 ",e.specificUsers.length," usuario(s)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:272,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:271,columnNumber:15},void 0),p.jsxDEV("div",{className:"col-span-1 flex justify-center",children:p.jsxDEV("button",{className:"p-1 text-gray-400 hover:text-gray-600",children:p.jsxDEV(V,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:280,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:279,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:278,columnNumber:15},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:233,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:231,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:216,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[p.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsxDEV("div",{className:"text-2xl font-bold text-blue-600",children:s.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:291,columnNumber:11},void 0),p.jsxDEV("div",{className:"text-sm text-gray-600",children:"Total de Páginas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:292,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:290,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsxDEV("div",{className:"text-2xl font-bold text-green-600",children:s.filter(e=>"Activa"===e.status).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:295,columnNumber:11},void 0),p.jsxDEV("div",{className:"text-sm text-gray-600",children:"Páginas Activas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:298,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:294,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsxDEV("div",{className:"text-2xl font-bold text-orange-600",children:s.filter(e=>e.permissions.candidato).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:301,columnNumber:11},void 0),p.jsxDEV("div",{className:"text-sm text-gray-600",children:"Accesibles a Candidatos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:304,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:300,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200",children:[p.jsxDEV("div",{className:"text-2xl font-bold text-purple-600",children:s.filter(e=>e.specificUsers.length>0).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:307,columnNumber:11},void 0),p.jsxDEV("div",{className:"text-sm text-gray-600",children:"Con Usuarios Específicos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:310,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:306,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:289,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PageAccessPanel.jsx",lineNumber:179,columnNumber:5},void 0)},ae=()=>{const[e,i]=F.useState([]),[a,n]=F.useState([]),[s,o]=F.useState([]),[r,c]=F.useState(!0),[d,u]=F.useState(""),[b,g]=F.useState(""),[N,x]=F.useState([]),[v,h]=F.useState(!1),[j,V]=F.useState([]),[w,S]=F.useState("");F.useEffect(()=>{A()},[]);const A=()=>m(null,null,function*(){try{c(!0),M(),k(),_()}catch(e){q.error("Error al cargar datos")}finally{c(!1)}}),M=()=>{i([{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Clínica"},{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Educativa"},{id:"3",nombre:"Dr. García",apellido:"Fernández",email:"<EMAIL>",tipo_usuario:"psicologo",especialidad:"Psicología Organizacional"}])},k=()=>{n([{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"12345678"},{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>",tipo_usuario:"candidato",documento:"87654321"},{id:"5",nombre:"Carlos",apellido:"López",email:"<EMAIL>",tipo_usuario:"candidato",documento:"11223344"},{id:"6",nombre:"Ana",apellido:"Martínez",email:"<EMAIL>",tipo_usuario:"candidato",documento:"44332211"}])},_=()=>{o([{id:1,psicologo_id:"1",paciente_id:"3",assigned_at:"2025-07-15T10:30:00Z",is_active:!0,psychologist:{id:"1",nombre:"Dr. Rodriguez",apellido:"Martínez",email:"<EMAIL>"},patient:{id:"3",nombre:"Juan",apellido:"Pérez",email:"<EMAIL>"}},{id:2,psicologo_id:"2",paciente_id:"4",assigned_at:"2025-07-14T14:20:00Z",is_active:!0,psychologist:{id:"2",nombre:"Dra. Martínez",apellido:"López",email:"<EMAIL>"},patient:{id:"4",nombre:"María",apellido:"García",email:"<EMAIL>"}}])},L=()=>{const e=s.filter(e=>!0===e.is_active).map(e=>e.paciente_id);return a.filter(i=>!e.includes(i.id))},$=e.filter(e=>{var i,a,n;return(null==(i=e.nombre)?void 0:i.toLowerCase().includes(d.toLowerCase()))||(null==(a=e.apellido)?void 0:a.toLowerCase().includes(d.toLowerCase()))||(null==(n=e.email)?void 0:n.toLowerCase().includes(d.toLowerCase()))});return r?p.jsxDEV("div",{className:"flex justify-center items-center h-64",children:p.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:278,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:277,columnNumber:7},void 0):p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"text-center mb-6",children:[p.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:"Asignación de Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:286,columnNumber:9},void 0),p.jsxDEV("p",{className:"text-gray-600 mt-2",children:"Gestiona la asignación de pacientes a psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:287,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:285,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-600",children:"Psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:295,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:e.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:296,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:294,columnNumber:13},void 0),p.jsxDEV(C,{className:"w-8 h-8 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:298,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:293,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:292,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-600",children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:304,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:a.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:305,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:303,columnNumber:13},void 0),p.jsxDEV(P,{className:"w-8 h-8 text-green-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:307,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:302,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:301,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-600",children:"Asignaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:313,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:s.filter(e=>"active"===e.status).length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:314,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:312,columnNumber:13},void 0),p.jsxDEV(E,{className:"w-8 h-8 text-orange-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:316,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:311,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:310,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-600",children:"Sin Asignar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:322,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:L().length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:323,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:321,columnNumber:13},void 0),p.jsxDEV(D,{className:"w-8 h-8 text-red-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:325,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:320,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:319,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:291,columnNumber:7},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6",children:[p.jsxDEV("div",{className:"relative",children:[p.jsxDEV(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:334,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",placeholder:"Buscar psicólogos...",value:d,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:335,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:333,columnNumber:11},void 0),p.jsxDEV("button",{onClick:()=>h(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[p.jsxDEV(y,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:347,columnNumber:13},void 0),p.jsxDEV("span",{children:"Nueva Asignación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:348,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:343,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:332,columnNumber:9},void 0),p.jsxDEV("div",{className:"space-y-6",children:$.map(e=>{const i=(n=e.id,s.filter(e=>e.psicologo_id===n&&!0===e.is_active).map(e=>{const i=a.find(i=>i.id===e.paciente_id);return t(l({},e),{patient:i})}).filter(e=>e.patient));var n;return p.jsxDEV("div",{className:"border border-gray-200 rounded-lg p-6",children:[p.jsxDEV("div",{className:"flex items-center justify-between mb-4",children:[p.jsxDEV("div",{className:"flex items-center space-x-3",children:[p.jsxDEV("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:p.jsxDEV(C,{className:"w-5 h-5 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:362,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:361,columnNumber:21},void 0),p.jsxDEV("div",{children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:365,columnNumber:23},void 0),p.jsxDEV("p",{className:"text-sm text-gray-600",children:e.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:368,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:364,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:360,columnNumber:19},void 0),p.jsxDEV("div",{className:"text-right",children:[p.jsxDEV("p",{className:"text-sm text-gray-600",children:"Pacientes asignados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:372,columnNumber:21},void 0),p.jsxDEV("p",{className:"text-2xl font-bold text-blue-600",children:i.length},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:373,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:371,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:359,columnNumber:17},void 0),i.length>0?p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:i.map(e=>p.jsxDEV("div",{className:"bg-gray-50 rounded-lg p-4 flex items-center justify-between",children:[p.jsxDEV("div",{className:"flex items-center space-x-3",children:[p.jsxDEV("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:p.jsxDEV(P,{className:"w-4 h-4 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:383,columnNumber:29},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:382,columnNumber:27},void 0),p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-900",children:[e.patient.nombre," ",e.patient.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:386,columnNumber:29},void 0),p.jsxDEV("p",{className:"text-xs text-gray-600",children:e.patient.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:389,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:385,columnNumber:27},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:381,columnNumber:25},void 0),p.jsxDEV("button",{onClick:()=>{return i=e.id,m(null,null,function*(){if(window.confirm("¿Estás seguro de que quieres desasignar este paciente?"))try{o(e=>e.filter(e=>e.id!==i));const{error:e}=yield supabase.from("patient_assignments").delete().eq("id",i);if(e&&"42P01"!==e.code)throw e;q.success("Paciente desasignado exitosamente")}catch(e){q.error("Error al desasignar paciente")}});var i},className:"text-red-600 hover:text-red-900 p-1",title:"Desasignar paciente",children:p.jsxDEV(B,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:397,columnNumber:27},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:392,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:380,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:378,columnNumber:19},void 0):p.jsxDEV("div",{className:"text-center py-8 text-gray-500",children:[p.jsxDEV(P,{className:"w-12 h-12 mx-auto mb-2 opacity-50"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:404,columnNumber:21},void 0),p.jsxDEV("p",{children:"No hay pacientes asignados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:405,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:403,columnNumber:19},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:358,columnNumber:15},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:353,columnNumber:9},void 0),0===$.length&&p.jsxDEV("div",{className:"text-center py-8",children:p.jsxDEV("p",{className:"text-gray-500",children:"No se encontraron psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:415,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:414,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:331,columnNumber:7},void 0),v&&p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxDEV("div",{className:"bg-white rounded-lg p-6 w-full max-w-lg",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Nueva Asignación"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:424,columnNumber:13},void 0),p.jsxDEV("div",{className:"space-y-4",children:[p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:428,columnNumber:17},void 0),p.jsxDEV("select",{value:b,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsxDEV("option",{value:"",children:"Seleccionar psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:436,columnNumber:19},void 0),e.map(e=>p.jsxDEV("option",{value:e.id,children:[e.nombre," ",e.apellido]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:438,columnNumber:21},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:431,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:427,columnNumber:15},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Pacientes sin asignar (",L().filter(e=>e.nombre.toLowerCase().includes(w.toLowerCase())||e.apellido.toLowerCase().includes(w.toLowerCase())||e.email.toLowerCase().includes(w.toLowerCase())).length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:446,columnNumber:17},void 0),p.jsxDEV("div",{className:"relative mb-3",children:[p.jsxDEV(f,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:456,columnNumber:19},void 0),p.jsxDEV("input",{type:"text",placeholder:"Buscar pacientes...",value:w,onChange:e=>S(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:457,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:455,columnNumber:17},void 0),p.jsxDEV("div",{className:"max-h-48 overflow-y-auto border border-gray-300 rounded-lg",children:[L().filter(e=>e.nombre.toLowerCase().includes(w.toLowerCase())||e.apellido.toLowerCase().includes(w.toLowerCase())||e.email.toLowerCase().includes(w.toLowerCase())).map(e=>p.jsxDEV("div",{className:"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0 flex items-center justify-between "+(j.includes(e.id)?"bg-blue-50 border-blue-200":""),onClick:()=>{return i=e.id,void V(e=>e.includes(i)?e.filter(e=>e!==i):[...e,i]);var i},children:[p.jsxDEV("div",{className:"flex-1",children:[p.jsxDEV("p",{className:"text-sm font-medium text-gray-900",children:[e.nombre," ",e.apellido]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:482,columnNumber:25},void 0),p.jsxDEV("p",{className:"text-xs text-gray-600",children:e.email},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:485,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:481,columnNumber:23},void 0),j.includes(e.id)&&p.jsxDEV(U,{className:"w-4 h-4 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:488,columnNumber:25},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:474,columnNumber:21},void 0)),0===L().filter(e=>e.nombre.toLowerCase().includes(w.toLowerCase())||e.apellido.toLowerCase().includes(w.toLowerCase())||e.email.toLowerCase().includes(w.toLowerCase())).length&&p.jsxDEV("div",{className:"p-4 text-center text-gray-500",children:w?"No se encontraron pacientes":"No hay pacientes sin asignar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:497,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:466,columnNumber:17},void 0),j.length>0&&p.jsxDEV("div",{className:"mt-2 text-sm text-blue-600",children:[j.length," paciente(s) seleccionado(s)"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:504,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:445,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:426,columnNumber:13},void 0),p.jsxDEV("div",{className:"flex space-x-2 mt-6",children:[p.jsxDEV("button",{onClick:()=>{h(!1),V([]),g(""),S("")},className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:512,columnNumber:15},void 0),p.jsxDEV("button",{onClick:()=>m(null,null,function*(){if(b&&0!==j.length)try{const e=j.map(e=>({id:`${b}-${e}-${Date.now()}`,psicologo_id:b,paciente_id:e,assigned_at:(new Date).toISOString(),is_active:!0}));o(i=>[...i,...e]),V([]),g(""),S(""),h(!1),q.success(`${j.length} paciente(s) asignado(s) exitosamente`)}catch(e){q.error("Error al guardar asignaciones")}else q.error("Debe seleccionar un psicólogo y al menos un paciente")}),disabled:!b||0===j.length,className:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[p.jsxDEV(U,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:528,columnNumber:17},void 0),p.jsxDEV("span",{children:["Guardar (",j.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:529,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:523,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:511,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:423,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:422,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PatientAssignmentPanel.jsx",lineNumber:284,columnNumber:5},void 0)},ne=({isOpen:e,onClose:i,onSuccess:a})=>{const[n,s]=F.useState(!1),{psychologists:o,loading:r,error:l}=(()=>{const[e,i]=F.useState([]),[a,n]=F.useState(!1),[s,o]=F.useState(null),r=F.useCallback(()=>m(null,null,function*(){try{n(!0),o(null);const e=(yield X.getAllPsychologists()).map(e=>{var i,a;return{id:e.psychologist_id,nombre:(null==(i=e.psychologist_name)?void 0:i.split(" ")[0])||"",apellido:(null==(a=e.psychologist_name)?void 0:a.split(" ").slice(1).join(" "))||"",email:e.psychologist_email,currentPins:e.remaining_pins,hasControl:e.has_control}});i(e)}catch(e){o("Error al cargar la lista de psicólogos"),q.error("Error al cargar la lista de psicólogos")}finally{n(!1)}}),[]);return F.useEffect(()=>{r()},[r]),{psychologists:e,loading:a,error:s,refetch:r}})(),{selectedPsychologist:t,pinAmount:c,pinOptions:d,validation:u,setSelectedPsychologist:b,setPinAmount:g,handlePinOptionClick:N,resetForm:x,getFormData:f}=(()=>{const[e,i]=F.useState(""),[a,n]=F.useState(""),[s,o]=F.useState(!1),r=F.useMemo(()=>[1,5,25,50,100,W.THRESHOLDS.BULK_ASSIGNMENT_MIN,W.THRESHOLDS.BULK_ASSIGNMENT_MAX],[]),l=F.useMemo(()=>{const i=[];if(e||i.push("Debe seleccionar un psicólogo"),!s){const e=parseInt(a);!a||e<=0?i.push("Debe ingresar una cantidad válida de pines"):e>W.THRESHOLDS.BULK_ASSIGNMENT_MAX&&i.push(`La cantidad máxima es ${W.THRESHOLDS.BULK_ASSIGNMENT_MAX} pines`)}return{isValid:0===i.length,errors:i}},[e,a,s]),t=F.useCallback(e=>{n(e.toString())},[]),m=F.useCallback(()=>{i(""),n(""),o(!1)},[]),c=F.useCallback(()=>({psychologistId:e,amount:s?0:parseInt(a),isUnlimited:s,planType:s?W.PLAN_TYPES.UNLIMITED:W.PLAN_TYPES.ASSIGNED}),[e,a,s]);return{selectedPsychologist:e,pinAmount:a,isUnlimited:s,pinOptions:r,validation:l,setSelectedPsychologist:i,setPinAmount:n,setIsUnlimited:o,handlePinOptionClick:t,resetForm:m,getFormData:c}})();F.useEffect(()=>{e||x()},[e,x]);const v=F.useCallback(()=>m(null,null,function*(){if(u.isValid)try{s(!0);const e=f();yield J.assignPins(e.psychologistId,e.amount,`Asignación manual ${e.isUnlimited?"ilimitada":`de ${e.amount} pines`} desde modal`),x(),null==a||a(),i(),q.success(`Pines ${e.isUnlimited?"ilimitados":e.amount} asignados correctamente`)}catch(e){q.error("Error al asignar pines: "+e.message)}finally{s(!1)}else u.errors.forEach(e=>q.warn(e))}),[u,f,x,a,i]),h=F.useCallback(()=>{n||i()},[n,i]);return F.useMemo(()=>o.find(e=>e.id===t),[o,t]),e?p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:p.jsxDEV("div",{className:"bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4 transform transition-all",children:[p.jsxDEV("div",{className:"flex items-center justify-between p-6 border-b",children:[p.jsxDEV("div",{className:"flex items-center space-x-3",children:[p.jsxDEV("div",{className:"p-2 bg-blue-100 rounded-lg",children:p.jsxDEV(w,{className:"w-6 h-6 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:103,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:102,columnNumber:13},void 0),p.jsxDEV("div",{children:[p.jsxDEV("h2",{className:"text-xl font-bold text-gray-900",id:"modal-title",children:"Asignar Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:106,columnNumber:15},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500",children:"Gestiona la asignación de pines a psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:109,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:105,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:101,columnNumber:11},void 0),p.jsxDEV("button",{onClick:h,disabled:n,className:"text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Cerrar modal",children:p.jsxDEV(B,{size:20},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:118,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:112,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:100,columnNumber:9},void 0),p.jsxDEV("div",{className:"p-6",children:[p.jsxDEV("div",{className:"mb-6",children:[p.jsxDEV("label",{htmlFor:"psychologist-select",className:"block text-sm font-semibold text-gray-700 mb-3",children:"Seleccionar Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:126,columnNumber:13},void 0),r?p.jsxDEV("div",{className:"flex items-center justify-center py-3",children:[p.jsxDEV(S,{className:"animate-spin text-blue-500 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:131,columnNumber:17},void 0),p.jsxDEV("span",{className:"text-gray-600",children:"Cargando psicólogos..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:132,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:130,columnNumber:15},void 0):l?p.jsxDEV("div",{className:"flex items-center py-3 text-red-600",children:[p.jsxDEV(A,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:136,columnNumber:17},void 0),p.jsxDEV("span",{className:"text-sm",children:l},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:137,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:135,columnNumber:15},void 0):p.jsxDEV("div",{className:"relative",children:[p.jsxDEV("select",{id:"psychologist-select",value:t,onChange:e=>b(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white appearance-none cursor-pointer transition-all disabled:bg-gray-100",disabled:n,"aria-describedby":"psychologist-help",children:[p.jsxDEV("option",{value:"",children:"Seleccionar psicólogo..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:149,columnNumber:19},void 0),o.map(e=>p.jsxDEV("option",{value:e.id,children:[e.nombre," ",e.apellido," - ",e.email,void 0!==e.currentPins&&` (${e.currentPins} pines actuales)`]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:151,columnNumber:21},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:141,columnNumber:17},void 0),p.jsxDEV("div",{className:"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none",children:p.jsxDEV("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:p.jsxDEV("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:159,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:158,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:157,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:140,columnNumber:15},void 0),p.jsxDEV("p",{id:"psychologist-help",className:"text-xs text-gray-500 mt-1",children:"Selecciona el psicólogo al que deseas asignar pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:164,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:125,columnNumber:11},void 0),p.jsxDEV("div",{className:"mb-6",children:[p.jsxDEV("label",{className:"block text-sm font-semibold text-gray-700 mb-3",children:"Cantidad de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:171,columnNumber:13},void 0),p.jsxDEV("div",{className:"grid grid-cols-4 gap-2 mb-4",children:d.map(e=>p.jsxDEV("button",{type:"button",onClick:()=>N(e),className:"px-4 py-3 text-sm font-medium rounded-lg border-2 transition-all transform hover:scale-105 "+(c===e.toString()?"bg-blue-500 text-white border-blue-500 shadow-lg":"bg-white text-gray-700 border-gray-200 hover:bg-blue-50 hover:border-blue-300"),disabled:n,children:e},e,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:178,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:176,columnNumber:13},void 0),p.jsxDEV("div",{className:"relative",children:[p.jsxDEV("input",{type:"number",min:"1",max:"1000",placeholder:"Cantidad personalizada",value:c,onChange:e=>g(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all",disabled:n},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:196,columnNumber:15},void 0),p.jsxDEV("div",{className:"absolute inset-y-0 right-0 flex items-center px-3 pointer-events-none",children:p.jsxDEV(w,{className:"w-4 h-4 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:207,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:206,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:195,columnNumber:13},void 0),c&&parseInt(c)>0&&p.jsxDEV("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("span",{className:"text-sm text-blue-700 font-medium",children:"Pines a asignar:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:215,columnNumber:19},void 0),p.jsxDEV("span",{className:"text-lg font-bold text-blue-600",children:c},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:218,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:214,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:213,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:170,columnNumber:11},void 0),t&&c&&p.jsxDEV("div",{className:"mb-6 p-3 bg-blue-50 rounded-md border border-blue-200",children:p.jsxDEV("div",{className:"flex items-center text-blue-800",children:[p.jsxDEV(w,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:230,columnNumber:17},void 0),p.jsxDEV("span",{className:"text-sm",children:["Se asignarán ",p.jsxDEV("strong",{children:[c," pines"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:232,columnNumber:32},void 0)," al psicólogo seleccionado"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:231,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:229,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:228,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:123,columnNumber:9},void 0),p.jsxDEV("div",{className:"flex items-center justify-end space-x-3 p-6 border-t bg-gray-50",children:[p.jsxDEV("button",{onClick:h,disabled:n,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:241,columnNumber:11},void 0),p.jsxDEV("button",{onClick:v,disabled:n||!t||!c,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors flex items-center",children:n?p.jsxDEV(p.Fragment,{children:[p.jsxDEV(S,{className:"animate-spin mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:255,columnNumber:17},void 0),"Asignando..."]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:254,columnNumber:15},void 0):p.jsxDEV(p.Fragment,{children:[p.jsxDEV(w,{className:"mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:260,columnNumber:17},void 0),"Asignar Pines"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:259,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:248,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:240,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:98,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/PinAssignmentModal.jsx",lineNumber:97,columnNumber:5},void 0):null},se=()=>{const[e,i]=F.useState([]),a=K,[n,s]=F.useState("7days"),[o,r]=F.useState(!0),[l,t]=F.useState([]),[d,u]=F.useState([]),[g,N]=F.useState(""),[f,j]=F.useState(1),[V,C]=F.useState(!1),[P,E]=F.useState(!1),[D,B]=F.useState([]),[U,S]=F.useState(!1),[T,O]=F.useState([]),[I,z]=F.useState(!1),[R,G]=F.useState(!1),[H,X]=F.useState("pins"),[W,Y]=F.useState(1),[Z,Q]=F.useState(!1),ee=F.useMemo(()=>({totalPsychologists:e.length,totalPinsAssigned:e.reduce((e,i)=>e+(i.total_asignado||0),0),totalPinsUsed:e.reduce((e,i)=>e+(i.total_consumido||0),0),totalPinsRemaining:e.reduce((e,i)=>e+(i.pines_restantes||0),0)}),[e]);F.useEffect(()=>{ie()},[n]);const ie=()=>m(null,null,function*(){try{r(!0);const e=yield J.getPsychologistsWithPinStats();i(e);const a=yield J.getTransactionHistory(null,20);t(a);const n=e.filter(e=>("pocos_pines"===e.status||"sin_pines"===e.status)&&e.total_asignado>0).map(e=>({type:"sin_pines"===e.status?"error":"warning",psychologist_id:e.psicologo_id,psychologist_name:e.nombre_psicologo,message:"sin_pines"===e.status?`${e.nombre_psicologo} no tiene pines disponibles`:`${e.nombre_psicologo} tiene solo ${e.pines_restantes} pines restantes`,severity:"sin_pines"===e.status?"error":"warning"}));u(n)}catch(e){q.error("Error al cargar datos de pines")}finally{r(!1)}}),ae=e=>{switch(e){case"asignacion":return p.jsxDEV(y,{className:"w-4 h-4 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:129,columnNumber:16},void 0);case"consumo":return p.jsxDEV(w,{className:"w-4 h-4 text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:131,columnNumber:16},void 0);case"pin_assigned":return p.jsxDEV(y,{className:"w-4 h-4 text-green-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:133,columnNumber:16},void 0);case"pin_consumed":return p.jsxDEV(w,{className:"w-4 h-4 text-orange-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:135,columnNumber:16},void 0);case"test_completed":return p.jsxDEV(b,{className:"w-4 h-4 text-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:137,columnNumber:16},void 0);case"report_generated":return p.jsxDEV($,{className:"w-4 h-4 text-purple-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:139,columnNumber:16},void 0);default:return p.jsxDEV(c,{className:"w-4 h-4 text-gray-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:141,columnNumber:16},void 0)}},se=e=>{switch(e){case"asignacion":case"pin_assigned":return"bg-green-100 text-green-800";case"consumo":case"pin_consumed":return"bg-orange-100 text-orange-800";case"test_completed":return"bg-blue-100 text-blue-800";case"report_generated":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},oe=e=>{switch(e){case"activo":return"bg-green-100 text-green-800";case"pocos_pines":return"bg-yellow-100 text-yellow-800";case"sin_pines":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},re=e=>{switch(e){case"activo":return"Activo";case"pocos_pines":return"Pines Bajos";case"sin_pines":return"Sin Pines";default:return"Inactivo"}},le=e=>{const i=new Date,a=new Date(e),n=Math.floor((i-a)/6e4);return n<60?`Hace ${n} min`:n<1440?`Hace ${Math.floor(n/60)} h`:`Hace ${Math.floor(n/1440)} días`};return o?p.jsxDEV("div",{className:"flex justify-center items-center h-64",children:p.jsxDEV("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:556,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:555,columnNumber:7},void 0):p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"text-center mb-6",children:[p.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900 flex items-center justify-center gap-2",children:[p.jsxDEV(w,{className:"text-yellow-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:565,columnNumber:11},void 0),"Control de Pines"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:564,columnNumber:9},void 0),p.jsxDEV("p",{className:"text-gray-600 mt-2",children:"Gestiona y monitorea el uso de pines por psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:568,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:563,columnNumber:7},void 0),d.length>0&&p.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:[p.jsxDEV("div",{className:"flex items-center mb-2",children:[p.jsxDEV(A,{className:"text-yellow-600 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:575,columnNumber:13},void 0),p.jsxDEV("h3",{className:"text-lg font-semibold text-yellow-800",children:"Alertas de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:576,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:574,columnNumber:11},void 0),p.jsxDEV("div",{className:"space-y-2",children:d.map((e,i)=>p.jsxDEV("div",{className:"text-yellow-700",children:e.message},e.psychologist_id,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:580,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:578,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:573,columnNumber:9},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6",children:[p.jsxDEV("div",{className:"flex items-center space-x-4",children:[p.jsxDEV(M,{className:"text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:592,columnNumber:13},void 0),p.jsxDEV("select",{value:n,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[{value:"24hours",label:"Últimas 24 horas"},{value:"7days",label:"Últimos 7 días"},{value:"30days",label:"Últimos 30 días"},{value:"90days",label:"Últimos 90 días"}].map(e=>p.jsxDEV("option",{value:e.value,children:e.label},e.value,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:599,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:593,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:591,columnNumber:11},void 0),p.jsxDEV("div",{className:"flex space-x-3",children:[p.jsxDEV("button",{onClick:()=>E(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[p.jsxDEV(y,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:610,columnNumber:15},void 0),p.jsxDEV("span",{children:"Asignar Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:611,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:606,columnNumber:13},void 0),p.jsxDEV("button",{onClick:()=>{try{const i=[["Psicólogo","Email","Total Pines","Pines Usados","Pines Restantes","Estado","Plan","Pacientes Asignados","Tests Completados"],...e.map(e=>[e.psychologist_name||"N/A",e.psychologist_email||"N/A",e.is_unlimited?"Ilimitado":e.total_pins||0,e.used_pins||0,e.is_unlimited?"Ilimitado":e.remaining_pins||0,re(e.status),e.plan_type||"N/A",e.assigned_patients||0,e.completed_tests||0])].map(e=>e.join(",")).join("\n"),a=new Blob([i],{type:"text/csv;charset=utf-8;"}),s=window.URL.createObjectURL(a),o=document.createElement("a");o.href=s,o.download=`reporte_pines_${n}_${(new Date).toISOString().split("T")[0]}.csv`,o.click(),window.URL.revokeObjectURL(s),q.success("Reporte de pines exportado exitosamente")}catch(i){q.error("Error al exportar reporte")}},className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[p.jsxDEV(k,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:617,columnNumber:15},void 0),p.jsxDEV("span",{children:"Exportar Reporte"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:618,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:613,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:605,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:590,columnNumber:9},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[p.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-gray-600 text-sm font-medium",children:"Total Psicólogos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:628,columnNumber:17},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:ee.totalPsychologists},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:629,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:627,columnNumber:15},void 0),p.jsxDEV(c,{className:"w-8 h-8 text-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:631,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:626,columnNumber:13},void 0),p.jsxDEV("div",{className:"mt-2 text-gray-500 text-xs",children:[e.filter(e=>e.total_asignado>0).length," con pines asignados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:633,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:625,columnNumber:11},void 0),p.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-gray-600 text-sm font-medium",children:"Pines Asignados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:640,columnNumber:17},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:ee.totalPinsAssigned},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:641,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:639,columnNumber:15},void 0),p.jsxDEV(w,{className:"w-8 h-8 text-green-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:643,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:638,columnNumber:13},void 0),p.jsxDEV("div",{className:"mt-2 text-gray-500 text-xs",children:"0 planes ilimitados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:645,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:637,columnNumber:11},void 0),p.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-gray-600 text-sm font-medium",children:"Pines Usados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:652,columnNumber:17},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:ee.totalPinsUsed},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:653,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:651,columnNumber:15},void 0),p.jsxDEV(_,{className:"w-8 h-8 text-orange-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:655,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:650,columnNumber:13},void 0),p.jsxDEV("div",{className:"mt-2 text-gray-500 text-xs",children:[e.reduce((e,i)=>e+(i.tests_completados||0),0)," tests completados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:657,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:649,columnNumber:11},void 0),p.jsxDEV("div",{className:"bg-white border border-gray-200 rounded-lg shadow-md p-6",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("p",{className:"text-gray-600 text-sm font-medium",children:"Pines Restantes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:664,columnNumber:17},void 0),p.jsxDEV("p",{className:"text-3xl font-bold text-gray-900",children:ee.totalPinsRemaining},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:665,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:663,columnNumber:15},void 0),p.jsxDEV(L,{className:"w-8 h-8 text-purple-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:667,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:662,columnNumber:13},void 0),p.jsxDEV("div",{className:"mt-2 text-gray-500 text-xs",children:[e.reduce((e,i)=>e+(i.pacientes_asignados||0),0)," pacientes asignados"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:669,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:661,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:624,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("div",{className:"flex justify-between items-center mb-4",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Estado de Pines por Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:678,columnNumber:13},void 0),T.length>0&&p.jsxDEV("div",{className:"flex space-x-2",children:[p.jsxDEV("button",{onClick:()=>{X("pins"),G(!0)},className:"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",disabled:o,children:[p.jsxDEV(v,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:689,columnNumber:19},void 0),p.jsxDEV("span",{children:["Eliminar Pines (",T.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:690,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:681,columnNumber:17},void 0),p.jsxDEV("button",{onClick:()=>{X("complete"),G(!0)},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",disabled:o,children:[p.jsxDEV(h,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:700,columnNumber:19},void 0),p.jsxDEV("span",{children:["Eliminar Asignaciones (",T.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:701,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:692,columnNumber:17},void 0),p.jsxDEV("button",{onClick:()=>Q(!0),className:"bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",disabled:o,children:[p.jsxDEV(x,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:708,columnNumber:19},void 0),p.jsxDEV("span",{children:["Eliminar Asignaciones (",T.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:709,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:703,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:680,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:677,columnNumber:11},void 0),p.jsxDEV("div",{className:"overflow-x-auto",children:p.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[p.jsxDEV("thead",{className:"bg-gray-50",children:p.jsxDEV("tr",{children:[p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:p.jsxDEV("input",{type:"checkbox",checked:I,onChange:()=>{O(I?[]:e.map(e=>e.psicologo_id)),z(!I)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:719,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:718,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:726,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:729,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Totales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:732,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Usados"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:735,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pines Restantes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:738,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Estado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:741,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pacientes"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:744,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tests"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:747,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:717,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:716,columnNumber:15},void 0),p.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map((e,i)=>p.jsxDEV("tr",{className:"hover:bg-gray-50 "+(T.includes(e.psicologo_id)?"bg-blue-50":""),children:[p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("input",{type:"checkbox",checked:T.includes(e.psicologo_id),onChange:()=>{return i=e.psicologo_id,void O(e=>e.includes(i)?e.filter(e=>e!==i):[...e,i]);var i},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:756,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:755,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:e.nombre_psicologo||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:764,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:763,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm text-gray-500",children:e.email_psicologo||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:769,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:768,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm text-gray-900 flex items-center",children:e.total_asignado||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:774,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:773,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm text-gray-900",children:e.total_consumido||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:779,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:778,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm text-gray-900",children:e.pines_restantes||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:782,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:781,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${oe(e.status)}`,children:re(e.status)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:787,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:786,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.pacientes_asignados||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:791,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.tests_completados||0},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:794,columnNumber:21},void 0)]},e.psicologo_id||i,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:754,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:752,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:715,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:714,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:676,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("div",{className:"flex justify-between items-center mb-4",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Historial de Actividad de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:807,columnNumber:13},void 0),D.length>0&&p.jsxDEV("button",{onClick:()=>m(null,null,function*(){if(0===D.length)return void q.error("No hay registros seleccionados");if(!window.confirm(`¿Seguro que deseas eliminar ${D.length} registros seleccionados?`))return;const e=q.loading(`Eliminando ${D.length} transacciones...`);try{const i=yield J.deleteMultipleTransactions(D);i.success?(q.update(e,{render:`${i.deletedCount} transacciones eliminadas exitosamente`,type:"success",isLoading:!1,autoClose:3e3}),B([]),S(!1),yield ie(),i.affectedPsychologists):q.update(e,{render:`Error al eliminar transacciones: ${i.message}`,type:"error",isLoading:!1,autoClose:5e3})}catch(i){q.update(e,{render:"Error inesperado al eliminar transacciones",type:"error",isLoading:!1,autoClose:5e3})}}),className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",disabled:o,children:[p.jsxDEV(h,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:814,columnNumber:17},void 0),p.jsxDEV("span",{children:["Eliminar Seleccionados (",D.length,")"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:815,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:809,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:806,columnNumber:11},void 0),p.jsxDEV("div",{className:"overflow-x-auto",children:p.jsxDEV("table",{className:"min-w-full divide-y divide-gray-200",children:[p.jsxDEV("thead",{className:"bg-gray-50",children:p.jsxDEV("tr",{children:[p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:p.jsxDEV("input",{type:"checkbox",checked:U,onChange:()=>{B(U?[]:l.map(e=>e.id)),S(!U)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:824,columnNumber:21},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:823,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:831,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acción"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:834,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Paciente"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:837,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Fecha"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:840,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Descripción"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:843,columnNumber:19},void 0),p.jsxDEV("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:846,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:822,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:821,columnNumber:15},void 0),p.jsxDEV("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map((e,i)=>{var a;return p.jsxDEV("tr",{className:"hover:bg-gray-50 "+(D.includes(e.id)?"bg-blue-50":""),children:[p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("input",{type:"checkbox",checked:D.includes(e.id),onChange:()=>{return i=e.id,void B(e=>e.includes(i)?e.filter(e=>e!==i):[...e,i]);var i},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:855,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:854,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:[p.jsxDEV("div",{className:"text-sm font-medium text-gray-900",children:e.psicologos?`${e.psicologos.nombre} ${e.psicologos.apellido}`:"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:863,columnNumber:23},void 0),p.jsxDEV("div",{className:"text-sm text-gray-500",children:(null==(a=e.psicologos)?void 0:a.email)||"N/A"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:866,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:862,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"flex items-center space-x-2",children:[ae(e.tipo),p.jsxDEV("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${se(e.tipo)}`,children:"asignacion"===e.tipo?"Asignación":"Consumo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:873,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:871,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:870,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("div",{className:"text-sm text-gray-900",children:"-"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:879,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:878,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:le(e.created_at)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:883,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 text-sm text-gray-900",children:e.motivo||"Sin descripción"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:886,columnNumber:21},void 0),p.jsxDEV("td",{className:"px-6 py-4 whitespace-nowrap",children:p.jsxDEV("button",{onClick:()=>{return i=e.id,m(null,null,function*(){if(!window.confirm("¿Estás seguro de que deseas eliminar esta transacción? Esta acción no se puede deshacer."))return;const e=q.loading("Eliminando transacción...");try{const a=yield J.deleteTransaction(i);a.success?(q.update(e,{render:"Transacción eliminada exitosamente",type:"success",isLoading:!1,autoClose:3e3}),yield ie()):q.update(e,{render:`Error al eliminar transacción: ${a.message}`,type:"error",isLoading:!1,autoClose:5e3})}catch(a){q.update(e,{render:"Error inesperado al eliminar transacción",type:"error",isLoading:!1,autoClose:5e3})}});var i},className:"p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors",title:"Eliminar Transacción",disabled:o,children:p.jsxDEV(h,{className:"w-4 h-4"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:896,columnNumber:25},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:890,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:889,columnNumber:21},void 0)]},e.id||i,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:853,columnNumber:19},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:851,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:820,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:819,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:805,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:589,columnNumber:7},void 0),P&&p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxDEV("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Asignar Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:911,columnNumber:13},void 0),p.jsxDEV("div",{className:"space-y-4",children:[p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:915,columnNumber:17},void 0),p.jsxDEV("select",{value:g,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsxDEV("option",{value:"",children:"Seleccionar psicólogo..."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:923,columnNumber:19},void 0),e.map((e,i)=>p.jsxDEV("option",{value:e.psicologo_id,children:[e.nombre_psicologo," (",e.email_psicologo,")",e.total_asignado>0?` - ${e.pines_restantes||0} pines`:" - Sin pines asignados"]},e.psicologo_id||i,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:925,columnNumber:21},void 0))]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:918,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:914,columnNumber:15},void 0),p.jsxDEV("div",{children:p.jsxDEV("label",{className:"flex items-center space-x-2",children:[p.jsxDEV("input",{type:"checkbox",checked:V,onChange:e=>C(e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:935,columnNumber:19},void 0),p.jsxDEV("span",{className:"text-sm font-medium text-gray-700",children:"Plan Ilimitado"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:941,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:934,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:933,columnNumber:15},void 0),!V&&p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cantidad de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:947,columnNumber:19},void 0),p.jsxDEV("div",{className:"grid grid-cols-3 gap-2",children:[1,5,10,25,50,100].map(e=>p.jsxDEV("button",{onClick:()=>j(e),className:"px-3 py-2 rounded-lg text-sm font-medium transition-colors "+(f===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e},e,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:952,columnNumber:23},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:950,columnNumber:19},void 0),p.jsxDEV("input",{type:"number",value:f,onChange:e=>j(parseInt(e.target.value)||1),min:"1",className:"mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Cantidad personalizada"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:965,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:946,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:913,columnNumber:13},void 0),p.jsxDEV("div",{className:"flex justify-end space-x-3 mt-6",children:[p.jsxDEV("button",{onClick:()=>{E(!1),N(""),j(1),C(!1)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:978,columnNumber:15},void 0),p.jsxDEV("button",{onClick:()=>m(null,null,function*(){if(g)try{r(!0),yield J.assignPins(g,V?0:f,V,V?"unlimited":"manual"),q.success(`Pines ${V?"ilimitados":f} asignados correctamente`),E(!1),N(""),j(1),C(!1),yield ie()}catch(e){q.error("Error al asignar pines")}finally{r(!1)}else q.error("Selecciona un psicólogo")}),disabled:!g||o,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:o?"Asignando...":"Asignar Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:989,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:977,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:910,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:909,columnNumber:9},void 0),R&&p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxDEV("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[p.jsxDEV("div",{className:"flex items-center mb-4",children:[p.jsxDEV(A,{className:"text-yellow-500 text-xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1006,columnNumber:15},void 0),p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"pins"===H?"Eliminar Pines":"Eliminar Asignaciones Completas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1007,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1005,columnNumber:13},void 0),p.jsxDEV("div",{className:"mb-4",children:[p.jsxDEV("p",{className:"text-gray-600 mb-3",children:"pins"===H?`¿Estás seguro de que deseas eliminar pines de ${T.length} psicólogo(s) seleccionado(s)?`:`¿Estás seguro de que deseas eliminar TODAS las asignaciones de pines de ${T.length} psicólogo(s) seleccionado(s)? Esta acción no se puede deshacer.`},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1013,columnNumber:15},void 0),"pins"===H&&p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Cantidad de pines a eliminar:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1022,columnNumber:19},void 0),p.jsxDEV("input",{type:"number",value:W,onChange:e=>Y(parseInt(e.target.value)||1),min:"1",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Cantidad de pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1025,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1021,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1012,columnNumber:13},void 0),p.jsxDEV("div",{className:"flex justify-end space-x-3",children:[p.jsxDEV("button",{onClick:()=>{G(!1),Y(1)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1038,columnNumber:15},void 0),p.jsxDEV("button",{onClick:()=>m(null,null,function*(){if(0===T.length)return void q.error("Selecciona al menos un psicólogo");if("pins"===H&&(!W||W<=0))return void q.error("Ingresa una cantidad válida de pines a eliminar");const e="complete"===H?`¿Estás seguro de eliminar COMPLETAMENTE todas las asignaciones de pines de ${T.length} psicólogo(s)? Esta acción no se puede deshacer.`:`¿Estás seguro de eliminar ${W} pines de ${T.length} psicólogo(s)?`;if(!window.confirm(e))return;const i=q.loading("complete"===H?"Eliminando asignaciones completas...":`Eliminando ${W} pines...`);try{let e;if("complete"===H)e=1===T.length?yield J.removePsychologistPinAssignment(T[0],"Eliminación completa desde panel de control"):yield J.removeMultiplePsychologistAssignments(T,"Eliminación masiva desde panel de control");else{const i=T.map(e=>J.removePinsFromPsychologist(e,W,`Eliminación de ${W} pines desde panel de control`)),a=(yield Promise.all(i)).filter(e=>e.success).length;e={success:a>0,deletedCount:a,totalRequested:T.length}}if(e.success){const a="complete"===H?`Asignaciones eliminadas exitosamente para ${e.deletedCount||T.length} psicólogo(s)`:`${W} pines eliminados exitosamente de ${e.deletedCount||T.length} psicólogo(s)`;q.update(i,{render:a,type:"success",isLoading:!1,autoClose:3e3}),O([]),z(!1),G(!1),Y(1),yield ie()}else q.update(i,{render:`Error: ${e.message||"No se pudieron procesar las eliminaciones"}`,type:"error",isLoading:!1,autoClose:5e3})}catch(a){q.update(i,{render:"Error inesperado al procesar la eliminación",type:"error",isLoading:!1,autoClose:5e3})}}),disabled:o||"pins"===H&&W<1,className:`px-4 py-2 text-white rounded-lg transition-colors ${"pins"===H?"bg-orange-600 hover:bg-orange-700":"bg-red-600 hover:bg-red-700"} disabled:opacity-50 disabled:cursor-not-allowed`,children:o?"Procesando...":"pins"===H?"Eliminar Pines":"Eliminar Asignaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1047,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1037,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1004,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1003,columnNumber:9},void 0),Z&&p.jsxDEV("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxDEV("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[p.jsxDEV("div",{className:"flex items-center mb-4",children:[p.jsxDEV(x,{className:"text-red-500 text-xl mr-3"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1068,columnNumber:15},void 0),p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Eliminar Asignaciones de Pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1069,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1067,columnNumber:13},void 0),p.jsxDEV("div",{className:"mb-4",children:[p.jsxDEV("p",{className:"text-gray-600 mb-3",children:["¿Estás seguro de que deseas eliminar las asignaciones de pines de ",T.length," psicólogo(s)?"]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1075,columnNumber:15},void 0),p.jsxDEV("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[p.jsxDEV("p",{className:"text-red-800 text-sm font-medium mb-2",children:"⚠️ ADVERTENCIA: Esta acción eliminará:"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1079,columnNumber:17},void 0),p.jsxDEV("ul",{className:"text-red-700 text-sm list-disc list-inside space-y-1",children:[p.jsxDEV("li",{children:"Todas las asignaciones de pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1081,columnNumber:19},void 0),p.jsxDEV("li",{children:"Todo el historial de transacciones de pines"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1082,columnNumber:19},void 0),p.jsxDEV("li",{children:"Los pines restantes del psicólogo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1083,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1080,columnNumber:17},void 0),p.jsxDEV("p",{className:"text-red-800 text-sm font-bold mt-2",children:"Los datos del psicólogo se mantendrán. Esta acción NO se puede deshacer."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1085,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1078,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1074,columnNumber:13},void 0),p.jsxDEV("div",{className:"flex justify-end space-x-3",children:[p.jsxDEV("button",{onClick:()=>Q(!1),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Cancelar"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1090,columnNumber:15},void 0),p.jsxDEV("button",{onClick:()=>m(null,null,function*(){if(0===T.length)return void q.error("Selecciona al menos un psicólogo");const e=`¿Estás seguro de eliminar las asignaciones de pines de ${T.length} psicólogo(s)? Esta acción eliminará todas sus transacciones de pines pero mantendrá sus datos de usuario. Esta acción no se puede deshacer.`;if(!window.confirm(e))return;const i=q.loading(`Eliminando asignaciones de pines de ${T.length} psicólogo(s)...`);try{let e;if(e=1===T.length?yield a.removePsychologistPinAssignment(T[0],"Eliminación desde panel de control"):yield a.removeMultiplePsychologistAssignments(T,"Eliminación múltiple desde panel de control"),e.success){const a=1===T.length?`Asignaciones de pines eliminadas exitosamente para ${e.psychologist_name||"el psicólogo"}`:`Asignaciones eliminadas: ${e.successCount} exitosos${e.errorCount>0?`, ${e.errorCount} con errores`:""}`;q.update(i,{render:a,type:e.errorCount>0?"warning":"success",isLoading:!1,autoClose:5e3}),e.errors&&e.errors.length>0&&e.errors.forEach(e=>{q.error(`Error al eliminar asignaciones del psicólogo ${e.psychologist_id}: ${e.error}`,{autoClose:8e3})})}else q.update(i,{render:`Error: ${e.message}`,type:"error",isLoading:!1,autoClose:5e3});O([]),z(!1),Q(!1),yield ie()}catch(n){q.update(i,{render:"Error inesperado al eliminar asignaciones de pines",type:"error",isLoading:!1,autoClose:5e3})}}),disabled:o,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Eliminando...":"Eliminar Asignaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1096,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1089,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1066,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1065,columnNumber:9},void 0),p.jsxDEV(ne,{isOpen:P,onClose:()=>E(!1),onSuccess:ie},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:1109,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/admin/UsageControlPanel.jsx",lineNumber:562,columnNumber:5},void 0)},oe=()=>{const[e,i]=F.useState("profile"),[a,n]=F.useState({nombre:"Usuario Demo",apellido:"Sistema",email:"<EMAIL>",documento:"12345678"}),[s,o]=F.useState({emailNotifications:!0,pushNotifications:!1,weeklyReports:!0}),[r,m]=F.useState({twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90"}),c=[{id:"profile",name:"Perfil",icon:P,description:"Información personal"},{id:"notifications",name:"Notificaciones",icon:T,description:"Preferencias de notificaciones"},{id:"security",name:"Seguridad",icon:O,description:"Configuración de seguridad"}],d=(e,i)=>{n(a=>t(l({},a),{[e]:i}))},u=(e,i)=>{o(a=>t(l({},a),{[e]:i}))},b=(e,i)=>{m(a=>t(l({},a),{[e]:i}))},g=()=>p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Información Personal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:75,columnNumber:7},void 0),p.jsxDEV("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nombre"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:79,columnNumber:11},void 0),p.jsxDEV("div",{className:"relative",children:[p.jsxDEV(P,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:83,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",value:a.nombre,onChange:e=>d("nombre",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:84,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:82,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:78,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Apellido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:94,columnNumber:11},void 0),p.jsxDEV("div",{className:"relative",children:[p.jsxDEV(P,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:98,columnNumber:13},void 0),p.jsxDEV("input",{type:"text",value:a.apellido,onChange:e=>d("apellido",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:99,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:97,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:93,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:109,columnNumber:11},void 0),p.jsxDEV("div",{className:"relative",children:[p.jsxDEV(z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:113,columnNumber:13},void 0),p.jsxDEV("input",{type:"email",value:a.email,onChange:e=>d("email",e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:114,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:112,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:108,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Documento"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:124,columnNumber:11},void 0),p.jsxDEV("input",{type:"text",value:a.documento,onChange:e=>d("documento",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:127,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:123,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:77,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:74,columnNumber:5},void 0);return p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("div",{className:"text-center mb-6",children:[p.jsxDEV("h2",{className:"text-2xl font-bold text-gray-900",children:"Configuración Personal"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:266,columnNumber:9},void 0),p.jsxDEV("p",{className:"text-gray-600 mt-2",children:"Administra tu perfil y preferencias"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:267,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:265,columnNumber:7},void 0),p.jsxDEV("div",{className:"flex flex-wrap gap-4 mb-8 justify-center",children:c.map(a=>p.jsxDEV("button",{onClick:()=>i(a.id),className:"flex items-center px-6 py-3 rounded-lg font-medium transition-colors "+(e===a.id?"bg-blue-600 text-white shadow-lg":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"),children:[p.jsxDEV(a.icon,{className:"w-5 h-5 mr-2"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:282,columnNumber:13},void 0),a.name]},a.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:273,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:271,columnNumber:7},void 0),p.jsxDEV("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(()=>{switch(e){case"profile":default:return g();case"notifications":return p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Preferencias de Notificaciones"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:140,columnNumber:7},void 0),p.jsxDEV("div",{className:"space-y-4",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones por Email"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:145,columnNumber:13},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500",children:"Recibir notificaciones importantes por correo"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:146,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:144,columnNumber:11},void 0),p.jsxDEV("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsxDEV("input",{type:"checkbox",checked:s.emailNotifications,onChange:e=>u("emailNotifications",e.target.checked),className:"sr-only peer"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:149,columnNumber:13},void 0),p.jsxDEV("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:155,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:148,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:143,columnNumber:9},void 0),p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:"Notificaciones Push"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:161,columnNumber:13},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500",children:"Notificaciones en tiempo real en el navegador"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:162,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:160,columnNumber:11},void 0),p.jsxDEV("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsxDEV("input",{type:"checkbox",checked:s.pushNotifications,onChange:e=>u("pushNotifications",e.target.checked),className:"sr-only peer"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:165,columnNumber:13},void 0),p.jsxDEV("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:171,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:164,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:159,columnNumber:9},void 0),p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:"Reportes Semanales"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:177,columnNumber:13},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500",children:"Resumen semanal de actividad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:178,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:176,columnNumber:11},void 0),p.jsxDEV("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsxDEV("input",{type:"checkbox",checked:s.weeklyReports,onChange:e=>u("weeklyReports",e.target.checked),className:"sr-only peer"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:181,columnNumber:13},void 0),p.jsxDEV("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:187,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:180,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:175,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:142,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:139,columnNumber:5},void 0);case"security":return p.jsxDEV("div",{className:"space-y-6",children:[p.jsxDEV("h3",{className:"text-lg font-semibold text-gray-900",children:"Configuración de Seguridad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:196,columnNumber:7},void 0),p.jsxDEV("div",{className:"space-y-4",children:[p.jsxDEV("div",{className:"flex items-center justify-between",children:[p.jsxDEV("div",{children:[p.jsxDEV("h4",{className:"text-sm font-medium text-gray-900",children:"Autenticación de Dos Factores"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:201,columnNumber:13},void 0),p.jsxDEV("p",{className:"text-sm text-gray-500",children:"Agregar una capa extra de seguridad"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:202,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:200,columnNumber:11},void 0),p.jsxDEV("label",{className:"relative inline-flex items-center cursor-pointer",children:[p.jsxDEV("input",{type:"checkbox",checked:r.twoFactorAuth,onChange:e=>b("twoFactorAuth",e.target.checked),className:"sr-only peer"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:205,columnNumber:13},void 0),p.jsxDEV("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:211,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:204,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:199,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tiempo de Sesión (minutos)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:216,columnNumber:11},void 0),p.jsxDEV("select",{value:r.sessionTimeout,onChange:e=>b("sessionTimeout",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsxDEV("option",{value:"15",children:"15 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:224,columnNumber:13},void 0),p.jsxDEV("option",{value:"30",children:"30 minutos"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:225,columnNumber:13},void 0),p.jsxDEV("option",{value:"60",children:"1 hora"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:226,columnNumber:13},void 0),p.jsxDEV("option",{value:"120",children:"2 horas"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:227,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:219,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:215,columnNumber:9},void 0),p.jsxDEV("div",{children:[p.jsxDEV("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expiración de Contraseña (días)"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:232,columnNumber:11},void 0),p.jsxDEV("select",{value:r.passwordExpiry,onChange:e=>b("passwordExpiry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[p.jsxDEV("option",{value:"30",children:"30 días"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:240,columnNumber:13},void 0),p.jsxDEV("option",{value:"60",children:"60 días"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:241,columnNumber:13},void 0),p.jsxDEV("option",{value:"90",children:"90 días"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:242,columnNumber:13},void 0),p.jsxDEV("option",{value:"180",children:"180 días"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:243,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:235,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:231,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:198,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:195,columnNumber:5},void 0)}})(),p.jsxDEV("div",{className:"mt-8 pt-6 border-t border-gray-200",children:p.jsxDEV("button",{onClick:()=>{alert("Configuración guardada correctamente")},className:"w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2 transition-colors",children:[p.jsxDEV(I,{className:"w-5 h-5"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:298,columnNumber:13},void 0),p.jsxDEV("span",{children:"Guardar Cambios"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:299,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:294,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:293,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:289,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/components/settings/SimpleUserSettings.jsx",lineNumber:264,columnNumber:5},void 0)},re=()=>{var e;const{user:i,userRole:a}=R(),[n,s]=F.useState("dashboard"),o="administrador"===a||"administrador"===(null==i?void 0:i.tipo_usuario),r=[{id:"dashboard",name:"Dashboard",icon:d,component:Z,adminOnly:!0,description:"Resumen general del sistema"},{id:"users",name:"Gestión de Usuarios",icon:c,component:Q,adminOnly:!0,description:"Administra usuarios del sistema"},{id:"access",name:"Control de Acceso",icon:O,component:ie,adminOnly:!0,description:"Gestiona permisos de acceso a páginas"},{id:"assignments",name:"Asignación de Pacientes",icon:C,component:ae,adminOnly:!0,description:"Asigna pacientes a psicólogos"},{id:"usage",name:"Control de Pines",icon:_,component:se,adminOnly:!0,description:"Gestiona y monitorea el uso de pines por psicólogo"},{id:"settings",name:"Configuración Personal",icon:G,component:oe,adminOnly:!1,description:"Configuración personal y preferencias"}],l=F.useMemo(()=>r.filter(e=>!e.adminOnly||o),[o]);F.useEffect(()=>{l.length>0&&!l.some(e=>e.id===n)&&s(l[0].id)},[l,n]);return o||0!==l.length?p.jsxDEV("div",{className:"min-h-screen bg-gray-50",children:[p.jsxDEV(H,{title:"Configuración del Sistema",subtitle:"Gestiona usuarios, configuraciones y estadísticas del sistema",icon:G},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:122,columnNumber:7},void 0),p.jsxDEV("div",{className:"container mx-auto px-4 py-8",children:[p.jsxDEV("div",{className:"bg-white rounded-lg shadow-md mb-6",children:[p.jsxDEV("div",{className:"border-b border-gray-200",children:p.jsxDEV("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:l.map(e=>{const i=e.icon,a=n===e.id;return p.jsxDEV("button",{onClick:()=>s(e.id),className:`\n                      flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200\n                      ${a?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                    `,"aria-current":a?"page":void 0,children:[p.jsxDEV(i,{className:"w-5 h-5 "+(a?"text-blue-600":"text-gray-400")},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:151,columnNumber:21},void 0),p.jsxDEV("span",{children:e.name},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:152,columnNumber:21},void 0)]},e.id,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:139,columnNumber:19},void 0)})},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:133,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:132,columnNumber:11},void 0),p.jsxDEV("div",{className:"px-6 py-4 bg-gray-50 border-b border-gray-200",children:p.jsxDEV("p",{className:"text-sm text-gray-600",children:null==(e=l.find(e=>e.id===n))?void 0:e.description},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:161,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:160,columnNumber:11},void 0),p.jsxDEV("div",{className:"p-6",children:(()=>{const e=l.find(e=>e.id===n);if(!e)return null;const i=e.component;return p.jsxDEV(i,{},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:102,columnNumber:12},void 0)})()},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:167,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:131,columnNumber:9},void 0),p.jsxDEV("div",{className:"text-center text-sm text-gray-500",children:p.jsxDEV("p",{children:["Sistema de Administración BAT-7 • Usuario: ",null==i?void 0:i.nombre," ",null==i?void 0:i.apellido," • Rol: ",a||(null==i?void 0:i.tipo_usuario)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:174,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:173,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:128,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:120,columnNumber:5},void 0):p.jsxDEV("div",{className:"container mx-auto px-4 py-8",children:p.jsxDEV("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:p.jsxDEV("div",{className:"text-yellow-800",children:[p.jsxDEV("h3",{className:"text-lg font-medium",children:"Acceso Restringido"},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:111,columnNumber:13},void 0),p.jsxDEV("p",{className:"mt-2",children:"No tienes permisos para acceder a esta sección."},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:112,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:110,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:109,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Bat-7 Version fial para github/src/pages/Configuracion/Configuracion.jsx",lineNumber:108,columnNumber:7},void 0)};export{re as default};
