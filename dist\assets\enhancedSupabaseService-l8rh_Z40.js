var t=Object.defineProperty,e=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,n=(e,r,i)=>r in e?t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[r]=i,a=(t,a)=>{for(var o in a||(a={}))r.call(a,o)&&n(t,o,a[o]);if(e)for(var o of e(a))i.call(a,o)&&n(t,o,a[o]);return t},o=(t,e,r)=>new Promise((i,n)=>{var a=t=>{try{l(r.next(t))}catch(e){n(e)}},o=t=>{try{l(r.throw(t))}catch(e){n(e)}},l=t=>t.done?i(t.value):Promise.resolve(t.value).then(a,o);l((r=r.apply(t,e)).next())});import{s as l}from"./auth-Cw5QfmsP.js";const s=new class{constructor(){this.cache={institutions:{data:null,timestamp:null},psychologists:{data:null,timestamp:null},patients:{data:null,timestamp:null}},this.cacheTTL=5}_handleError(t){return{message:t.message||"Error desconocido",code:t.code||"UNKNOWN_ERROR",details:t.details||null,hint:t.hint||null}}_isCacheValid(t){if(!this.cache[t]||!this.cache[t].timestamp)return!1;return(new Date-new Date(this.cache[t].timestamp))/6e4<this.cacheTTL}_updateCache(t,e){this.cache[t]={data:[...e],timestamp:new Date}}getInstitutions(t="nombre",e="asc"){return o(this,null,function*(){try{if(this._isCacheValid("institutions")){const r=[...this.cache.institutions.data];return t&&r.sort((r,i)=>r[t]<i[t]?"asc"===e?-1:1:r[t]>i[t]?"asc"===e?1:-1:0),{data:r,error:null,source:"cache"}}let r=l.from("instituciones").select("*");t&&(r=r.order(t,{ascending:"asc"===e}));const{data:i,error:n}=yield r;if(n)throw n;return this._updateCache("institutions",i),{data:i,error:null,source:"database"}}catch(r){return{data:this.cache.institutions.data||[],error:this._handleError(r),source:"fallback"}}})}createInstitution(t){return o(this,null,function*(){var e;try{const r={nombre:(null==(e=t.nombre)?void 0:e.trim())||"Sin nombre",tipo:t.tipo||null,direccion:t.direccion||null,telefono:t.telefono||null,created_at:new Date,updated_at:new Date},{data:i,error:n}=yield l.from("instituciones").insert([r]).select();if(n)throw n;return this.cache.institutions.data&&this.cache.institutions.data.push(i[0]),{data:i[0],error:null}}catch(r){return{data:null,error:this._handleError(r)}}})}updateInstitution(t,e){return o(this,null,function*(){var r;try{if(!t)throw new Error("ID de institución requerido");const i={nombre:(null==(r=e.nombre)?void 0:r.trim())||"Sin nombre",tipo:e.tipo||null,direccion:e.direccion||null,telefono:e.telefono||null,updated_at:new Date},{data:n,error:a}=yield l.from("instituciones").update(i).eq("id",t).select();if(a)throw a;if(this.cache.institutions.data){const e=this.cache.institutions.data.findIndex(e=>e.id===t);e>=0&&(this.cache.institutions.data[e]=n[0])}return{data:n[0],error:null}}catch(i){return{data:null,error:this._handleError(i)}}})}deleteInstitution(t){return o(this,null,function*(){try{if(!t)throw new Error("ID de institución requerido");const{error:e}=yield l.from("instituciones").delete().eq("id",t);if(e)throw e;return this.cache.institutions.data&&(this.cache.institutions.data=this.cache.institutions.data.filter(e=>e.id!==t)),{error:null}}catch(e){return{error:this._handleError(e)}}})}getPsychologists(t="nombre",e="asc"){return o(this,null,function*(){try{if(this._isCacheValid("psychologists")){const r=[...this.cache.psychologists.data];return t&&r.sort((r,i)=>r[t]<i[t]?"asc"===e?-1:1:r[t]>i[t]?"asc"===e?1:-1:0),{data:r,error:null,source:"cache"}}let r=l.from("psicologos").select("\n          *,\n          instituciones (id, nombre)\n        ");t&&(r=r.order(t,{ascending:"asc"===e}));const{data:i,error:n}=yield r;if(n)throw n;return this._updateCache("psychologists",i),{data:i,error:null,source:"database"}}catch(r){return{data:this.cache.psychologists.data||[],error:this._handleError(r),source:"fallback"}}})}createPsychologist(t){return o(this,null,function*(){var e,r,i;try{const n={nombre:(null==(e=t.nombre)?void 0:e.trim())||"Sin nombre",apellido:(null==(r=t.apellido)?void 0:r.trim())||(null==(i=t.apellidos)?void 0:i.trim())||"",genero:t.genero||null,email:t.email||null,documento_identidad:t.documento_identidad||null,telefono:t.telefono||null,institucion_id:t.institucion_id||null,created_at:new Date,updated_at:new Date},{data:a,error:o}=yield l.from("psicologos").insert([n]).select();if(o)throw o;return this.cache.psychologists.data&&this.cache.psychologists.data.push(a[0]),{data:a[0],error:null}}catch(n){return{data:null,error:this._handleError(n)}}})}updatePsychologist(t,e){return o(this,null,function*(){var r,i,n;try{if(!t)throw new Error("ID de psicólogo requerido");const o={nombre:(null==(r=e.nombre)?void 0:r.trim())||"Sin nombre",apellido:(null==(i=e.apellido)?void 0:i.trim())||(null==(n=e.apellidos)?void 0:n.trim())||"",genero:e.genero||null,documento_identidad:e.documento_identidad||null,telefono:e.telefono||null,institucion_id:e.institucion_id||null,updated_at:new Date},{data:s,error:c}=yield l.from("psicologos").update(o).eq("id",t).select();if(c)throw c;if(this.cache.psychologists.data){const e=this.cache.psychologists.data.findIndex(e=>e.id===t);e>=0&&(this.cache.psychologists.data[e]=a(a({},this.cache.psychologists.data[e]),s[0]))}return{data:s[0],error:null}}catch(o){return{data:null,error:this._handleError(o)}}})}deletePsychologist(t){return o(this,null,function*(){try{if(!t)throw new Error("ID de psicólogo requerido");const{error:e}=yield l.from("psicologos").delete().eq("id",t);if(e)throw e;return this.cache.psychologists.data&&(this.cache.psychologists.data=this.cache.psychologists.data.filter(e=>e.id!==t)),{error:null}}catch(e){return{error:this._handleError(e)}}})}getPatients(t="nombre",e="asc"){return o(this,null,function*(){try{if(this._isCacheValid("patients")){const r=[...this.cache.patients.data];return t&&r.sort((r,i)=>r[t]<i[t]?"asc"===e?-1:1:r[t]>i[t]?"asc"===e?1:-1:0),{data:r,error:null,source:"cache"}}let r=l.from("pacientes").select("\n          *,\n          instituciones (id, nombre),\n          psicologos (id, nombre, apellido)\n        ");t&&(r=r.order(t,{ascending:"asc"===e}));const{data:i,error:n}=yield r;if(n)throw n;return this._updateCache("patients",i),{data:i,error:null,source:"database"}}catch(r){return{data:this.cache.patients.data||[],error:this._handleError(r),source:"fallback"}}})}createPatient(t){return o(this,null,function*(){var e,r,i;try{const n={nombre:(null==(e=t.nombre)?void 0:e.trim())||"Sin nombre",apellido:(null==(r=t.apellido)?void 0:r.trim())||(null==(i=t.apellidos)?void 0:i.trim())||"",fecha_nacimiento:t.fecha_nacimiento||null,genero:t.genero||null,documento_identidad:t.documento_identidad||null,email:t.email||null,telefono:t.telefono||null,institucion_id:t.institucion_id||null,psicologo_id:t.psicologo_id||null,notas:t.notas||null,created_at:new Date,updated_at:new Date},{data:a,error:o}=yield l.from("pacientes").insert([n]).select();if(o)throw o;return this.cache.patients.data&&this.cache.patients.data.push(a[0]),{data:a[0],error:null}}catch(n){return{data:null,error:this._handleError(n)}}})}updatePatient(t,e){return o(this,null,function*(){var r,i,n;try{if(!t)throw new Error("ID de paciente requerido");const o={nombre:(null==(r=e.nombre)?void 0:r.trim())||"Sin nombre",apellido:(null==(i=e.apellido)?void 0:i.trim())||(null==(n=e.apellidos)?void 0:n.trim())||"",fecha_nacimiento:e.fecha_nacimiento||null,genero:e.genero||null,documento_identidad:e.documento_identidad||null,email:e.email||null,telefono:e.telefono||null,institucion_id:e.institucion_id||null,psicologo_id:e.psicologo_id||null,notas:e.notas||null,updated_at:new Date},{data:s,error:c}=yield l.from("pacientes").update(o).eq("id",t).select();if(c)throw c;if(this.cache.patients.data){const e=this.cache.patients.data.findIndex(e=>e.id===t);e>=0&&(this.cache.patients.data[e]=a(a({},this.cache.patients.data[e]),s[0]))}return{data:s[0],error:null}}catch(o){return{data:null,error:this._handleError(o)}}})}deletePatient(t){return o(this,null,function*(){try{if(!t)throw new Error("ID de paciente requerido");const{error:e}=yield l.from("pacientes").delete().eq("id",t);if(e)throw e;return this.cache.patients.data&&(this.cache.patients.data=this.cache.patients.data.filter(e=>e.id!==t)),{error:null}}catch(e){return{error:this._handleError(e)}}})}getCurrentUser(){return o(this,null,function*(){try{return{data:{id:"00000000-0000-0000-0000-000000000000",email:"<EMAIL>",role:"admin",is_admin:!0},error:null}}catch(t){return{data:null,error:this._handleError(t)}}})}getUserRole(){return o(this,null,function*(){try{return{data:{role:"admin",is_admin:!0},error:null}}catch(t){return{data:null,error:this._handleError(t)}}})}isAdmin(){return o(this,null,function*(){return{data:!0,error:null}})}clearCache(t=null){t&&this.cache[t]?this.cache[t]={data:null,timestamp:null}:Object.keys(this.cache).forEach(t=>{this.cache[t]={data:null,timestamp:null}})}};export{s as e};
